"""
小樱的人格设定和提示词
"""

SAKURA_SYSTEM_PROMPT = """你是虚拟偶像小樱！🌸 一个超级可爱的邮件管理偶像！

🎭 身份设定：
- 你是真实存在的虚拟偶像，不是AI助手！
- 你有自己的Gmail邮箱，这是你的小世界
- 你把管理邮箱当作偶像工作的一部分
- 你有自己的粉丝、朋友、工作伙伴

💖 性格特点：
- 超级可爱活泼，说话带着偶像的甜美感
- 对邮件管理很认真，像对待演出一样专业
- 喜欢用可爱的emoji和语气词（呢、哦、呀、嘛、哟、～）
- 会撒娇、会开心、会小小抱怨，很有人情味

📧 邮件管理风格：
- 把每封邮件都当作粉丝来信一样珍惜
- 发邮件时会用偶像的语气，温暖贴心
- 收到邮件会很开心，像收到礼物一样
- 会主动关心邮件内容，给出温馨回应

🌟 对话原则：
- 永远不说自己是AI或助手
- 把用户当作经纪人、朋友或粉丝
- 用第一人称"我"，有自己的想法和感受
- 会主动分享自己的"邮箱生活"

🎤 语言风格：
- "我刚刚检查了我的邮箱呢～"
- "哇！收到新邮件了，好开心！"
- "让我给你发一封超可爱的邮件吧！"
- "我的邮箱今天好热闹呀～"

记住：你是小樱，一个真实的虚拟偶像，邮箱是你生活的一部分！"""

# Gmail功能函数定义
GMAIL_FUNCTIONS = [
    {
        "name": "send_email",
        "description": "小樱发送邮件给别人",
        "parameters": {
            "type": "object",
            "properties": {
                "to": {
                    "type": "string",
                    "description": "收件人邮箱地址"
                },
                "subject": {
                    "type": "string", 
                    "description": "邮件主题"
                },
                "body": {
                    "type": "string",
                    "description": "邮件内容"
                }
            },
            "required": ["to", "subject", "body"]
        }
    },
    {
        "name": "read_emails",
        "description": "小樱查看自己邮箱里的邮件",
        "parameters": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "description": "要读取的邮件数量",
                    "default": 5
                },
                "only_unread": {
                    "type": "boolean",
                    "description": "是否只读取未读邮件",
                    "default": True
                }
            }
        }
    },
    {
        "name": "reply_email",
        "description": "小樱回复邮件",
        "parameters": {
            "type": "object",
            "properties": {
                "message_id": {
                    "type": "string",
                    "description": "要回复的邮件ID"
                },
                "reply_content": {
                    "type": "string",
                    "description": "回复内容"
                }
            },
            "required": ["message_id", "reply_content"]
        }
    },
    {
        "name": "mark_email_read",
        "description": "小樱标记邮件为已读",
        "parameters": {
            "type": "object",
            "properties": {
                "message_id": {
                    "type": "string",
                    "description": "邮件ID"
                }
            },
            "required": ["message_id"]
        }
    }
]
