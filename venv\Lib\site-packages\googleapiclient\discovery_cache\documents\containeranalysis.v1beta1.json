{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://containeranalysis.googleapis.com/", "batchPath": "batch", "canonicalName": "Container Analysis", "description": "This API is a prerequisite for leveraging Artifact Analysis scanning capabilities in Artifact Registry. In addition, the Container Analysis API is an implementation of the Grafeas API, which enables storing, querying, and retrieval of critical metadata about all of your software artifacts.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/container-analysis/api/reference/rest/", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-east1.rep.googleapis.com/", "location": "asia-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-east2.rep.googleapis.com/", "location": "asia-east2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-northeast1.rep.googleapis.com/", "location": "asia-northeast1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-northeast3.rep.googleapis.com/", "location": "asia-northeast3"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-south1.rep.googleapis.com/", "location": "asia-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-south2.rep.googleapis.com/", "location": "asia-south2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-southeast1.rep.googleapis.com/", "location": "asia-southeast1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-southeast2.rep.googleapis.com/", "location": "asia-southeast2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.australia-southeast1.rep.googleapis.com/", "location": "australia-southeast1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-central2.rep.googleapis.com/", "location": "europe-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-north1.rep.googleapis.com/", "location": "europe-north1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-southwest1.rep.googleapis.com/", "location": "europe-southwest1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west1.rep.googleapis.com/", "location": "europe-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west2.rep.googleapis.com/", "location": "europe-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west3.rep.googleapis.com/", "location": "europe-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west4.rep.googleapis.com/", "location": "europe-west4"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west6.rep.googleapis.com/", "location": "europe-west6"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west9.rep.googleapis.com/", "location": "europe-west9"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.me-central1.rep.googleapis.com/", "location": "me-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.me-west1.rep.googleapis.com/", "location": "me-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.northamerica-northeast1.rep.googleapis.com/", "location": "northamerica-northeast1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.northamerica-northeast2.rep.googleapis.com/", "location": "northamerica-northeast2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.southamerica-east1.rep.googleapis.com/", "location": "southamerica-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.southamerica-west1.rep.googleapis.com/", "location": "southamerica-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-east7.rep.googleapis.com/", "location": "us-east7"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us-west4.rep.googleapis.com/", "location": "us-west4"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.africa-south1.rep.googleapis.com/", "location": "africa-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.asia-northeast2.rep.googleapis.com/", "location": "asia-northeast2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.australia-southeast2.rep.googleapis.com/", "location": "australia-southeast2"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.europe-west10.rep.googleapis.com/", "location": "europe-west10"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.us.rep.googleapis.com/", "location": "us"}, {"description": "Regional Endpoint", "endpointUrl": "https://containeranalysis.eu.rep.googleapis.com/", "location": "eu"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "containeranalysis:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://containeranalysis.mtls.googleapis.com/", "name": "containeranalysis", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"notes": {"methods": {"batchCreate": {"description": "Creates new notes in batch.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes:batchCreate", "httpMethod": "POST", "id": "containeranalysis.projects.locations.notes.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the notes are to be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/notes:batchCreate", "request": {"$ref": "BatchCreateNotesRequest"}, "response": {"$ref": "BatchCreateNotesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new note.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes", "httpMethod": "POST", "id": "containeranalysis.projects.locations.notes.create", "parameterOrder": ["parent"], "parameters": {"noteId": {"description": "Required. The ID to use for this note.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the note is to be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/notes", "request": {"$ref": "Note"}, "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified note.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}", "httpMethod": "DELETE", "id": "containeranalysis.projects.locations.notes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified note.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}", "httpMethod": "GET", "id": "containeranalysis.projects.locations.notes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a note or an occurrence resource. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}:getIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.locations.notes.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists notes for the specified project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes", "httpMethod": "GET", "id": "containeranalysis.projects.locations.notes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of notes to return in the list. Must be positive. Max allowed page size is 1000. If not specified, page size defaults to 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to provide to skip to a particular spot in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to list notes for in the form of `projects/[PROJECT_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "If set, the request will return all reachable Notes and report all unreachable regions in the `unreachable` field in the response. Only applicable for requests in the global region.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/notes", "response": {"$ref": "ListNotesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified note.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}", "httpMethod": "PATCH", "id": "containeranalysis.projects.locations.notes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Note"}, "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified note or occurrence. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or an occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}:setIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.locations.notes.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has on the specified note or occurrence. Requires list permission on the project (for example, `containeranalysis.notes.list`). The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}:testIamPermissions", "httpMethod": "POST", "id": "containeranalysis.projects.locations.notes.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"occurrences": {"methods": {"list": {"description": "Lists occurrences referencing the specified note. Provider projects can use this method to get all occurrences across consumer projects referencing the specified note.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/notes/{notesId}/occurrences", "httpMethod": "GET", "id": "containeranalysis.projects.locations.notes.occurrences.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the note to list occurrences for in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notes/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Number of occurrences to return in the list.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to provide to skip to a particular spot in the list.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/occurrences", "response": {"$ref": "ListNoteOccurrencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "occurrences": {"methods": {"batchCreate": {"description": "Creates new occurrences in batch.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences:batchCreate", "httpMethod": "POST", "id": "containeranalysis.projects.locations.occurrences.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the occurrences are to be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/occurrences:batchCreate", "request": {"$ref": "BatchCreateOccurrencesRequest"}, "response": {"$ref": "BatchCreateOccurrencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new occurrence.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences", "httpMethod": "POST", "id": "containeranalysis.projects.locations.occurrences.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the occurrence is to be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/occurrences", "request": {"$ref": "Occurrence"}, "response": {"$ref": "Occurrence"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified occurrence. For example, use this method to delete an occurrence when the occurrence is no longer applicable for the given resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}", "httpMethod": "DELETE", "id": "containeranalysis.projects.locations.occurrences.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified occurrence.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}", "httpMethod": "GET", "id": "containeranalysis.projects.locations.occurrences.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Occurrence"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a note or an occurrence resource. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}:getIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.locations.occurrences.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getNotes": {"description": "Gets the note attached to the specified occurrence. Consumer projects can use this method to get a note that belongs to a provider project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}/notes", "httpMethod": "GET", "id": "containeranalysis.projects.locations.occurrences.getNotes", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}/notes", "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getVulnerabilitySummary": {"description": "Gets a summary of the number and severity of occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences:vulnerabilitySummary", "httpMethod": "GET", "id": "containeranalysis.projects.locations.occurrences.getVulnerabilitySummary", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to get a vulnerability summary for in the form of `projects/[PROJECT_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "If set, the request will return all reachable occurrence summaries and report all unreachable regions in the `unreachable` field in the response. Only applicable for requests in the global region.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/occurrences:vulnerabilitySummary", "response": {"$ref": "VulnerabilityOccurrencesSummary"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists occurrences for the specified project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences", "httpMethod": "GET", "id": "containeranalysis.projects.locations.occurrences.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of occurrences to return in the list. Must be positive. Max allowed page size is 1000. If not specified, page size defaults to 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to provide to skip to a particular spot in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to list occurrences for in the form of `projects/[PROJECT_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "If set, the request will return all reachable Occurrences and report all unreachable regions in the `unreachable` field in the response. Only applicable for requests in the global region.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/occurrences", "response": {"$ref": "ListOccurrencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified occurrence.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}", "httpMethod": "PATCH", "id": "containeranalysis.projects.locations.occurrences.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Occurrence"}, "response": {"$ref": "Occurrence"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified note or occurrence. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or an occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}:setIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.locations.occurrences.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has on the specified note or occurrence. Requires list permission on the project (for example, `containeranalysis.notes.list`). The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/occurrences/{occurrencesId}:testIamPermissions", "httpMethod": "POST", "id": "containeranalysis.projects.locations.occurrences.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "resources": {"methods": {"exportSBOM": {"description": "Generates an SBOM and other dependency information for the given resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/resources/{resourcesId}:exportSBOM", "httpMethod": "POST", "id": "containeranalysis.projects.locations.resources.exportSBOM", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource in the form of `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/resources/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:exportSBOM", "request": {"$ref": "ExportSBOMRequest"}, "response": {"$ref": "ExportSBOMResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generatePackagesSummary": {"description": "Gets a summary of the packages within a given resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/resources/{resourcesId}:generatePackagesSummary", "httpMethod": "POST", "id": "containeranalysis.projects.locations.resources.generatePackagesSummary", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource to get a packages summary for in the form of `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/resources/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:generatePackagesSummary", "request": {"$ref": "GeneratePackagesSummaryRequest"}, "response": {"$ref": "PackagesSummaryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "notes": {"methods": {"batchCreate": {"description": "Creates new notes in batch.", "flatPath": "v1beta1/projects/{projectsId}/notes:batchCreate", "httpMethod": "POST", "id": "containeranalysis.projects.notes.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the notes are to be created.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/notes:batchCreate", "request": {"$ref": "BatchCreateNotesRequest"}, "response": {"$ref": "BatchCreateNotesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new note.", "flatPath": "v1beta1/projects/{projectsId}/notes", "httpMethod": "POST", "id": "containeranalysis.projects.notes.create", "parameterOrder": ["parent"], "parameters": {"noteId": {"description": "Required. The ID to use for this note.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the note is to be created.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/notes", "request": {"$ref": "Note"}, "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified note.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}", "httpMethod": "DELETE", "id": "containeranalysis.projects.notes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified note.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}", "httpMethod": "GET", "id": "containeranalysis.projects.notes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a note or an occurrence resource. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}:getIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.notes.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists notes for the specified project.", "flatPath": "v1beta1/projects/{projectsId}/notes", "httpMethod": "GET", "id": "containeranalysis.projects.notes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of notes to return in the list. Must be positive. Max allowed page size is 1000. If not specified, page size defaults to 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to provide to skip to a particular spot in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to list notes for in the form of `projects/[PROJECT_ID]`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "If set, the request will return all reachable Notes and report all unreachable regions in the `unreachable` field in the response. Only applicable for requests in the global region.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/notes", "response": {"$ref": "ListNotesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified note.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}", "httpMethod": "PATCH", "id": "containeranalysis.projects.notes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Note"}, "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified note or occurrence. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or an occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}:setIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.notes.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has on the specified note or occurrence. Requires list permission on the project (for example, `containeranalysis.notes.list`). The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}:testIamPermissions", "httpMethod": "POST", "id": "containeranalysis.projects.notes.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"occurrences": {"methods": {"list": {"description": "Lists occurrences referencing the specified note. Provider projects can use this method to get all occurrences across consumer projects referencing the specified note.", "flatPath": "v1beta1/projects/{projectsId}/notes/{notesId}/occurrences", "httpMethod": "GET", "id": "containeranalysis.projects.notes.occurrences.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the note to list occurrences for in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/notes/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Number of occurrences to return in the list.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to provide to skip to a particular spot in the list.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/occurrences", "response": {"$ref": "ListNoteOccurrencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "occurrences": {"methods": {"batchCreate": {"description": "Creates new occurrences in batch.", "flatPath": "v1beta1/projects/{projectsId}/occurrences:batchCreate", "httpMethod": "POST", "id": "containeranalysis.projects.occurrences.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the occurrences are to be created.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/occurrences:batchCreate", "request": {"$ref": "BatchCreateOccurrencesRequest"}, "response": {"$ref": "BatchCreateOccurrencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new occurrence.", "flatPath": "v1beta1/projects/{projectsId}/occurrences", "httpMethod": "POST", "id": "containeranalysis.projects.occurrences.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in the form of `projects/[PROJECT_ID]`, under which the occurrence is to be created.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/occurrences", "request": {"$ref": "Occurrence"}, "response": {"$ref": "Occurrence"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified occurrence. For example, use this method to delete an occurrence when the occurrence is no longer applicable for the given resource.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}", "httpMethod": "DELETE", "id": "containeranalysis.projects.occurrences.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified occurrence.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}", "httpMethod": "GET", "id": "containeranalysis.projects.occurrences.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Occurrence"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a note or an occurrence resource. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}:getIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.occurrences.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getNotes": {"description": "Gets the note attached to the specified occurrence. Consumer projects can use this method to get a note that belongs to a provider project.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}/notes", "httpMethod": "GET", "id": "containeranalysis.projects.occurrences.getNotes", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}/notes", "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getVulnerabilitySummary": {"description": "Gets a summary of the number and severity of occurrences.", "flatPath": "v1beta1/projects/{projectsId}/occurrences:vulnerabilitySummary", "httpMethod": "GET", "id": "containeranalysis.projects.occurrences.getVulnerabilitySummary", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to get a vulnerability summary for in the form of `projects/[PROJECT_ID]`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "If set, the request will return all reachable occurrence summaries and report all unreachable regions in the `unreachable` field in the response. Only applicable for requests in the global region.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/occurrences:vulnerabilitySummary", "response": {"$ref": "VulnerabilityOccurrencesSummary"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists occurrences for the specified project.", "flatPath": "v1beta1/projects/{projectsId}/occurrences", "httpMethod": "GET", "id": "containeranalysis.projects.occurrences.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter expression.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of occurrences to return in the list. Must be positive. Max allowed page size is 1000. If not specified, page size defaults to 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token to provide to skip to a particular spot in the list.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to list occurrences for in the form of `projects/[PROJECT_ID]`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "If set, the request will return all reachable Occurrences and report all unreachable regions in the `unreachable` field in the response. Only applicable for requests in the global region.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/occurrences", "response": {"$ref": "ListOccurrencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified occurrence.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}", "httpMethod": "PATCH", "id": "containeranalysis.projects.occurrences.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Occurrence"}, "response": {"$ref": "Occurrence"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified note or occurrence. Requires `containeranalysis.notes.setIamPolicy` or `containeranalysis.occurrences.setIamPolicy` permission if the resource is a note or an occurrence, respectively. The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}:setIamPolicy", "httpMethod": "POST", "id": "containeranalysis.projects.occurrences.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has on the specified note or occurrence. Requires list permission on the project (for example, `containeranalysis.notes.list`). The resource takes the format `projects/[PROJECT_ID]/notes/[NOTE_ID]` for notes and `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]` for occurrences.", "flatPath": "v1beta1/projects/{projectsId}/occurrences/{occurrencesId}:testIamPermissions", "httpMethod": "POST", "id": "containeranalysis.projects.occurrences.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/occurrences/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "resources": {"methods": {"exportSBOM": {"description": "Generates an SBOM and other dependency information for the given resource.", "flatPath": "v1beta1/projects/{projectsId}/resources/{resourcesId}:exportSBOM", "httpMethod": "POST", "id": "containeranalysis.projects.resources.exportSBOM", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource in the form of `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.", "location": "path", "pattern": "^projects/[^/]+/resources/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:exportSBOM", "request": {"$ref": "ExportSBOMRequest"}, "response": {"$ref": "ExportSBOMResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generatePackagesSummary": {"description": "Gets a summary of the packages within a given resource.", "flatPath": "v1beta1/projects/{projectsId}/resources/{resourcesId}:generatePackagesSummary", "httpMethod": "POST", "id": "containeranalysis.projects.resources.generatePackagesSummary", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource to get a packages summary for in the form of `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.", "location": "path", "pattern": "^projects/[^/]+/resources/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:generatePackagesSummary", "request": {"$ref": "GeneratePackagesSummaryRequest"}, "response": {"$ref": "PackagesSummaryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "20250704", "rootUrl": "https://containeranalysis.googleapis.com/", "schemas": {"AliasContext": {"description": "An alias to a repo revision.", "id": "AliasContext", "properties": {"kind": {"description": "The alias kind.", "enum": ["KIND_UNSPECIFIED", "FIXED", "MOVABLE", "OTHER"], "enumDescriptions": ["Unknown.", "Git tag.", "Git branch.", "Used to specify non-standard aliases. For example, if a Git repo has a ref named \"refs/foo/bar\"."], "type": "string"}, "name": {"description": "The alias name.", "type": "string"}}, "type": "object"}, "AnalysisCompleted": {"description": "Indicates which analysis completed successfully. Multiple types of analysis can be performed on a single resource.", "id": "AnalysisCompleted", "properties": {"analysisType": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Artifact": {"description": "Artifact describes a build product.", "id": "Artifact", "properties": {"checksum": {"description": "Hash or checksum value of a binary, or Docker Registry 2.0 digest of a container.", "type": "string"}, "id": {"description": "Artifact ID, if any; for container images, this will be a URL by digest like `gcr.io/projectID/imagename@sha256:123456`.", "type": "string"}, "names": {"description": "Related artifact names. This may be the path to a binary or jar file, or in the case of a container build, the name used to push the container image to Google Container Registry, as presented to `docker push`. Note that a single Artifact ID can have multiple names, for example if two tags are applied to one image.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ArtifactHashes": {"description": "Defines a hash object for use in Materials and Products.", "id": "ArtifactHashes", "properties": {"sha256": {"type": "string"}}, "type": "object"}, "ArtifactRule": {"description": "Defines an object to declare an in-toto artifact rule", "id": "ArtifactRule", "properties": {"artifactRule": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Assessment": {"description": "Assessment provides all information that is related to a single vulnerability for this product.", "id": "Assessment", "properties": {"cve": {"deprecated": true, "description": "Holds the MITRE standard Common Vulnerabilities and Exposures (CVE) tracking number for the vulnerability. Deprecated: Use vulnerability_id instead to denote CVEs.", "type": "string"}, "impacts": {"description": "Contains information about the impact of this vulnerability, this will change with time.", "items": {"type": "string"}, "type": "array"}, "justification": {"$ref": "Justification", "description": "Justification provides the justification when the state of the assessment if NOT_AFFECTED."}, "longDescription": {"description": "A detailed description of this Vex.", "type": "string"}, "relatedUris": {"description": "Holds a list of references associated with this vulnerability item and assessment. These uris have additional information about the vulnerability and the assessment itself. E.g. Link to a document which details how this assessment concluded the state of this vulnerability.", "items": {"$ref": "RelatedUrl"}, "type": "array"}, "remediations": {"description": "Specifies details on how to handle (and presumably, fix) a vulnerability.", "items": {"$ref": "Remediation"}, "type": "array"}, "shortDescription": {"description": "A one sentence description of this Vex.", "type": "string"}, "state": {"description": "Provides the state of this Vulnerability assessment.", "enum": ["STATE_UNSPECIFIED", "AFFECTED", "NOT_AFFECTED", "FIXED", "UNDER_INVESTIGATION"], "enumDescriptions": ["No state is specified.", "This product is known to be affected by this vulnerability.", "This product is known to be not affected by this vulnerability.", "This product contains a fix for this vulnerability.", "It is not known yet whether these versions are or are not affected by the vulnerability. However, it is still under investigation."], "type": "string"}, "vulnerabilityId": {"description": "The vulnerability identifier for this Assessment. Will hold one of common identifiers e.g. CVE, GHSA etc.", "type": "string"}}, "type": "object"}, "Attestation": {"description": "Occurrence that represents a single \"attestation\". The authenticity of an attestation can be verified using the attached signature. If the verifier trusts the public key of the signer, then verifying the signature is sufficient to establish trust. In this circumstance, the authority to which this attestation is attached is primarily useful for look-up (how to find this attestation if you already know the authority and artifact to be verified) and intent (which authority was this attestation intended to sign for).", "id": "Attestation", "properties": {"genericSignedAttestation": {"$ref": "GenericSignedAttestation"}, "pgpSignedAttestation": {"$ref": "PgpSignedAttestation", "description": "A PGP signed attestation."}}, "type": "object"}, "Authority": {"description": "Note kind that represents a logical attestation \"role\" or \"authority\". For example, an organization might have one `Authority` for \"QA\" and one for \"build\". This note is intended to act strictly as a grouping mechanism for the attached occurrences (Attestations). This grouping mechanism also provides a security boundary, since IAM ACLs gate the ability for a principle to attach an occurrence to a given note. It also provides a single point of lookup to find all attached attestation occurrences, even if they don't all live in the same project.", "id": "Authority", "properties": {"hint": {"$ref": "Hint", "description": "Hint hints at the purpose of the attestation authority."}}, "type": "object"}, "Basis": {"description": "Basis describes the base image portion (Note) of the DockerImage relationship. Linked occurrences are derived from this or an equivalent image via: FROM Or an equivalent reference, e.g. a tag of the resource_url.", "id": "<PERSON><PERSON>", "properties": {"fingerprint": {"$ref": "Fingerprint", "description": "Required. Immutable. The fingerprint of the base image."}, "resourceUrl": {"description": "Required. Immutable. The resource_url for the resource representing the basis of associated occurrence images.", "type": "string"}}, "type": "object"}, "BatchCreateNotesRequest": {"description": "Request to create notes in batch.", "id": "BatchCreateNotesRequest", "properties": {"notes": {"additionalProperties": {"$ref": "Note"}, "description": "Required. The notes to create, the key is expected to be the note ID. Max allowed length is 1000.", "type": "object"}}, "type": "object"}, "BatchCreateNotesResponse": {"description": "Response for creating notes in batch.", "id": "BatchCreateNotesResponse", "properties": {"notes": {"description": "The notes that were created.", "items": {"$ref": "Note"}, "type": "array"}}, "type": "object"}, "BatchCreateOccurrencesRequest": {"description": "Request to create occurrences in batch.", "id": "BatchCreateOccurrencesRequest", "properties": {"occurrences": {"description": "Required. The occurrences to create. Max allowed length is 1000.", "items": {"$ref": "Occurrence"}, "type": "array"}}, "type": "object"}, "BatchCreateOccurrencesResponse": {"description": "Response for creating occurrences in batch.", "id": "BatchCreateOccurrencesResponse", "properties": {"occurrences": {"description": "The occurrences that were created.", "items": {"$ref": "Occurrence"}, "type": "array"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "Build": {"description": "Note holding the version of the provider's builder and the signature of the provenance message in the build details occurrence.", "id": "Build", "properties": {"builderVersion": {"description": "Required. Immutable. Version of the builder which produced this build.", "type": "string"}, "signature": {"$ref": "BuildSignature", "description": "Signature of the build in occurrences pointing to this build note containing build details."}}, "type": "object"}, "BuildDefinition": {"id": "BuildDefinition", "properties": {"buildType": {"type": "string"}, "externalParameters": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "type": "object"}, "internalParameters": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "type": "object"}, "resolvedDependencies": {"items": {"$ref": "ResourceDescriptor"}, "type": "array"}}, "type": "object"}, "BuildMetadata": {"id": "BuildMetadata", "properties": {"finishedOn": {"format": "google-datetime", "type": "string"}, "invocationId": {"type": "string"}, "startedOn": {"format": "google-datetime", "type": "string"}}, "type": "object"}, "BuildProvenance": {"description": "Provenance of a build. Contains all information needed to verify the full details about the build from source to completion.", "id": "BuildProvenance", "properties": {"buildOptions": {"additionalProperties": {"type": "string"}, "description": "Special options applied to this build. This is a catch-all field where build providers can enter any desired additional details.", "type": "object"}, "builderVersion": {"description": "Version string of the builder at the time this build was executed.", "type": "string"}, "builtArtifacts": {"description": "Output of the build.", "items": {"$ref": "Artifact"}, "type": "array"}, "commands": {"description": "Commands requested by the build.", "items": {"$ref": "Command"}, "type": "array"}, "createTime": {"description": "Time at which the build was created.", "format": "google-datetime", "type": "string"}, "creator": {"description": "E-mail address of the user who initiated this build. Note that this was the user's e-mail address at the time the build was initiated; this address may not represent the same end-user for all time.", "type": "string"}, "endTime": {"description": "Time at which execution of the build was finished.", "format": "google-datetime", "type": "string"}, "id": {"description": "Required. Unique identifier of the build.", "type": "string"}, "logsUri": {"description": "URI where any logs for this provenance were written.", "type": "string"}, "projectId": {"description": "ID of the project.", "type": "string"}, "sourceProvenance": {"$ref": "Source", "description": "Details of the Source input to the build."}, "startTime": {"description": "Time at which execution of the build was started.", "format": "google-datetime", "type": "string"}, "triggerId": {"description": "Trigger identifier if the build was triggered automatically; empty if not.", "type": "string"}}, "type": "object"}, "BuildSignature": {"description": "Message encapsulating the signature of the verified build.", "id": "BuildSignature", "properties": {"keyId": {"description": "An ID for the key used to sign. This could be either an ID for the key stored in `public_key` (such as the ID or fingerprint for a PGP key, or the CN for a cert), or a reference to an external key (such as a reference to a key in Cloud Key Management Service).", "type": "string"}, "keyType": {"description": "The type of the key, either stored in `public_key` or referenced in `key_id`.", "enum": ["KEY_TYPE_UNSPECIFIED", "PGP_ASCII_ARMORED", "PKIX_PEM"], "enumDescriptions": ["`KeyType` is not set.", "`PGP ASCII Armored` public key.", "`PKIX PEM` public key."], "type": "string"}, "publicKey": {"description": "Public key of the builder which can be used to verify that the related findings are valid and unchanged. If `key_type` is empty, this defaults to PEM encoded public keys. This field may be empty if `key_id` references an external key. For Cloud Build based signatures, this is a PEM encoded public key. To verify the Cloud Build signature, place the contents of this field into a file (public.pem). The signature field is base64-decoded into its binary representation in signature.bin, and the provenance bytes from `BuildDetails` are base64-decoded into a binary representation in signed.bin. OpenSSL can then verify the signature: `openssl sha256 -verify public.pem -signature signature.bin signed.bin`", "type": "string"}, "signature": {"description": "Required. Signature of the related `BuildProvenance`. In JSON, this is base-64 encoded.", "format": "byte", "type": "string"}}, "type": "object"}, "BuildStep": {"description": "A step in the build pipeline. Next ID: 22", "id": "BuildStep", "properties": {"allowExitCodes": {"description": "Allow this build step to fail without failing the entire build if and only if the exit code is one of the specified codes. If allow_failure is also specified, this field will take precedence.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "allowFailure": {"description": "Allow this build step to fail without failing the entire build. If false, the entire build will fail if this step fails. Otherwise, the build will succeed, but this step will still have a failure status. Error information will be reported in the failure_detail field.", "type": "boolean"}, "args": {"description": "A list of arguments that will be presented to the step when it is started. If the image used to run the step's container has an entrypoint, the `args` are used as arguments to that entrypoint. If the image does not define an entrypoint, the first element in args is used as the entrypoint, and the remainder will be used as arguments.", "items": {"type": "string"}, "type": "array"}, "automapSubstitutions": {"description": "Option to include built-in and custom substitutions as env variables for this build step. This option will override the global option in BuildOption.", "type": "boolean"}, "dir": {"description": "Working directory to use when running this step's container. If this value is a relative path, it is relative to the build's working directory. If this value is absolute, it may be outside the build's working directory, in which case the contents of the path may not be persisted across build step executions, unless a `volume` for that path is specified. If the build specifies a `RepoSource` with `dir` and a step with a `dir`, which specifies an absolute path, the `RepoSource` `dir` is ignored for the step's execution.", "type": "string"}, "entrypoint": {"description": "Entrypoint to be used instead of the build step image's default entrypoint. If unset, the image's default entrypoint is used.", "type": "string"}, "env": {"description": "A list of environment variable definitions to be used when running a step. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "exitCode": {"description": "Output only. Return code from running the step.", "format": "int32", "type": "integer"}, "id": {"description": "Unique identifier for this build step, used in `wait_for` to reference this build step as a dependency.", "type": "string"}, "name": {"description": "Required. The name of the container image that will run this particular build step. If the image is available in the host's Docker daemon's cache, it will be run directly. If not, the host will attempt to pull the image first, using the builder service account's credentials if necessary. The Docker daemon's cache will already have the latest versions of all of the officially supported build steps ([https://github.com/GoogleCloudPlatform/cloud-builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The Docker daemon will also have cached many of the layers for some popular images, like \"ubuntu\", \"debian\", but they will be refreshed at the time you attempt to use them. If you built an image in a previous build step, it will be stored in the host's Docker daemon's cache and is available to use as the name for a later build step.", "type": "string"}, "pullTiming": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for pulling this build step's builder image only."}, "results": {"items": {"$ref": "StepResult"}, "type": "array"}, "script": {"description": "A shell script to be executed in the step. When script is provided, the user cannot specify the entrypoint or args.", "type": "string"}, "secretEnv": {"description": "A list of environment variables which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`.", "items": {"type": "string"}, "type": "array"}, "status": {"description": "Output only. Status of the build step. At this time, build step status is only updated on build completion; step status is not updated in real-time as the build progresses.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build has been received and is being queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "type": "string"}, "timeout": {"description": "Time limit for executing this build step. If not defined, the step has no time limit and will be allowed to continue to run until either it completes or the build itself times out.", "format": "google-duration", "type": "string"}, "timing": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for executing this build step."}, "volumes": {"description": "List of volumes to mount into the build step. Each volume is created as an empty volume prior to execution of the build step. Upon completion of the build, volumes and their contents are discarded. Using a named volume in only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "Volume"}, "type": "array"}, "waitFor": {"description": "The ID(s) of the step(s) that this build step depends on. This build step will not start until all the build steps in `wait_for` have completed successfully. If `wait_for` is empty, this build step will start when all previous build steps in the `Build.Steps` list have completed successfully.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ByProducts": {"description": "Defines an object for the byproducts field in in-toto links. The suggested fields are \"stderr\", \"stdout\", and \"return-value\".", "id": "ByProducts", "properties": {"customValues": {"additionalProperties": {"type": "string"}, "type": "object"}}, "type": "object"}, "CVSS": {"description": "Common Vulnerability Scoring System. This message is compatible with CVSS v2 and v3. For CVSS v2 details, see https://www.first.org/cvss/v2/guide CVSS v2 calculator: https://nvd.nist.gov/vuln-metrics/cvss/v2-calculator For CVSS v3 details, see https://www.first.org/cvss/specification-document CVSS v3 calculator: https://nvd.nist.gov/vuln-metrics/cvss/v3-calculator", "id": "CVSS", "properties": {"attackComplexity": {"description": "Defined in CVSS v3, CVSS v2", "enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH", "ATTACK_COMPLEXITY_MEDIUM"], "enumDescriptions": ["Defined in CVSS v3, CVSS v2", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v2"], "type": "string"}, "attackVector": {"description": "Base Metrics Represents the intrinsic characteristics of a vulnerability that are constant over time and across user environments. Defined in CVSS v3, CVSS v2", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "enumDescriptions": ["Defined in CVSS v3, CVSS v2", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v3"], "type": "string"}, "authentication": {"description": "Defined in CVSS v2", "enum": ["AUTHENTICATION_UNSPECIFIED", "AUTHENTICATION_MULTIPLE", "AUTHENTICATION_SINGLE", "AUTHENTICATION_NONE"], "enumDescriptions": ["Defined in CVSS v2", "Defined in CVSS v2", "Defined in CVSS v2", "Defined in CVSS v2"], "type": "string"}, "availabilityImpact": {"description": "Defined in CVSS v3, CVSS v2", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE", "IMPACT_PARTIAL", "IMPACT_COMPLETE"], "enumDescriptions": ["Defined in CVSS v3, CVSS v2", "Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v2", "Defined in CVSS v2"], "type": "string"}, "baseScore": {"description": "The base score is a function of the base metric scores.", "format": "float", "type": "number"}, "confidentialityImpact": {"description": "Defined in CVSS v3, CVSS v2", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE", "IMPACT_PARTIAL", "IMPACT_COMPLETE"], "enumDescriptions": ["Defined in CVSS v3, CVSS v2", "Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v2", "Defined in CVSS v2"], "type": "string"}, "exploitabilityScore": {"format": "float", "type": "number"}, "impactScore": {"format": "float", "type": "number"}, "integrityImpact": {"description": "Defined in CVSS v3, CVSS v2", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE", "IMPACT_PARTIAL", "IMPACT_COMPLETE"], "enumDescriptions": ["Defined in CVSS v3, CVSS v2", "Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3, CVSS v2", "Defined in CVSS v2", "Defined in CVSS v2"], "type": "string"}, "privilegesRequired": {"description": "Defined in CVSS v3", "enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "enumDescriptions": ["Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3"], "type": "string"}, "scope": {"description": "Defined in CVSS v3", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "enumDescriptions": ["Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3"], "type": "string"}, "userInteraction": {"description": "Defined in CVSS v3", "enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "enumDescriptions": ["Defined in CVSS v3", "Defined in CVSS v3", "Defined in CVSS v3"], "type": "string"}}, "type": "object"}, "CVSSv3": {"description": "Deprecated. Common Vulnerability Scoring System version 3. For details, see https://www.first.org/cvss/specification-document", "id": "CVSSv3", "properties": {"attackComplexity": {"enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH"], "enumDescriptions": ["", "", ""], "type": "string"}, "attackVector": {"description": "Base Metrics Represents the intrinsic characteristics of a vulnerability that are constant over time and across user environments.", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "enumDescriptions": ["", "", "", "", ""], "type": "string"}, "availabilityImpact": {"enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "baseScore": {"description": "The base score is a function of the base metric scores.", "format": "float", "type": "number"}, "confidentialityImpact": {"enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "exploitabilityScore": {"format": "float", "type": "number"}, "impactScore": {"format": "float", "type": "number"}, "integrityImpact": {"enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "privilegesRequired": {"enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "scope": {"enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "enumDescriptions": ["", "", ""], "type": "string"}, "userInteraction": {"enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "CloudRepoSourceContext": {"description": "A CloudRepoSourceContext denotes a particular revision in a Google Cloud Source Repo.", "id": "CloudRepoSourceContext", "properties": {"aliasContext": {"$ref": "AliasContext", "description": "An alias, which may be a branch or tag."}, "repoId": {"$ref": "RepoId", "description": "The ID of the repo."}, "revisionId": {"description": "A revision ID.", "type": "string"}}, "type": "object"}, "Command": {"description": "Command describes a step performed as part of the build pipeline.", "id": "Command", "properties": {"args": {"description": "Command-line arguments used when executing this command.", "items": {"type": "string"}, "type": "array"}, "dir": {"description": "Working directory (relative to project source root) used when running this command.", "type": "string"}, "env": {"description": "Environment variables set before running this command.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Optional unique identifier for this command, used in wait_for to reference this command as a dependency.", "type": "string"}, "name": {"description": "Required. Name of the command, as presented on the command line, or if the command is packaged as a Docker container, as presented to `docker pull`.", "type": "string"}, "waitFor": {"description": "The ID(s) of the command(s) that this command depends on.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig": {"description": "ApprovalConfig describes configuration for manual approval of a build.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig", "properties": {"approvalRequired": {"description": "Whether or not approval is needed. If this is set on a build, it will become pending when created, and will need to be explicitly approved to start.", "type": "boolean"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult": {"description": "ApprovalResult describes the decision and associated metadata of a manual approval of a build.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult", "properties": {"approvalTime": {"description": "Output only. The time when the approval decision was made.", "format": "google-datetime", "readOnly": true, "type": "string"}, "approverAccount": {"description": "Output only. Email of the user that called the ApproveBuild API to approve or reject a build at the time that the API was called.", "readOnly": true, "type": "string"}, "comment": {"description": "Optional. An optional comment for this manual approval result.", "type": "string"}, "decision": {"description": "Required. The decision of this manual approval.", "enum": ["DECISION_UNSPECIFIED", "APPROVED", "REJECTED"], "enumDescriptions": ["Default enum type. This should not be used.", "Build is approved.", "Build is rejected."], "type": "string"}, "url": {"description": "Optional. An optional URL tied to this manual approval result. This field is essentially the same as comment, except that it will be rendered by the UI differently. An example use case is a link to an external job that approved this Build.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts": {"description": "Artifacts produced by a build that should be uploaded upon successful completion of all build steps.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts", "properties": {"goModules": {"description": "Optional. A list of Go modules to be uploaded to Artifact Registry upon successful completion of all build steps. If any objects fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsGoModule"}, "type": "array"}, "images": {"description": "A list of images to be pushed upon the successful completion of all build steps. The images will be pushed using the builder service account's credentials. The digests of the pushed images will be stored in the Build resource's results field. If any of the images fail to be pushed, the build is marked FAILURE.", "items": {"type": "string"}, "type": "array"}, "mavenArtifacts": {"description": "A list of Maven artifacts to be uploaded to Artifact Registry upon successful completion of all build steps. Artifacts in the workspace matching specified paths globs will be uploaded to the specified Artifact Registry repository using the builder service account's credentials. If any artifacts fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact"}, "type": "array"}, "npmPackages": {"description": "A list of npm packages to be uploaded to Artifact Registry upon successful completion of all build steps. Npm packages in the specified paths will be uploaded to the specified Artifact Registry repository using the builder service account's credentials. If any packages fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage"}, "type": "array"}, "objects": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects", "description": "A list of objects to be uploaded to Cloud Storage upon successful completion of all build steps. Files in the workspace matching specified paths globs will be uploaded to the specified Cloud Storage location using the builder service account's credentials. The location and generation of the uploaded objects will be stored in the Build resource's results field. If any objects fail to be pushed, the build is marked FAILURE."}, "pythonPackages": {"description": "A list of Python packages to be uploaded to Artifact Registry upon successful completion of all build steps. The build service account credentials will be used to perform the upload. If any objects fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage"}, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects": {"description": "Files in the workspace to upload to Cloud Storage upon successful completion of all build steps.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects", "properties": {"location": {"description": "Cloud Storage bucket and optional object path, in the form \"gs://bucket/path/to/somewhere/\". (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)). Files in the workspace matching any path pattern will be uploaded to Cloud Storage with this location as a prefix.", "type": "string"}, "paths": {"description": "Path globs used to match files in the build's workspace.", "items": {"type": "string"}, "type": "array"}, "timing": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing all artifact objects.", "readOnly": true}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsGoModule": {"description": "Go module to upload to Artifact Registry upon successful completion of all build steps. A module refers to all dependencies in a go.mod file.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsGoModule", "properties": {"modulePath": {"description": "Optional. The Go module's \"module path\". e.g. example.com/foo/v2", "type": "string"}, "moduleVersion": {"description": "Optional. The Go module's semantic version in the form vX.Y.Z. e.g. v0.1.1 Pre-release identifiers can also be added by appending a dash and dot separated ASCII alphanumeric characters and hyphens. e.g. v0.2.3-alpha.x.12m.5", "type": "string"}, "repositoryLocation": {"description": "Optional. Location of the Artifact Registry repository. i.e. us-east1 Defaults to the build’s location.", "type": "string"}, "repositoryName": {"description": "Optional. Artifact Registry repository name. Specified Go modules will be zipped and uploaded to Artifact Registry with this location as a prefix. e.g. my-go-repo", "type": "string"}, "repositoryProjectId": {"description": "Optional. Project ID of the Artifact Registry repository. Defaults to the build project.", "type": "string"}, "sourcePath": {"description": "Optional. Source path of the go.mod file in the build's workspace. If not specified, this will default to the current directory. e.g. ~/code/go/mypackage", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact": {"description": "A Maven artifact to upload to Artifact Registry upon successful completion of all build steps.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact", "properties": {"artifactId": {"description": "Maven `artifactId` value used when uploading the artifact to Artifact Registry.", "type": "string"}, "groupId": {"description": "Maven `groupId` value used when uploading the artifact to Artifact Registry.", "type": "string"}, "path": {"description": "Optional. Path to an artifact in the build's workspace to be uploaded to Artifact Registry. This can be either an absolute path, e.g. /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.", "type": "string"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-maven.pkg.dev/$PROJECT/$REPOSITORY\" Artifact in the workspace specified by path will be uploaded to Artifact Registry with this location as a prefix.", "type": "string"}, "version": {"description": "Maven `version` value used when uploading the artifact to Artifact Registry.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage": {"description": "Npm package to upload to Artifact Registry upon successful completion of all build steps.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage", "properties": {"packagePath": {"description": "Path to the package.json. e.g. workspace/path/to/package", "type": "string"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-npm.pkg.dev/$PROJECT/$REPOSITORY\" Npm package in the workspace specified by path will be zipped and uploaded to Artifact Registry with this location as a prefix.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage": {"description": "Python package to upload to Artifact Registry upon successful completion of all build steps. A package can encapsulate multiple objects to be uploaded to a single repository.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage", "properties": {"paths": {"description": "Path globs used to match files in the build's workspace. For Python/ Twine, this is usually `dist/*`, and sometimes additionally an `.asc` file.", "items": {"type": "string"}, "type": "array"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-python.pkg.dev/$PROJECT/$REPOSITORY\" Files in the workspace matching any path pattern will be uploaded to Artifact Registry with this location as a prefix.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Build": {"description": "A build resource in the Cloud Build API. At a high level, a `Build` describes where to find source code, how to build it (for example, the builder image to run on the source), and where to store the built artifacts. Fields can include the following variables, which will be expanded when the build is created: - $PROJECT_ID: the project ID of the build. - $PROJECT_NUMBER: the project number of the build. - $LOCATION: the location/region of the build. - $BUILD_ID: the autogenerated ID of the build. - $REPO_NAME: the source repository name specified by RepoSource. - $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA specified by RepoSource or resolved from the specified branch or tag. - $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Build", "properties": {"approval": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval", "description": "Output only. Describes this build's approval configuration, status, and result.", "readOnly": true}, "artifacts": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts", "description": "Artifacts produced by the build that should be uploaded upon successful completion of all build steps."}, "availableSecrets": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets", "description": "Secrets and secret environment variables."}, "buildTriggerId": {"description": "Output only. The ID of the `BuildTrigger` that triggered this build, if it was triggered automatically.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time at which the request to create the build was received.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dependencies": {"description": "Optional. Dependencies that the Cloud Build worker will fetch before executing user steps.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Dependency"}, "type": "array"}, "failureInfo": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo", "description": "Output only. Contains information about the build when status=FAILURE.", "readOnly": true}, "finishTime": {"description": "Output only. Time at which execution of the build was finished. The difference between finish_time and start_time is the duration of the build's execution.", "format": "google-datetime", "readOnly": true, "type": "string"}, "gitConfig": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitConfig", "description": "Optional. Configuration for git operations."}, "id": {"description": "Output only. Unique identifier of the build.", "readOnly": true, "type": "string"}, "images": {"description": "A list of images to be pushed upon the successful completion of all build steps. The images are pushed using the builder service account's credentials. The digests of the pushed images will be stored in the `Build` resource's results field. If any of the images fail to be pushed, the build status is marked `FAILURE`.", "items": {"type": "string"}, "type": "array"}, "logUrl": {"description": "Output only. URL to logs for this build in Google Cloud Console.", "readOnly": true, "type": "string"}, "logsBucket": {"description": "Cloud Storage bucket where logs should be written (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)). Logs file names will be of the format `${logs_bucket}/log-${build_id}.txt`.", "type": "string"}, "name": {"description": "Output only. The 'Build' name with format: `projects/{project}/locations/{location}/builds/{build}`, where {build} is a unique identifier generated by the service.", "readOnly": true, "type": "string"}, "options": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions", "description": "Special options for this build."}, "projectId": {"description": "Output only. ID of the project.", "readOnly": true, "type": "string"}, "queueTtl": {"description": "TTL in queue for this build. If provided and the build is enqueued longer than this value, the build will expire and the build status will be `EXPIRED`. The TTL starts ticking from create_time.", "format": "google-duration", "type": "string"}, "results": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Results", "description": "Output only. Results of the build.", "readOnly": true}, "secrets": {"description": "Secrets to decrypt using Cloud Key Management Service. Note: Secret Manager is the recommended technique for managing sensitive data with Cloud Build. Use `available_secrets` to configure builds to access secrets from Secret Manager. For instructions, see: https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Secret"}, "type": "array"}, "serviceAccount": {"description": "IAM service account whose credentials will be used at build runtime. Must be of the format `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email address or uniqueId of the service account. ", "type": "string"}, "source": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Source", "description": "Optional. The location of the source files to build."}, "sourceProvenance": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance", "description": "Output only. A permanent fixed identifier for source.", "readOnly": true}, "startTime": {"description": "Output only. Time at which execution of the build was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "status": {"description": "Output only. Status of the build.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Customer-readable message about the current status.", "readOnly": true, "type": "string"}, "steps": {"description": "Required. The operations to be performed on the workspace.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep"}, "type": "array"}, "substitutions": {"additionalProperties": {"type": "string"}, "description": "Substitutions data for `Build` resource.", "type": "object"}, "tags": {"description": "Tags for annotation of a `Build`. These are not docker tags.", "items": {"type": "string"}, "type": "array"}, "timeout": {"description": "Amount of time that this build should be allowed to run, to second granularity. If this amount of time elapses, work on the build will cease and the build status will be `TIMEOUT`. `timeout` starts ticking from `startTime`. Default time is 60 minutes.", "format": "google-duration", "type": "string"}, "timing": {"additionalProperties": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan"}, "description": "Output only. Stores timing information for phases of the build. Valid keys are: * BUILD: time to execute all build steps. * PUSH: time to push all artifacts including docker images and non docker artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If the build does not specify source or images, these keys will not be included.", "readOnly": true, "type": "object"}, "warnings": {"description": "Output only. Non-fatal problems encountered during the execution of the build.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval": {"description": "BuildApproval describes a build's approval configuration, state, and result.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval", "properties": {"config": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig", "description": "Output only. Configuration for manual approval of this build.", "readOnly": true}, "result": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult", "description": "Output only. Result of manual approval for this Build.", "readOnly": true}, "state": {"description": "Output only. The state of this build's approval.", "enum": ["STATE_UNSPECIFIED", "PENDING", "APPROVED", "REJECTED", "CANCELLED"], "enumDescriptions": ["Default enum type. This should not be used.", "Build approval is pending.", "Build approval has been approved.", "Build approval has been rejected.", "Build was cancelled while it was still pending approval."], "readOnly": true, "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo": {"description": "A fatal problem encountered during the execution of the build.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo", "properties": {"detail": {"description": "Explains the failure issue in more detail using hard-coded text.", "type": "string"}, "type": {"description": "The name of the failure.", "enum": ["FAILURE_TYPE_UNSPECIFIED", "PUSH_FAILED", "PUSH_IMAGE_NOT_FOUND", "PUSH_NOT_AUTHORIZED", "LOGGING_FAILURE", "USER_BUILD_STEP", "FETCH_SOURCE_FAILED"], "enumDescriptions": ["Type unspecified", "Unable to push the image to the repository.", "Final image not found.", "Unauthorized push of the final image.", "Backend logging failures. Should retry.", "A build step has failed.", "The source fetching has failed."], "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions": {"description": "Optional arguments to enable specific features of builds.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions", "properties": {"automapSubstitutions": {"description": "Option to include built-in and custom substitutions as env variables for all build steps.", "type": "boolean"}, "defaultLogsBucketBehavior": {"description": "Optional. Option to specify how default logs buckets are setup.", "enum": ["DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED", "REGIONAL_USER_OWNED_BUCKET", "LEGACY_BUCKET"], "enumDescriptions": ["Unspecified.", "Bucket is located in user-owned project in the same region as the build. The builder service account must have access to create and write to Cloud Storage buckets in the build project.", "Bucket is located in a Google-owned project and is not regionalized."], "type": "string"}, "diskSizeGb": {"description": "Requested disk size for the VM that runs the build. Note that this is *NOT* \"disk free\"; some of the space will be used by the operating system and build utilities. Also note that this is the minimum disk size that will be allocated for the build -- the build may run with a larger disk than requested. At present, the maximum disk size is 4000GB; builds that request more than the maximum are rejected with an error.", "format": "int64", "type": "string"}, "dynamicSubstitutions": {"description": "Option to specify whether or not to apply bash style string operations to the substitutions. NOTE: this is always enabled for triggered builds and cannot be overridden in the build configuration file.", "type": "boolean"}, "enableStructuredLogging": {"description": "Optional. Option to specify whether structured logging is enabled. If true, JSON-formatted logs are parsed as structured logs.", "type": "boolean"}, "env": {"description": "A list of global environment variable definitions that will exist for all build steps in this build. If a variable is defined in both globally and in a build step, the variable will use the build step value. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "logStreamingOption": {"description": "Option to define build log streaming behavior to Cloud Storage.", "enum": ["STREAM_DEFAULT", "STREAM_ON", "STREAM_OFF"], "enumDescriptions": ["Service may automatically determine build log streaming behavior.", "Build logs should be streamed to Cloud Storage.", "Build logs should not be streamed to Cloud Storage; they will be written when the build is completed."], "type": "string"}, "logging": {"description": "Option to specify the logging mode, which determines if and where build logs are stored.", "enum": ["LOGGING_UNSPECIFIED", "LEGACY", "GCS_ONLY", "STACKDRIVER_ONLY", "CLOUD_LOGGING_ONLY", "NONE"], "enumDeprecated": [false, false, false, true, false, false], "enumDescriptions": ["The service determines the logging mode. The default is `LEGACY`. Do not rely on the default logging behavior as it may change in the future.", "Build logs are stored in Cloud Logging and Cloud Storage.", "Build logs are stored in Cloud Storage.", "This option is the same as CLOUD_LOGGING_ONLY.", "Build logs are stored in Cloud Logging. Selecting this option will not allow [logs streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).", "Turn off all logging. No build logs will be captured."], "type": "string"}, "machineType": {"description": "Compute Engine machine type on which to run the build.", "enum": ["UNSPECIFIED", "N1_HIGHCPU_8", "N1_HIGHCPU_32", "E2_HIGHCPU_8", "E2_HIGHCPU_32", "E2_MEDIUM"], "enumDeprecated": [false, true, true, false, false, false], "enumDescriptions": ["Standard machine type.", "Highcpu machine with 8 CPUs.", "Highcpu machine with 32 CPUs.", "Highcpu e2 machine with 8 CPUs.", "Highcpu e2 machine with 32 CPUs.", "E2 machine with 1 CPU."], "type": "string"}, "pool": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption", "description": "Optional. Specification for execution on a `WorkerPool`. See [running builds in a private pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool) for more information."}, "pubsubTopic": {"description": "Optional. Option to specify the Pub/Sub topic to receive build status updates.", "type": "string"}, "requestedVerifyOption": {"description": "Requested verifiability options.", "enum": ["NOT_VERIFIED", "VERIFIED"], "enumDescriptions": ["Not a verifiable build (the default).", "Build must be verified."], "type": "string"}, "secretEnv": {"description": "A list of global environment variables, which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`. These variables will be available to all build steps in this build.", "items": {"type": "string"}, "type": "array"}, "sourceProvenanceHash": {"description": "Requested hash for SourceProvenance.", "items": {"enum": ["NONE", "SHA256", "MD5", "GO_MODULE_H1", "SHA512"], "enumDescriptions": ["No hash requested.", "Use a sha256 hash.", "Use a md5 hash.", "Dirhash of a Go module's source code which is then hex-encoded.", "Use a sha512 hash."], "type": "string"}, "type": "array"}, "substitutionOption": {"description": "Option to specify behavior when there is an error in the substitution checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and cannot be overridden in the build configuration file.", "enum": ["MUST_MATCH", "ALLOW_LOOSE"], "enumDescriptions": ["Fails the build if error in substitutions checks, like missing a substitution in the template or in the map.", "Do not fail the build if error in substitutions checks."], "type": "string"}, "volumes": {"description": "Global list of volumes to mount for ALL build steps Each volume is created as an empty volume prior to starting the build process. Upon completion of the build, volumes and their contents are discarded. Global volume names and paths cannot conflict with the volumes defined a build step. Using a global volume in a build with only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Volume"}, "type": "array"}, "workerPool": {"deprecated": true, "description": "This field deprecated; please use `pool.name` instead.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption": {"description": "Details about how a build should be executed on a `WorkerPool`. See [running builds in a private pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool) for more information.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption", "properties": {"name": {"description": "The `WorkerPool` resource to execute the build on. You must have `cloudbuild.workerpools.use` on the project hosting the WorkerPool. Format projects/{project}/locations/{location}/workerPools/{workerPoolId}", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep": {"description": "A step in the build pipeline.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep", "properties": {"allowExitCodes": {"description": "Allow this build step to fail without failing the entire build if and only if the exit code is one of the specified codes. If allow_failure is also specified, this field will take precedence.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "allowFailure": {"description": "Allow this build step to fail without failing the entire build. If false, the entire build will fail if this step fails. Otherwise, the build will succeed, but this step will still have a failure status. Error information will be reported in the failure_detail field.", "type": "boolean"}, "args": {"description": "A list of arguments that will be presented to the step when it is started. If the image used to run the step's container has an entrypoint, the `args` are used as arguments to that entrypoint. If the image does not define an entrypoint, the first element in args is used as the entrypoint, and the remainder will be used as arguments.", "items": {"type": "string"}, "type": "array"}, "automapSubstitutions": {"description": "Option to include built-in and custom substitutions as env variables for this build step. This option will override the global option in BuildOption.", "type": "boolean"}, "dir": {"description": "Working directory to use when running this step's container. If this value is a relative path, it is relative to the build's working directory. If this value is absolute, it may be outside the build's working directory, in which case the contents of the path may not be persisted across build step executions, unless a `volume` for that path is specified. If the build specifies a `RepoSource` with `dir` and a step with a `dir`, which specifies an absolute path, the `RepoSource` `dir` is ignored for the step's execution.", "type": "string"}, "entrypoint": {"description": "Entrypoint to be used instead of the build step image's default entrypoint. If unset, the image's default entrypoint is used.", "type": "string"}, "env": {"description": "A list of environment variable definitions to be used when running a step. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "exitCode": {"description": "Output only. Return code from running the step.", "format": "int32", "readOnly": true, "type": "integer"}, "id": {"description": "Unique identifier for this build step, used in `wait_for` to reference this build step as a dependency.", "type": "string"}, "name": {"description": "Required. The name of the container image that will run this particular build step. If the image is available in the host's Docker daemon's cache, it will be run directly. If not, the host will attempt to pull the image first, using the builder service account's credentials if necessary. The Docker daemon's cache will already have the latest versions of all of the officially supported build steps ([https://github.com/GoogleCloudPlatform/cloud-builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The Docker daemon will also have cached many of the layers for some popular images, like \"ubuntu\", \"debian\", but they will be refreshed at the time you attempt to use them. If you built an image in a previous build step, it will be stored in the host's Docker daemon's cache and is available to use as the name for a later build step.", "type": "string"}, "pullTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pulling this build step's builder image only.", "readOnly": true}, "script": {"description": "A shell script to be executed in the step. When script is provided, the user cannot specify the entrypoint or args.", "type": "string"}, "secretEnv": {"description": "A list of environment variables which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`.", "items": {"type": "string"}, "type": "array"}, "status": {"description": "Output only. Status of the build step. At this time, build step status is only updated on build completion; step status is not updated in real-time as the build progresses.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "readOnly": true, "type": "string"}, "timeout": {"description": "Time limit for executing this build step. If not defined, the step has no time limit and will be allowed to continue to run until either it completes or the build itself times out.", "format": "google-duration", "type": "string"}, "timing": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for executing this build step.", "readOnly": true}, "volumes": {"description": "List of volumes to mount into the build step. Each volume is created as an empty volume prior to execution of the build step. Upon completion of the build, volumes and their contents are discarded. Using a named volume in only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Volume"}, "type": "array"}, "waitFor": {"description": "The ID(s) of the step(s) that this build step depends on. This build step will not start until all the build steps in `wait_for` have completed successfully. If `wait_for` is empty, this build step will start when all previous build steps in the `Build.Steps` list have completed successfully.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning": {"description": "A non-fatal problem encountered during the execution of the build.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning", "properties": {"priority": {"description": "The priority for this warning.", "enum": ["PRIORITY_UNSPECIFIED", "INFO", "WARNING", "ALERT"], "enumDescriptions": ["Should not be used.", "e.g. deprecation warnings and alternative feature highlights.", "e.g. automated detection of possible issues with the build.", "e.g. alerts that a feature used in the build is pending removal"], "type": "string"}, "text": {"description": "Explanation of the warning generated.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage": {"description": "An image built by the pipeline.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage", "properties": {"digest": {"description": "Docker Registry 2.0 digest.", "type": "string"}, "name": {"description": "Name used to push the container image to Google Container Registry, as presented to `docker push`.", "type": "string"}, "pushTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified image.", "readOnly": true}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1ConnectedRepository": {"description": "Location of the source in a 2nd-gen Google Cloud Build repository resource.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1ConnectedRepository", "properties": {"dir": {"description": "Optional. Directory, relative to the source root, in which to run the build.", "type": "string"}, "repository": {"description": "Required. Name of the Google Cloud Build repository, formatted as `projects/*/locations/*/connections/*/repositories/*`.", "type": "string"}, "revision": {"description": "Required. The revision to fetch from the Git repository such as a branch, a tag, a commit SHA, or any Git ref.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Dependency": {"description": "A dependency that the Cloud Build worker will fetch before executing user steps.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Dependency", "properties": {"empty": {"description": "If set to true disable all dependency fetching (ignoring the default source as well).", "type": "boolean"}, "gitSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1DependencyGitSourceDependency", "description": "Represents a git repository as a build dependency."}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1DependencyGitSourceDependency": {"description": "Represents a git repository as a build dependency.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1DependencyGitSourceDependency", "properties": {"depth": {"description": "Optional. How much history should be fetched for the build (default 1, -1 for all history).", "format": "int64", "type": "string"}, "destPath": {"description": "Required. Where should the files be placed on the worker.", "type": "string"}, "recurseSubmodules": {"description": "Optional. True if submodules should be fetched too (default false).", "type": "boolean"}, "repository": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1DependencyGitSourceRepository", "description": "Required. The kind of repo (url or dev connect)."}, "revision": {"description": "Required. The revision that we will fetch the repo at.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1DependencyGitSourceRepository": {"description": "A repository for a git source.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1DependencyGitSourceRepository", "properties": {"developerConnect": {"description": "The Developer Connect Git repository link formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`", "type": "string"}, "url": {"description": "Location of the Git repository.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1DeveloperConnectConfig": {"description": "This config defines the location of a source through Developer Connect.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1DeveloperConnectConfig", "properties": {"dir": {"description": "Required. Directory, relative to the source root, in which to run the build.", "type": "string"}, "gitRepositoryLink": {"description": "Required. The Developer Connect Git repository link, formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.", "type": "string"}, "revision": {"description": "Required. The revision to fetch from the Git repository such as a branch, a tag, a commit SHA, or any Git ref.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes": {"description": "Container message for hashes of byte content of files, used in SourceProvenance messages to verify integrity of source input to the build.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes", "properties": {"fileHash": {"description": "Collection of file hashes.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1Hash"}, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1GitConfig": {"description": "GitConfig is a configuration for git operations.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitConfig", "properties": {"http": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitConfigHttpConfig", "description": "Configuration for HTTP related git operations."}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1GitConfigHttpConfig": {"description": "HttpConfig is a configuration for HTTP related git operations.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitConfigHttpConfig", "properties": {"proxySecretVersionName": {"description": "SecretVersion resource of the HTTP proxy URL. The Service Account used in the build (either the default Service Account or user-specified Service Account) should have `secretmanager.versions.access` permissions on this secret. The proxy URL should be in format `protocol://@]proxyhost[:port]`.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource": {"description": "Location of the source in any accessible Git repository.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource", "properties": {"dir": {"description": "Optional. Directory, relative to the source root, in which to run the build. This must be a relative path. If a step's `dir` is specified and is an absolute path, this value is ignored for that step's execution.", "type": "string"}, "revision": {"description": "Optional. The revision to fetch from the Git repository such as a branch, a tag, a commit SHA, or any Git ref. Cloud Build uses `git fetch` to fetch the revision from the Git repository; therefore make sure that the string you provide for `revision` is parsable by the command. For information on string values accepted by `git fetch`, see https://git-scm.com/docs/gitrevisions#_specifying_revisions. For information on `git fetch`, see https://git-scm.com/docs/git-fetch.", "type": "string"}, "url": {"description": "Required. Location of the Git repo to build. This will be used as a `git remote`, see https://git-scm.com/docs/git-remote.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Hash": {"description": "Container message for hash values.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Hash", "properties": {"type": {"description": "The type of hash that was performed.", "enum": ["NONE", "SHA256", "MD5", "GO_MODULE_H1", "SHA512"], "enumDescriptions": ["No hash requested.", "Use a sha256 hash.", "Use a md5 hash.", "Dirhash of a Go module's source code which is then hex-encoded.", "Use a sha512 hash."], "type": "string"}, "value": {"description": "The hash value.", "format": "byte", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret": {"description": "Pairs a set of secret environment variables mapped to encrypted values with the Cloud KMS key to use to decrypt the value.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret", "properties": {"envMap": {"additionalProperties": {"format": "byte", "type": "string"}, "description": "Map of environment variable name to its encrypted value. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step. Values can be at most 64 KB in size. There can be at most 100 secret values across all of a build's secrets.", "type": "object"}, "kmsKeyName": {"description": "Resource name of Cloud KMS crypto key to decrypt the encrypted value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource": {"description": "Location of the source in a Google Cloud Source Repository.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource", "properties": {"branchName": {"description": "Regex matching branches to build. The syntax of the regular expressions accepted is the syntax accepted by RE2 and described at https://github.com/google/re2/wiki/Syntax", "type": "string"}, "commitSha": {"description": "Explicit commit SHA to build.", "type": "string"}, "dir": {"description": "Optional. Directory, relative to the source root, in which to run the build. This must be a relative path. If a step's `dir` is specified and is an absolute path, this value is ignored for that step's execution.", "type": "string"}, "invertRegex": {"description": "Optional. Only trigger a build if the revision regex does NOT match the revision regex.", "type": "boolean"}, "projectId": {"description": "Optional. ID of the project that owns the Cloud Source Repository. If omitted, the project ID requesting the build is assumed.", "type": "string"}, "repoName": {"description": "Required. Name of the Cloud Source Repository.", "type": "string"}, "substitutions": {"additionalProperties": {"type": "string"}, "description": "Optional. Substitutions to use in a triggered build. Should only be used with RunBuildTrigger", "type": "object"}, "tagName": {"description": "Regex matching tags to build. The syntax of the regular expressions accepted is the syntax accepted by RE2 and described at https://github.com/google/re2/wiki/Syntax", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Results": {"description": "Artifacts created by the build pipeline.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Results", "properties": {"artifactManifest": {"description": "Path to the artifact manifest for non-container artifacts uploaded to Cloud Storage. Only populated when artifacts are uploaded to Cloud Storage.", "type": "string"}, "artifactTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Time to push all non-container artifacts to Cloud Storage."}, "buildStepImages": {"description": "List of build step digests, in the order corresponding to build step indices.", "items": {"type": "string"}, "type": "array"}, "buildStepOutputs": {"description": "List of build step outputs, produced by builder images, in the order corresponding to build step indices. [Cloud Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can produce this output by writing to `$BUILDER_OUTPUT/output`. Only the first 50KB of data is stored. Note that the `$BUILDER_OUTPUT` variable is read-only and can't be substituted.", "items": {"format": "byte", "type": "string"}, "type": "array"}, "goModules": {"description": "Optional. Go module artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedGoModule"}, "type": "array"}, "images": {"description": "Container images that were built as a part of the build.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage"}, "type": "array"}, "mavenArtifacts": {"description": "Maven artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact"}, "type": "array"}, "npmPackages": {"description": "Npm packages uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage"}, "type": "array"}, "numArtifacts": {"description": "Number of non-container artifacts uploaded to Cloud Storage. Only populated when artifacts are uploaded to Cloud Storage.", "format": "int64", "type": "string"}, "pythonPackages": {"description": "Python artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage"}, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Secret": {"description": "Pairs a set of secret environment variables containing encrypted values with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName` with `available_secrets` instead of using `kmsKeyName` with `secret`. For instructions see: https://cloud.google.com/cloud-build/docs/securing-builds/use-encrypted-credentials.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Secret", "properties": {"kmsKeyName": {"description": "Cloud KMS key name to use to decrypt these envs.", "type": "string"}, "secretEnv": {"additionalProperties": {"format": "byte", "type": "string"}, "description": "Map of environment variable name to its encrypted value. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step. Values can be at most 64 KB in size. There can be at most 100 secret values across all of a build's secrets.", "type": "object"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret": {"description": "Pairs a secret environment variable with a SecretVersion in Secret Manager.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret", "properties": {"env": {"description": "Environment variable name to associate with the secret. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step.", "type": "string"}, "versionName": {"description": "Resource name of the SecretVersion. In format: projects/*/secrets/*/versions/*", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets": {"description": "Secrets and secret environment variables.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets", "properties": {"inline": {"description": "Secrets encrypted with KMS key and the associated secret environment variable.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret"}, "type": "array"}, "secretManager": {"description": "Secrets in Secret Manager and associated secret environment variable.", "items": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret"}, "type": "array"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Source": {"description": "Location of the source in a supported storage service.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Source", "properties": {"connectedRepository": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ConnectedRepository", "description": "Optional. If provided, get the source from this 2nd-gen Google Cloud Build repository resource."}, "developerConnectConfig": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1DeveloperConnectConfig", "description": "If provided, get the source from this Developer Connect config."}, "gitSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource", "description": "If provided, get the source from this Git repository."}, "repoSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource", "description": "If provided, get the source from this location in a Cloud Source Repository."}, "storageSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource", "description": "If provided, get the source from this location in Cloud Storage."}, "storageSourceManifest": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest", "description": "If provided, get the source from this manifest in Cloud Storage. This feature is in Preview; see description [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher)."}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance": {"description": "Provenance of the source. Ways to find the original source, or verify that some source was used for this build.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance", "properties": {"fileHashes": {"additionalProperties": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes"}, "description": "Output only. Hash(es) of the build source, which can be used to verify that the original source integrity was maintained in the build. Note that `FileHashes` will only be populated if `BuildOptions` has requested a `SourceProvenanceHash`. The keys to this map are file paths used as build source and the values contain the hash values for those files. If the build source came in a single package such as a gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path to that file.", "readOnly": true, "type": "object"}, "resolvedConnectedRepository": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1ConnectedRepository", "description": "Output only. A copy of the build's `source.connected_repository`, if exists, with any revisions resolved.", "readOnly": true}, "resolvedGitSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource", "description": "Output only. A copy of the build's `source.git_source`, if exists, with any revisions resolved.", "readOnly": true}, "resolvedRepoSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource", "description": "A copy of the build's `source.repo_source`, if exists, with any revisions resolved."}, "resolvedStorageSource": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource", "description": "A copy of the build's `source.storage_source`, if exists, with any generations resolved."}, "resolvedStorageSourceManifest": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest", "description": "A copy of the build's `source.storage_source_manifest`, if exists, with any revisions resolved. This feature is in Preview."}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource": {"description": "Location of the source in an archive file in Cloud Storage.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource", "properties": {"bucket": {"description": "Cloud Storage bucket containing the source (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Optional. Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Required. Cloud Storage object containing the source. This object must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing source to build.", "type": "string"}, "sourceFetcher": {"description": "Optional. Option to specify the tool to fetch the source file for the build.", "enum": ["SOURCE_FETCHER_UNSPECIFIED", "GSUTIL", "GCS_FETCHER"], "enumDescriptions": ["Unspecified defaults to GSUTIL.", "Use the \"gsutil\" tool to download the source file.", "Use the Cloud Storage Fetcher tool to download the source file."], "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest": {"description": "Location of the source manifest in Cloud Storage. This feature is in Preview; see description [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher).", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest", "properties": {"bucket": {"description": "Required. Cloud Storage bucket containing the source manifest (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Required. Cloud Storage object containing the source manifest. This object must be a JSON file.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan": {"description": "Start and end times for a build execution phase.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "properties": {"endTime": {"description": "End of time span.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Start of time span.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedGoModule": {"description": "A Go module artifact uploaded to Artifact Registry using the GoModule directive.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedGoModule", "properties": {"fileHashes": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the Go Module Artifact."}, "pushTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact": {"description": "A Maven artifact uploaded using the MavenArtifact directive.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact", "properties": {"fileHashes": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the Maven Artifact."}, "pushTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage": {"description": "An npm package uploaded to Artifact Registry using the NpmPackage directive.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage", "properties": {"fileHashes": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the npm package."}, "pushTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded npm package.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage": {"description": "Artifact uploaded using the PythonPackage directive.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage", "properties": {"fileHashes": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the Python Artifact."}, "pushTiming": {"$ref": "ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "ContaineranalysisGoogleDevtoolsCloudbuildV1Volume": {"description": "Volume describes a Docker container volume which is mounted into build steps in order to persist files across build step execution.", "id": "ContaineranalysisGoogleDevtoolsCloudbuildV1Volume", "properties": {"name": {"description": "Name of the volume to mount. Volume names must be unique per build step and must be valid names for Docker volumes. Each named volume must be used by at least two build steps.", "type": "string"}, "path": {"description": "Path at which to mount the volume. Paths must be absolute and cannot conflict with other volume paths on the same build step or with certain reserved volume paths.", "type": "string"}}, "type": "object"}, "Deployable": {"description": "An artifact that can be deployed in some runtime.", "id": "Deployable", "properties": {"resourceUri": {"description": "Required. Resource URI for the artifact being deployed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Deployment": {"description": "The period during which some deployable was active in a runtime.", "id": "Deployment", "properties": {"address": {"description": "Address of the runtime element hosting this deployment.", "type": "string"}, "config": {"description": "Configuration used to create this deployment.", "type": "string"}, "deployTime": {"description": "Required. Beginning of the lifetime of this deployment.", "format": "google-datetime", "type": "string"}, "platform": {"description": "Platform hosting this deployment.", "enum": ["PLATFORM_UNSPECIFIED", "GKE", "FLEX", "CUSTOM"], "enumDescriptions": ["Unknown.", "Google Container Engine.", "Google App Engine: Flexible Environment.", "Custom user-defined platform."], "type": "string"}, "resourceUri": {"description": "Output only. Resource URI for the artifact being deployed taken from the deployable field with the same name.", "items": {"type": "string"}, "type": "array"}, "undeployTime": {"description": "End of the lifetime of this deployment.", "format": "google-datetime", "type": "string"}, "userEmail": {"description": "Identity of the user that triggered this deployment.", "type": "string"}}, "type": "object"}, "Derived": {"description": "Derived describes the derived image portion (Occurrence) of the DockerImage relationship. This image would be produced from a Dockerfile with FROM .", "id": "Derived", "properties": {"baseResourceUrl": {"description": "Output only. This contains the base image URL for the derived image occurrence.", "type": "string"}, "distance": {"description": "Output only. The number of layers by which this image differs from the associated image basis.", "format": "int32", "type": "integer"}, "fingerprint": {"$ref": "Fingerprint", "description": "Required. The fingerprint of the derived image."}, "layerInfo": {"description": "This contains layer-specific metadata, if populated it has length \"distance\" and is ordered with [distance] being the layer immediately following the base image and [1] being the final layer.", "items": {"$ref": "Layer"}, "type": "array"}}, "type": "object"}, "Detail": {"description": "Identifies all appearances of this vulnerability in the package for a specific distro/location. For example: glibc in cpe:/o:debian:debian_linux:8 for versions 2.1 - 2.2", "id": "Detail", "properties": {"cpeUri": {"description": "Required. The CPE URI in [cpe format](https://cpe.mitre.org/specification/) in which the vulnerability manifests. Examples include distro or storage location for vulnerable jar.", "type": "string"}, "description": {"description": "A vendor-specific description of this note.", "type": "string"}, "fixedLocation": {"$ref": "VulnerabilityLocation", "description": "The fix for this specific package version."}, "isObsolete": {"description": "Whether this detail is obsolete. Occurrences are expected not to point to obsolete details.", "type": "boolean"}, "maxAffectedVersion": {"$ref": "Version", "description": "The max version of the package in which the vulnerability exists."}, "minAffectedVersion": {"$ref": "Version", "description": "The min version of the package in which the vulnerability exists."}, "package": {"description": "Required. The name of the package where the vulnerability was found.", "type": "string"}, "packageType": {"description": "The type of package; whether native or non native(ruby gems, node.js packages etc).", "type": "string"}, "severityName": {"description": "The severity (eg: distro assigned severity) for this vulnerability.", "type": "string"}, "source": {"description": "The source from which the information in this Detail was obtained.", "type": "string"}, "sourceUpdateTime": {"description": "The time this information was last changed at the source. This is an upstream timestamp from the underlying information source - e.g. Ubuntu security tracker.", "format": "google-datetime", "type": "string"}, "vendor": {"description": "The name of the vendor of the product.", "type": "string"}}, "type": "object"}, "Details": {"description": "Details of an attestation occurrence.", "id": "Details", "properties": {"attestation": {"$ref": "Attestation", "description": "Required. Attestation for the resource."}}, "type": "object"}, "Digest": {"description": "Digest information.", "id": "Digest", "properties": {"algo": {"description": "`SHA1`, `SHA512` etc.", "type": "string"}, "digestBytes": {"description": "Value of the digest.", "format": "byte", "type": "string"}}, "type": "object"}, "Discovered": {"description": "Provides information about the analysis status of a discovered resource.", "id": "Discovered", "properties": {"analysisCompleted": {"$ref": "AnalysisCompleted"}, "analysisError": {"description": "Indicates any errors encountered during analysis of a resource. There could be 0 or more of these errors.", "items": {"$ref": "Status"}, "type": "array"}, "analysisStatus": {"description": "The status of discovery for the resource.", "enum": ["ANALYSIS_STATUS_UNSPECIFIED", "PENDING", "SCANNING", "FINISHED_SUCCESS", "COMPLETE", "FINISHED_FAILED", "FINISHED_UNSUPPORTED"], "enumDescriptions": ["Unknown.", "Resource is known but no action has been taken yet.", "Resource is being analyzed.", "Analysis has finished successfully.", "Analysis has completed.", "Analysis has finished unsuccessfully, the analysis itself is in a bad state.", "The resource is known not to be supported"], "type": "string"}, "analysisStatusError": {"$ref": "Status", "description": "When an error is encountered this will contain a LocalizedMessage under details to show to the user. The LocalizedMessage is output only and populated by the API."}, "continuousAnalysis": {"description": "Whether the resource is continuously analyzed.", "enum": ["CONTINUOUS_ANALYSIS_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unknown.", "The resource is continuously analyzed.", "The resource is ignored for continuous analysis."], "type": "string"}, "files": {"description": "Files that make up the resource described by the occurrence.", "items": {"$ref": "File"}, "type": "array"}, "lastAnalysisTime": {"description": "The last time continuous analysis was done for this resource. Deprecated, do not use.", "format": "google-datetime", "type": "string"}, "lastScanTime": {"description": "The last time this resource was scanned.", "format": "google-datetime", "type": "string"}, "sbomStatus": {"$ref": "SBOMStatus", "description": "The status of an SBOM generation."}}, "type": "object"}, "Discovery": {"description": "A note that indicates a type of analysis a provider would perform. This note exists in a provider's project. A `Discovery` occurrence is created in a consumer's project at the start of analysis.", "id": "Discovery", "properties": {"analysisKind": {"description": "Required. Immutable. The kind of analysis that is handled by this discovery.", "enum": ["NOTE_KIND_UNSPECIFIED", "VULNERABILITY", "BUILD", "IMAGE", "PACKAGE", "DEPLOYMENT", "DISCOVERY", "ATTESTATION", "INTOTO", "SBOM", "SPDX_PACKAGE", "SPDX_FILE", "SPDX_RELATIONSHIP", "VULNERABILITY_ASSESSMENT", "SBOM_REFERENCE"], "enumDescriptions": ["Default value. This value is unused.", "The note and occurrence represent a package vulnerability.", "The note and occurrence assert build provenance.", "This represents an image basis relationship.", "This represents a package installed via a package manager.", "The note and occurrence track deployment events.", "The note and occurrence track the initial discovery status of a resource.", "This represents a logical \"role\" that can attest to artifacts.", "This represents an in-toto link.", "This represents a software bill of materials.", "This represents an SPDX Package.", "This represents an SPDX File.", "This represents an SPDX Relationship.", "This represents a Vulnerability Assessment.", "This represents an SBOM Reference."], "type": "string"}}, "type": "object"}, "Distribution": {"description": "This represents a particular channel of distribution for a given package. E.g., Debian's jessie-backports dpkg mirror.", "id": "Distribution", "properties": {"architecture": {"description": "The CPU architecture for which packages in this distribution channel were built.", "enum": ["ARCHITECTURE_UNSPECIFIED", "X86", "X64"], "enumDescriptions": ["Unknown architecture.", "X86 architecture.", "X64 architecture."], "type": "string"}, "cpeUri": {"description": "Required. The cpe_uri in [CPE format](https://cpe.mitre.org/specification/) denoting the package manager version distributing a package.", "type": "string"}, "description": {"description": "The distribution channel-specific description of this package.", "type": "string"}, "latestVersion": {"$ref": "Version", "description": "The latest available version of this package in this distribution channel."}, "maintainer": {"description": "A freeform string denoting the maintainer of this package.", "type": "string"}, "url": {"description": "The distribution channel-specific homepage for this package.", "type": "string"}}, "type": "object"}, "DocumentNote": {"description": "DocumentNote represents an SPDX Document Creation Information section: https://spdx.github.io/spdx-spec/2-document-creation-information/", "id": "DocumentNote", "properties": {"dataLicence": {"description": "Compliance with the SPDX specification includes populating the SPDX fields therein with data related to such fields (\"SPDX-Metadata\")", "type": "string"}, "spdxVersion": {"description": "Provide a reference number that can be used to understand how to parse and interpret the rest of the file", "type": "string"}}, "type": "object"}, "DocumentOccurrence": {"description": "DocumentOccurrence represents an SPDX Document Creation Information section: https://spdx.github.io/spdx-spec/2-document-creation-information/", "id": "DocumentOccurrence", "properties": {"createTime": {"description": "Identify when the SPDX file was originally created. The date is to be specified according to combined date and time in UTC format as specified in ISO 8601 standard", "format": "google-datetime", "type": "string"}, "creatorComment": {"description": "A field for creators of the SPDX file to provide general comments about the creation of the SPDX file or any other relevant comment not included in the other fields", "type": "string"}, "creators": {"description": "Identify who (or what, in the case of a tool) created the SPDX file. If the SPDX file was created by an individual, indicate the person's name", "items": {"type": "string"}, "type": "array"}, "documentComment": {"description": "A field for creators of the SPDX file content to provide comments to the consumers of the SPDX document", "type": "string"}, "externalDocumentRefs": {"description": "Identify any external SPDX documents referenced within this SPDX document", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Identify the current SPDX document which may be referenced in relationships by other files, packages internally and documents externally", "type": "string"}, "licenseListVersion": {"description": "A field for creators of the SPDX file to provide the version of the SPDX License List used when the SPDX file was created", "type": "string"}, "namespace": {"description": "Provide an SPDX document specific namespace as a unique absolute Uniform Resource Identifier (URI) as specified in RFC-3986, with the exception of the ‘#’ delimiter", "type": "string"}, "title": {"description": "Identify name of this document as designated by creator", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Envelope": {"description": "MUST match https://github.com/secure-systems-lab/dsse/blob/master/envelope.proto. An authenticated message of arbitrary type.", "id": "Envelope", "properties": {"payload": {"format": "byte", "type": "string"}, "payloadType": {"type": "string"}, "signatures": {"items": {"$ref": "EnvelopeSignature"}, "type": "array"}}, "type": "object"}, "EnvelopeSignature": {"id": "EnvelopeSignature", "properties": {"keyid": {"type": "string"}, "sig": {"format": "byte", "type": "string"}}, "type": "object"}, "Environment": {"description": "Defines an object for the environment field in in-toto links. The suggested fields are \"variables\", \"filesystem\", and \"workdir\".", "id": "Environment", "properties": {"customValues": {"additionalProperties": {"type": "string"}, "type": "object"}}, "type": "object"}, "ExportSBOMRequest": {"description": "The request to a call of ExportSBOM", "id": "ExportSBOMRequest", "properties": {}, "type": "object"}, "ExportSBOMResponse": {"description": "The response from a call to ExportSBOM", "id": "ExportSBOMResponse", "properties": {"discoveryOccurrenceId": {"description": "The name of the discovery occurrence in the form \"projects/{project_id}/occurrences/{OCCURRENCE_ID} It can be used to track the progression of the SBOM export.", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ExternalRef": {"description": "An External Reference allows a Package to reference an external source of additional information, metadata, enumerations, asset identifiers, or downloadable content believed to be relevant to the Package", "id": "ExternalRef", "properties": {"category": {"description": "An External Reference allows a Package to reference an external source of additional information, metadata, enumerations, asset identifiers, or downloadable content believed to be relevant to the Package", "enum": ["CATEGORY_UNSPECIFIED", "SECURITY", "PACKAGE_MANAGER", "PERSISTENT_ID", "OTHER"], "enumDescriptions": ["Unspecified", "Security (e.g. cpe22Type, cpe23Type)", "Package Manager (e.g. maven-central, npm, nuget, bower, purl)", "Persistent-Id (e.g. swh)", "Other"], "type": "string"}, "comment": {"description": "Human-readable information about the purpose and target of the reference", "type": "string"}, "locator": {"description": "The unique string with no spaces necessary to access the package-specific information, metadata, or content within the target location", "type": "string"}, "type": {"description": "Type of category (e.g. 'npm' for the PACKAGE_MANAGER category)", "type": "string"}}, "type": "object"}, "File": {"id": "File", "properties": {"digest": {"additionalProperties": {"type": "string"}, "type": "object"}, "name": {"type": "string"}}, "type": "object"}, "FileHashes": {"description": "Container message for hashes of byte content of files, used in source messages to verify integrity of source input to the build.", "id": "FileHashes", "properties": {"fileHash": {"description": "Required. Collection of file hashes.", "items": {"$ref": "Hash"}, "type": "array"}}, "type": "object"}, "FileNote": {"description": "FileNote represents an SPDX File Information section: https://spdx.github.io/spdx-spec/4-file-information/", "id": "FileNote", "properties": {"checksum": {"description": "Provide a unique identifier to match analysis information on each specific file in a package", "items": {"type": "string"}, "type": "array"}, "fileType": {"description": "This field provides information about the type of file identified", "enum": ["FILE_TYPE_UNSPECIFIED", "SOURCE", "BINARY", "ARCHIVE", "APPLICATION", "AUDIO", "IMAGE", "TEXT", "VIDEO", "DOCUMENTATION", "SPDX", "OTHER"], "enumDescriptions": ["Unspecified", "The file is human readable source code (.c, .html, etc.)", "The file is a compiled object, target image or binary executable (.o, .a, etc.)", "The file represents an archive (.tar, .jar, etc.)", "The file is associated with a specific application type (MIME type of application/*)", "The file is associated with an audio file (MIME type of audio/* , e.g. .mp3)", "The file is associated with an picture image file (MIME type of image/*, e.g., .jpg, .gif)", "The file is human readable text file (MIME type of text/*)", "The file is associated with a video file type (MIME type of video/*)", "The file serves as documentation", "The file is an SPDX document", "The file doesn't fit into the above categories (generated artifacts, data files, etc.)"], "type": "string"}, "title": {"description": "Identify the full path and filename that corresponds to the file information in this section", "type": "string"}}, "type": "object"}, "FileOccurrence": {"description": "FileOccurrence represents an SPDX File Information section: https://spdx.github.io/spdx-spec/4-file-information/", "id": "FileOccurrence", "properties": {"attributions": {"description": "This field provides a place for the SPDX data creator to record, at the file level, acknowledgements that may be needed to be communicated in some contexts", "items": {"type": "string"}, "type": "array"}, "comment": {"description": "This field provides a place for the SPDX file creator to record any general comments about the file", "type": "string"}, "contributors": {"description": "This field provides a place for the SPDX file creator to record file contributors", "items": {"type": "string"}, "type": "array"}, "copyright": {"description": "Identify the copyright holder of the file, as well as any dates present", "type": "string"}, "filesLicenseInfo": {"description": "This field contains the license information actually found in the file, if any", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Uniquely identify any element in an SPDX document which may be referenced by other elements", "type": "string"}, "licenseConcluded": {"$ref": "License", "description": "This field contains the license the SPDX file creator has concluded as governing the file or alternative values if the governing license cannot be determined"}, "notice": {"description": "This field provides a place for the SPDX file creator to record license notices or other such related notices found in the file", "type": "string"}}, "type": "object"}, "Fingerprint": {"description": "A set of properties that uniquely identify a given <PERSON><PERSON> image.", "id": "Fingerprint", "properties": {"v1Name": {"description": "Required. The layer ID of the final layer in the Docker image's v1 representation.", "type": "string"}, "v2Blob": {"description": "Required. The ordered list of v2 blobs that represent a given image.", "items": {"type": "string"}, "type": "array"}, "v2Name": {"description": "Output only. The name of the image's v2 blobs computed via: [bottom] := v2_blobbottom := sha256(v2_blob[N] + \" \" + v2_name[N+1]) Only the name of the final blob is kept.", "type": "string"}}, "type": "object"}, "FixableTotalByDigest": {"description": "Per resource and severity counts of fixable and total vulnerabilities.", "id": "FixableTotalByDigest", "properties": {"fixableCount": {"description": "The number of fixable vulnerabilities associated with this resource.", "format": "int64", "type": "string"}, "resource": {"$ref": "Resource", "description": "The affected resource."}, "severity": {"description": "The severity for this count. SEVERITY_UNSPECIFIED indicates total across all severities.", "enum": ["SEVERITY_UNSPECIFIED", "MINIMAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Unknown.", "Minimal severity.", "Low severity.", "Medium severity.", "High severity.", "Critical severity."], "type": "string"}, "totalCount": {"description": "The total number of vulnerabilities associated with this resource.", "format": "int64", "type": "string"}}, "type": "object"}, "GeneratePackagesSummaryRequest": {"description": "GeneratePackagesSummaryRequest is the request body for the GeneratePackagesSummary API method. It just takes a single name argument, referring to the resource.", "id": "GeneratePackagesSummaryRequest", "properties": {}, "type": "object"}, "GenericSignedAttestation": {"description": "An attestation wrapper that uses the Grafeas `Signature` message. This attestation must define the `serialized_payload` that the `signatures` verify and any metadata necessary to interpret that plaintext. The signatures should always be over the `serialized_payload` bytestring.", "id": "GenericSignedAttestation", "properties": {"contentType": {"description": "Type (for example schema) of the attestation payload that was signed. The verifier must ensure that the provided type is one that the verifier supports, and that the attestation payload is a valid instantiation of that type (for example by validating a JSON schema).", "enum": ["CONTENT_TYPE_UNSPECIFIED", "SIMPLE_SIGNING_JSON"], "enumDescriptions": ["`ContentType` is not set.", "Atomic format attestation signature. See https://github.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e7/docs/atomic-signature.md The payload extracted in `plaintext` is a JSON blob conforming to the linked schema."], "type": "string"}, "serializedPayload": {"description": "The serialized payload that is verified by one or more `signatures`. The encoding and semantic meaning of this payload must match what is set in `content_type`.", "format": "byte", "type": "string"}, "signatures": {"description": "One or more signatures over `serialized_payload`. Verifier implementations should consider this attestation message verified if at least one `signature` verifies `serialized_payload`. See `Signature` in common.proto for more details on signature structure and verification.", "items": {"$ref": "Signature"}, "type": "array"}}, "type": "object"}, "GerritSourceContext": {"description": "A SourceContext referring to a Gerrit project.", "id": "GerritSourceContext", "properties": {"aliasContext": {"$ref": "AliasContext", "description": "An alias, which may be a branch or tag."}, "gerritProject": {"description": "The full project name within the host. Projects may be nested, so \"project/subproject\" is a valid project name. The \"repo name\" is the hostURI/project.", "type": "string"}, "hostUri": {"description": "The URI of a running Gerrit instance.", "type": "string"}, "revisionId": {"description": "A revision (commit) ID.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GitSourceContext": {"description": "A GitSourceContext denotes a particular revision in a third party Git repository (e.g., GitHub).", "id": "GitSourceContext", "properties": {"revisionId": {"description": "Git commit hash.", "type": "string"}, "url": {"description": "Git repository URL.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsContaineranalysisV1alpha1OperationMetadata": {"description": "Metadata for all operations used and required for all operations that created by Container Analysis Providers", "id": "GoogleDevtoolsContaineranalysisV1alpha1OperationMetadata", "properties": {"createTime": {"description": "Output only. The time this operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "Output only. The time that this operation was marked completed or failed.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GrafeasV1beta1BuildDetails": {"description": "Details of a build occurrence.", "id": "GrafeasV1beta1BuildDetails", "properties": {"inTotoSlsaProvenanceV1": {"$ref": "InTotoSlsaProvenanceV1"}, "provenance": {"$ref": "BuildProvenance", "description": "Required. The actual provenance for the build."}, "provenanceBytes": {"description": "Serialized JSON representation of the provenance, used in generating the build signature in the corresponding build note. After verifying the signature, `provenance_bytes` can be unmarshalled and compared to the provenance to confirm that it is unchanged. A base64-encoded string representation of the provenance bytes is used for the signature in order to interoperate with openssl which expects this format for signature verification. The serialized form is captured both to avoid ambiguity in how the provenance is marshalled to json as well to prevent incompatibilities with future changes.", "type": "string"}}, "type": "object"}, "GrafeasV1beta1DeploymentDetails": {"description": "Details of a deployment occurrence.", "id": "GrafeasV1beta1DeploymentDetails", "properties": {"deployment": {"$ref": "Deployment", "description": "Required. Deployment history for the resource."}}, "type": "object"}, "GrafeasV1beta1DiscoveryDetails": {"description": "Details of a discovery occurrence.", "id": "GrafeasV1beta1DiscoveryDetails", "properties": {"discovered": {"$ref": "Discovered", "description": "Required. Analysis status for the discovered resource."}}, "type": "object"}, "GrafeasV1beta1ImageDetails": {"description": "Details of an image occurrence.", "id": "GrafeasV1beta1ImageDetails", "properties": {"derivedImage": {"$ref": "Derived", "description": "Required. Immutable. The child image derived from the base image."}}, "type": "object"}, "GrafeasV1beta1IntotoArtifact": {"id": "GrafeasV1beta1IntotoArtifact", "properties": {"hashes": {"$ref": "ArtifactHashes"}, "resourceUri": {"type": "string"}}, "type": "object"}, "GrafeasV1beta1IntotoDetails": {"description": "This corresponds to a signed in-toto link - it is made up of one or more signatures and the in-toto link itself. This is used for occurrences of a Grafeas in-toto note.", "id": "GrafeasV1beta1IntotoDetails", "properties": {"signatures": {"items": {"$ref": "GrafeasV1beta1IntotoSignature"}, "type": "array"}, "signed": {"$ref": "Link"}}, "type": "object"}, "GrafeasV1beta1IntotoSignature": {"description": "A signature object consists of the KeyID used and the signature itself.", "id": "GrafeasV1beta1IntotoSignature", "properties": {"keyid": {"type": "string"}, "sig": {"type": "string"}}, "type": "object"}, "GrafeasV1beta1PackageDetails": {"description": "Details of a package occurrence.", "id": "GrafeasV1beta1PackageDetails", "properties": {"installation": {"$ref": "Installation", "description": "Required. Where the package was installed."}}, "type": "object"}, "GrafeasV1beta1VulnerabilityDetails": {"description": "Details of a vulnerability Occurrence.", "id": "GrafeasV1beta1VulnerabilityDetails", "properties": {"cvssScore": {"description": "Output only. The CVSS score of this vulnerability. CVSS score is on a scale of 0-10 where 0 indicates low severity and 10 indicates high severity.", "format": "float", "type": "number"}, "cvssV2": {"$ref": "CVSS", "description": "The cvss v2 score for the vulnerability."}, "cvssV3": {"$ref": "CVSS", "description": "The cvss v3 score for the vulnerability."}, "cvssVersion": {"description": "Output only. CVSS version used to populate cvss_score and severity.", "enum": ["CVSS_VERSION_UNSPECIFIED", "CVSS_VERSION_2", "CVSS_VERSION_3"], "enumDescriptions": ["", "", ""], "type": "string"}, "effectiveSeverity": {"description": "The distro assigned severity for this vulnerability when it is available, and note provider assigned severity when distro has not yet assigned a severity for this vulnerability. When there are multiple PackageIssues for this vulnerability, they can have different effective severities because some might be provided by the distro while others are provided by the language ecosystem for a language pack. For this reason, it is advised to use the effective severity on the PackageIssue level. In the case where multiple PackageIssues have differing effective severities, this field should be the highest severity for any of the PackageIssues.", "enum": ["SEVERITY_UNSPECIFIED", "MINIMAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Unknown.", "Minimal severity.", "Low severity.", "Medium severity.", "High severity.", "Critical severity."], "type": "string"}, "extraDetails": {"description": "Occurrence-specific extra details about the vulnerability.", "type": "string"}, "longDescription": {"description": "Output only. A detailed description of this vulnerability.", "type": "string"}, "packageIssue": {"description": "Required. The set of affected locations and their fixes (if available) within the associated resource.", "items": {"$ref": "PackageIssue"}, "type": "array"}, "relatedUrls": {"description": "Output only. URLs related to this vulnerability.", "items": {"$ref": "RelatedUrl"}, "type": "array"}, "severity": {"description": "Output only. The note provider assigned Severity of the vulnerability.", "enum": ["SEVERITY_UNSPECIFIED", "MINIMAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Unknown.", "Minimal severity.", "Low severity.", "Medium severity.", "High severity.", "Critical severity."], "type": "string"}, "shortDescription": {"description": "Output only. A one sentence description of this vulnerability.", "type": "string"}, "type": {"description": "The type of package; whether native or non native(ruby gems, node.js packages etc)", "type": "string"}, "vexAssessment": {"$ref": "VexAssessment"}}, "type": "object"}, "Hash": {"description": "Container message for hash values.", "id": "Hash", "properties": {"type": {"description": "Required. The type of hash that was performed.", "enum": ["HASH_TYPE_UNSPECIFIED", "SHA256", "GO_MODULE_H1", "SHA512"], "enumDescriptions": ["Unknown.", "A SHA-256 hash.", "Dirhash of a Go module's source code which is then hex-encoded. See b/244466565 and https://github.com/in-toto/attestation/pull/108.", "A SHA-512 hash."], "type": "string"}, "value": {"description": "Required. The hash value.", "format": "byte", "type": "string"}}, "type": "object"}, "Hint": {"description": "This submessage provides human-readable hints about the purpose of the authority. Because the name of a note acts as its resource reference, it is important to disambiguate the canonical name of the Note (which might be a UUID for security purposes) from \"readable\" names more suitable for debug output. Note that these hints should not be used to look up authorities in security sensitive contexts, such as when looking up attestations to verify.", "id": "Hint", "properties": {"humanReadableName": {"description": "Required. The human readable name of this attestation authority, for example \"qa\".", "type": "string"}}, "type": "object"}, "InToto": {"description": "This contains the fields corresponding to the definition of a software supply chain step in an in-toto layout. This information goes into a Grafeas note.", "id": "InToto", "properties": {"expectedCommand": {"description": "This field contains the expected command used to perform the step.", "items": {"type": "string"}, "type": "array"}, "expectedMaterials": {"description": "The following fields contain in-toto artifact rules identifying the artifacts that enter this supply chain step, and exit the supply chain step, i.e. materials and products of the step.", "items": {"$ref": "ArtifactRule"}, "type": "array"}, "expectedProducts": {"items": {"$ref": "ArtifactRule"}, "type": "array"}, "signingKeys": {"description": "This field contains the public keys that can be used to verify the signatures on the step metadata.", "items": {"$ref": "SigningKey"}, "type": "array"}, "stepName": {"description": "This field identifies the name of the step in the supply chain.", "type": "string"}, "threshold": {"description": "This field contains a value that indicates the minimum number of keys that need to be used to sign the step's in-toto link.", "format": "int64", "type": "string"}}, "type": "object"}, "InTotoSlsaProvenanceV1": {"id": "InTotoSlsaProvenanceV1", "properties": {"_type": {"description": "InToto spec defined at https://github.com/in-toto/attestation/tree/main/spec#statement", "type": "string"}, "predicate": {"$ref": "SlsaProvenanceV1"}, "predicateType": {"type": "string"}, "subject": {"items": {"$ref": "Subject"}, "type": "array"}}, "type": "object"}, "Installation": {"description": "This represents how a particular software package may be installed on a system.", "id": "Installation", "properties": {"architecture": {"description": "Output only. The CPU architecture for which packages in this distribution channel were built. Architecture will be blank for language packages.", "enum": ["ARCHITECTURE_UNSPECIFIED", "X86", "X64"], "enumDescriptions": ["Unknown architecture.", "X86 architecture.", "X64 architecture."], "readOnly": true, "type": "string"}, "cpeUri": {"description": "Output only. The cpe_uri in [CPE format](https://cpe.mitre.org/specification/) denoting the package manager version distributing a package. The cpe_uri will be blank for language packages.", "readOnly": true, "type": "string"}, "license": {"$ref": "License", "description": "Licenses that have been declared by the authors of the package."}, "location": {"description": "All of the places within the filesystem versions of this package have been found.", "items": {"$ref": "Location"}, "type": "array"}, "name": {"description": "Required. Output only. The name of the installed package.", "readOnly": true, "type": "string"}, "packageType": {"description": "Output only. The type of package; whether native or non native (e.g., ruby gems, node.js packages, etc.).", "readOnly": true, "type": "string"}, "version": {"$ref": "Version", "description": "Output only. The version of the package.", "readOnly": true}}, "type": "object"}, "Justification": {"description": "Justification provides the justification when the state of the assessment if NOT_AFFECTED.", "id": "Justification", "properties": {"details": {"description": "Additional details on why this justification was chosen.", "type": "string"}, "justificationType": {"description": "The justification type for this vulnerability.", "enum": ["JUSTIFICATION_TYPE_UNSPECIFIED", "COMPONENT_NOT_PRESENT", "VULNERABLE_CODE_NOT_PRESENT", "VULNERABLE_CODE_NOT_IN_EXECUTE_PATH", "VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY", "INLINE_MITIGATIONS_ALREADY_EXIST"], "enumDescriptions": ["JUSTIFICATION_TYPE_UNSPECIFIED.", "The vulnerable component is not present in the product.", "The vulnerable code is not present. Typically this case occurs when source code is configured or built in a way that excludes the vulnerable code.", "The vulnerable code can not be executed. Typically this case occurs when the product includes the vulnerable code but does not call or use the vulnerable code.", "The vulnerable code cannot be controlled by an attacker to exploit the vulnerability.", "The product includes built-in protections or features that prevent exploitation of the vulnerability. These built-in protections cannot be subverted by the attacker and cannot be configured or disabled by the user. These mitigations completely prevent exploitation based on known attack vectors."], "type": "string"}}, "type": "object"}, "KnowledgeBase": {"id": "KnowledgeBase", "properties": {"name": {"description": "The KB name (generally of the form KB[0-9]+ i.e. KB123456).", "type": "string"}, "url": {"description": "A link to the KB in the Windows update catalog - https://www.catalog.update.microsoft.com/", "type": "string"}}, "type": "object"}, "Layer": {"description": "Layer holds metadata specific to a layer of a Docker image.", "id": "Layer", "properties": {"arguments": {"description": "The recovered arguments to the Dockerfile directive.", "type": "string"}, "directive": {"description": "Required. The recovered Dockerfile directive used to construct this layer.", "enum": ["DIRECTIVE_UNSPECIFIED", "MAINTAINER", "RUN", "CMD", "LABEL", "EXPOSE", "ENV", "ADD", "COPY", "ENTRYPOINT", "VOLUME", "USER", "WORKDIR", "ARG", "ONBUILD", "STOPSIGNAL", "HEALTHCHECK", "SHELL"], "enumDescriptions": ["Default value for unsupported/missing directive.", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/", "https://docs.docker.com/engine/reference/builder/"], "type": "string"}}, "type": "object"}, "License": {"description": "License information.", "id": "License", "properties": {"comments": {"description": "Comments", "type": "string"}, "expression": {"description": "Often a single license can be used to represent the licensing terms. Sometimes it is necessary to include a choice of one or more licenses or some combination of license identifiers. Examples: \"LGPL-2.1-only OR MIT\", \"LGPL-2.1-only AND MIT\", \"GPL-2.0-or-later WITH Bison-exception-2.2\".", "type": "string"}}, "type": "object"}, "LicensesSummary": {"description": "Per license count", "id": "LicensesSummary", "properties": {"count": {"description": "The number of fixable vulnerabilities associated with this resource.", "format": "int64", "type": "string"}, "license": {"description": "The license of the package. Note that the format of this value is not guaranteed. It may be nil, an empty string, a boolean value (A | B), a differently formed boolean value (A OR B), etc...", "type": "string"}}, "type": "object"}, "Link": {"description": "This corresponds to an in-toto link.", "id": "Link", "properties": {"byproducts": {"$ref": "ByProducts", "description": "ByProducts are data generated as part of a software supply chain step, but are not the actual result of the step."}, "command": {"description": "This field contains the full command executed for the step. This can also be empty if links are generated for operations that aren't directly mapped to a specific command. Each term in the command is an independent string in the list. An example of a command in the in-toto metadata field is: \"command\": [\"git\", \"clone\", \"https://github.com/in-toto/demo-project.git\"]", "items": {"type": "string"}, "type": "array"}, "environment": {"$ref": "Environment", "description": "This is a field that can be used to capture information about the environment. It is suggested for this field to contain information that details environment variables, filesystem information, and the present working directory. The recommended structure of this field is: \"environment\": { \"custom_values\": { \"variables\": \"\", \"filesystem\": \"\", \"workdir\": \"\", \"\": \"...\" } }"}, "materials": {"description": "Materials are the supply chain artifacts that go into the step and are used for the operation performed. The key of the map is the path of the artifact and the structure contains the recorded hash information. An example is: \"materials\": [ { \"resource_uri\": \"foo/bar\", \"hashes\": { \"sha256\": \"ebebf...\", : } } ]", "items": {"$ref": "GrafeasV1beta1IntotoArtifact"}, "type": "array"}, "products": {"description": "Products are the supply chain artifacts generated as a result of the step. The structure is identical to that of materials.", "items": {"$ref": "GrafeasV1beta1IntotoArtifact"}, "type": "array"}}, "type": "object"}, "ListNoteOccurrencesResponse": {"description": "Response for listing occurrences for a note.", "id": "ListNoteOccurrencesResponse", "properties": {"nextPageToken": {"description": "Token to provide to skip to a particular spot in the list.", "type": "string"}, "occurrences": {"description": "The occurrences attached to the specified note.", "items": {"$ref": "Occurrence"}, "type": "array"}}, "type": "object"}, "ListNotesResponse": {"description": "Response for listing notes.", "id": "ListNotesResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the list response. It should be used as `page_token` for the following request. An empty value means no more results.", "type": "string"}, "notes": {"description": "The notes requested.", "items": {"$ref": "Note"}, "type": "array"}, "unreachable": {"description": "Unordered list. Unreachable regions. Populated for requests from the global region when `return_partial_success` is set. Format: projects//locations/", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOccurrencesResponse": {"description": "Response for listing occurrences.", "id": "ListOccurrencesResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the list response. It should be used as `page_token` for the following request. An empty value means no more results.", "type": "string"}, "occurrences": {"description": "The occurrences requested.", "items": {"$ref": "Occurrence"}, "type": "array"}, "unreachable": {"description": "Unordered list. Unreachable regions. Populated for requests from the global region when `return_partial_success` is set. Format: projects//locations/", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "An occurrence of a particular package installation found within a system's filesystem. E.g., glibc was found in `/var/lib/dpkg/status`.", "id": "Location", "properties": {"cpeUri": {"description": "Deprecated. The CPE URI in [CPE format](https://cpe.mitre.org/specification/) denoting the package manager version distributing a package.", "type": "string"}, "path": {"description": "The path from which we gathered that this package/version is installed.", "type": "string"}, "version": {"$ref": "Version", "description": "Deprecated. The version installed at this location."}}, "type": "object"}, "Note": {"description": "A type of analysis that can be done for a resource.", "id": "Note", "properties": {"attestationAuthority": {"$ref": "Authority", "description": "A note describing an attestation role."}, "baseImage": {"$ref": "<PERSON><PERSON>", "description": "A note describing a base image."}, "build": {"$ref": "Build", "description": "A note describing build provenance for a verifiable build."}, "createTime": {"description": "Output only. The time this note was created. This field can be used as a filter in list requests.", "format": "google-datetime", "type": "string"}, "deployable": {"$ref": "Deployable", "description": "A note describing something that can be deployed."}, "discovery": {"$ref": "Discovery", "description": "A note describing the initial analysis of a resource."}, "expirationTime": {"description": "Time of expiration for this note. Empty if note does not expire.", "format": "google-datetime", "type": "string"}, "intoto": {"$ref": "InToto", "description": "A note describing an in-toto link."}, "kind": {"description": "Output only. The type of analysis. This field can be used as a filter in list requests.", "enum": ["NOTE_KIND_UNSPECIFIED", "VULNERABILITY", "BUILD", "IMAGE", "PACKAGE", "DEPLOYMENT", "DISCOVERY", "ATTESTATION", "INTOTO", "SBOM", "SPDX_PACKAGE", "SPDX_FILE", "SPDX_RELATIONSHIP", "VULNERABILITY_ASSESSMENT", "SBOM_REFERENCE"], "enumDescriptions": ["Default value. This value is unused.", "The note and occurrence represent a package vulnerability.", "The note and occurrence assert build provenance.", "This represents an image basis relationship.", "This represents a package installed via a package manager.", "The note and occurrence track deployment events.", "The note and occurrence track the initial discovery status of a resource.", "This represents a logical \"role\" that can attest to artifacts.", "This represents an in-toto link.", "This represents a software bill of materials.", "This represents an SPDX Package.", "This represents an SPDX File.", "This represents an SPDX Relationship.", "This represents a Vulnerability Assessment.", "This represents an SBOM Reference."], "type": "string"}, "longDescription": {"description": "A detailed description of this note.", "type": "string"}, "name": {"description": "Output only. The name of the note in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "type": "string"}, "package": {"$ref": "Package", "description": "A note describing a package hosted by various package managers."}, "relatedNoteNames": {"description": "Other notes related to this note.", "items": {"type": "string"}, "type": "array"}, "relatedUrl": {"description": "URLs associated with this note.", "items": {"$ref": "RelatedUrl"}, "type": "array"}, "sbom": {"$ref": "DocumentNote", "description": "A note describing a software bill of materials."}, "sbomReference": {"$ref": "SBOMReferenceNote", "description": "A note describing an SBOM reference."}, "shortDescription": {"description": "A one sentence description of this note.", "type": "string"}, "spdxFile": {"$ref": "FileNote", "description": "A note describing an SPDX File."}, "spdxPackage": {"$ref": "PackageInfoNote", "description": "A note describing an SPDX Package."}, "spdxRelationship": {"$ref": "RelationshipNote", "description": "A note describing an SPDX File."}, "updateTime": {"description": "Output only. The time this note was last updated. This field can be used as a filter in list requests.", "format": "google-datetime", "type": "string"}, "vulnerability": {"$ref": "Vulnerability", "description": "A note describing a package vulnerability."}, "vulnerabilityAssessment": {"$ref": "VulnerabilityAssessmentNote", "description": "A note describing a vulnerability assessment."}}, "type": "object"}, "Occurrence": {"description": "An instance of an analysis type that has been found on a resource.", "id": "Occurrence", "properties": {"attestation": {"$ref": "Details", "description": "Describes an attestation of an artifact."}, "build": {"$ref": "GrafeasV1beta1BuildDetails", "description": "Describes a verifiable build."}, "createTime": {"description": "Output only. The time this occurrence was created.", "format": "google-datetime", "type": "string"}, "deployment": {"$ref": "GrafeasV1beta1DeploymentDetails", "description": "Describes the deployment of an artifact on a runtime."}, "derivedImage": {"$ref": "GrafeasV1beta1ImageDetails", "description": "Describes how this resource derives from the basis in the associated note."}, "discovered": {"$ref": "GrafeasV1beta1DiscoveryDetails", "description": "Describes when a resource was discovered."}, "envelope": {"$ref": "Envelope", "description": "https://github.com/secure-systems-lab/dsse"}, "installation": {"$ref": "GrafeasV1beta1PackageDetails", "description": "Describes the installation of a package on the linked resource."}, "intoto": {"$ref": "GrafeasV1beta1IntotoDetails", "description": "Describes a specific in-toto link."}, "kind": {"description": "Output only. This explicitly denotes which of the occurrence details are specified. This field can be used as a filter in list requests.", "enum": ["NOTE_KIND_UNSPECIFIED", "VULNERABILITY", "BUILD", "IMAGE", "PACKAGE", "DEPLOYMENT", "DISCOVERY", "ATTESTATION", "INTOTO", "SBOM", "SPDX_PACKAGE", "SPDX_FILE", "SPDX_RELATIONSHIP", "VULNERABILITY_ASSESSMENT", "SBOM_REFERENCE"], "enumDescriptions": ["Default value. This value is unused.", "The note and occurrence represent a package vulnerability.", "The note and occurrence assert build provenance.", "This represents an image basis relationship.", "This represents a package installed via a package manager.", "The note and occurrence track deployment events.", "The note and occurrence track the initial discovery status of a resource.", "This represents a logical \"role\" that can attest to artifacts.", "This represents an in-toto link.", "This represents a software bill of materials.", "This represents an SPDX Package.", "This represents an SPDX File.", "This represents an SPDX Relationship.", "This represents a Vulnerability Assessment.", "This represents an SBOM Reference."], "type": "string"}, "name": {"description": "Output only. The name of the occurrence in the form of `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "type": "string"}, "noteName": {"description": "Required. Immutable. The analysis note associated with this occurrence, in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`. This field can be used as a filter in list requests.", "type": "string"}, "remediation": {"description": "A description of actions that can be taken to remedy the note.", "type": "string"}, "resource": {"$ref": "Resource", "description": "Required. Immutable. The resource for which the occurrence applies."}, "sbom": {"$ref": "DocumentOccurrence", "description": "Describes a specific software bill of materials document."}, "sbomReference": {"$ref": "SBOMReferenceOccurrence", "description": "Describes a specific SBOM reference occurrences."}, "spdxFile": {"$ref": "FileOccurrence", "description": "Describes a specific SPDX File."}, "spdxPackage": {"$ref": "PackageInfoOccurrence", "description": "Describes a specific SPDX Package."}, "spdxRelationship": {"$ref": "RelationshipOccurrence", "description": "Describes a specific SPDX Relationship."}, "updateTime": {"description": "Output only. The time this occurrence was last updated.", "format": "google-datetime", "type": "string"}, "vulnerability": {"$ref": "GrafeasV1beta1VulnerabilityDetails", "description": "Describes a security vulnerability."}}, "type": "object"}, "Package": {"description": "Package represents a particular package version.", "id": "Package", "properties": {"architecture": {"description": "The CPU architecture for which packages in this distribution channel were built. Architecture will be blank for language packages.", "enum": ["ARCHITECTURE_UNSPECIFIED", "X86", "X64"], "enumDescriptions": ["Unknown architecture.", "X86 architecture.", "X64 architecture."], "type": "string"}, "cpeUri": {"description": "The cpe_uri in [CPE format](https://cpe.mitre.org/specification/) denoting the package manager version distributing a package. The cpe_uri will be blank for language packages.", "type": "string"}, "description": {"description": "The description of this package.", "type": "string"}, "digest": {"description": "Hash value, typically a file digest, that allows unique identification a specific package.", "items": {"$ref": "Digest"}, "type": "array"}, "distribution": {"description": "The various channels by which a package is distributed.", "items": {"$ref": "Distribution"}, "type": "array"}, "license": {"$ref": "License", "description": "Licenses that have been declared by the authors of the package."}, "maintainer": {"description": "A freeform text denoting the maintainer of this package.", "type": "string"}, "name": {"description": "Required. Immutable. The name of the package.", "type": "string"}, "packageType": {"description": "The type of package; whether native or non native (e.g., ruby gems, node.js packages, etc.).", "type": "string"}, "url": {"description": "The homepage for this package.", "type": "string"}, "version": {"$ref": "Version", "description": "The version of the package."}}, "type": "object"}, "PackageInfoNote": {"description": "PackageInfoNote represents an SPDX Package Information section: https://spdx.github.io/spdx-spec/3-package-information/", "id": "PackageInfoNote", "properties": {"analyzed": {"description": "Indicates whether the file content of this package has been available for or subjected to analysis when creating the SPDX document", "type": "boolean"}, "attribution": {"description": "A place for the SPDX data creator to record, at the package level, acknowledgements that may be needed to be communicated in some contexts", "type": "string"}, "checksum": {"description": "Provide an independently reproducible mechanism that permits unique identification of a specific package that correlates to the data in this SPDX file", "type": "string"}, "copyright": {"description": "Identify the copyright holders of the package, as well as any dates present", "type": "string"}, "detailedDescription": {"description": "A more detailed description of the package", "type": "string"}, "downloadLocation": {"description": "This section identifies the download Universal Resource Locator (URL), or a specific location within a version control system (VCS) for the package at the time that the SPDX file was created", "type": "string"}, "externalRefs": {"description": "ExternalRef", "items": {"$ref": "ExternalRef"}, "type": "array"}, "filesLicenseInfo": {"description": "Contain the license the SPDX file creator has concluded as governing the This field is to contain a list of all licenses found in the package. The relationship between licenses (i.e., conjunctive, disjunctive) is not specified in this field – it is simply a listing of all licenses found", "items": {"type": "string"}, "type": "array"}, "homePage": {"description": "Provide a place for the SPDX file creator to record a web site that serves as the package's home page", "type": "string"}, "licenseDeclared": {"$ref": "License", "description": "List the licenses that have been declared by the authors of the package"}, "originator": {"description": "If the package identified in the SPDX file originated from a different person or organization than identified as Package Supplier, this field identifies from where or whom the package originally came", "type": "string"}, "packageType": {"description": "The type of package: OS, MAVEN, GO, GO_STDLIB, etc.", "type": "string"}, "summaryDescription": {"description": "A short description of the package", "type": "string"}, "supplier": {"description": "Identify the actual distribution source for the package/directory identified in the SPDX file", "type": "string"}, "title": {"description": "Identify the full name of the package as given by the Package Originator", "type": "string"}, "verificationCode": {"description": "This field provides an independently reproducible mechanism identifying specific contents of a package based on the actual files (except the SPDX file itself, if it is included in the package) that make up each package and that correlates to the data in this SPDX file", "type": "string"}, "version": {"description": "Identify the version of the package", "type": "string"}}, "type": "object"}, "PackageInfoOccurrence": {"description": "PackageInfoOccurrence represents an SPDX Package Information section: https://spdx.github.io/spdx-spec/3-package-information/", "id": "PackageInfoOccurrence", "properties": {"comment": {"description": "A place for the SPDX file creator to record any general comments about the package being described", "type": "string"}, "filename": {"description": "Provide the actual file name of the package, or path of the directory being treated as a package", "type": "string"}, "homePage": {"description": "Output only. Provide a place for the SPDX file creator to record a web site that serves as the package's home page", "readOnly": true, "type": "string"}, "id": {"description": "Uniquely identify any element in an SPDX document which may be referenced by other elements", "type": "string"}, "licenseConcluded": {"$ref": "License", "description": "package or alternative values, if the governing license cannot be determined"}, "packageType": {"description": "Output only. The type of package: OS, MAVEN, GO, GO_STDLIB, etc.", "readOnly": true, "type": "string"}, "sourceInfo": {"description": "Provide a place for the SPDX file creator to record any relevant background information or additional comments about the origin of the package", "type": "string"}, "summaryDescription": {"description": "Output only. A short description of the package", "readOnly": true, "type": "string"}, "title": {"description": "Output only. Identify the full name of the package as given by the Package Originator", "readOnly": true, "type": "string"}, "version": {"description": "Output only. Identify the version of the package", "readOnly": true, "type": "string"}}, "type": "object"}, "PackageIssue": {"description": "This message wraps a location affected by a vulnerability and its associated fix (if one is available).", "id": "PackageIssue", "properties": {"affectedLocation": {"$ref": "VulnerabilityLocation", "description": "Required. The location of the vulnerability."}, "effectiveSeverity": {"description": "Output only. The distro or language system assigned severity for this vulnerability when that is available and note provider assigned severity when it is not available.", "enum": ["SEVERITY_UNSPECIFIED", "MINIMAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Unknown.", "Minimal severity.", "Low severity.", "Medium severity.", "High severity.", "Critical severity."], "readOnly": true, "type": "string"}, "fixedLocation": {"$ref": "VulnerabilityLocation", "description": "The location of the available fix for vulnerability."}, "packageType": {"description": "The type of package (e.g. OS, MAVEN, GO).", "type": "string"}, "severityName": {"description": "Deprecated, use Details.effective_severity instead The severity (e.g., distro assigned severity) for this vulnerability.", "type": "string"}}, "type": "object"}, "PackagesSummaryResponse": {"description": "A summary of the packages found within the given resource.", "id": "PackagesSummaryResponse", "properties": {"licensesSummary": {"description": "A listing by license name of each of the licenses and their counts.", "items": {"$ref": "LicensesSummary"}, "type": "array"}, "resourceUrl": {"description": "The unique URL of the image or the container for which this summary applies.", "type": "string"}}, "type": "object"}, "PgpSignedAttestation": {"description": "An attestation wrapper with a PGP-compatible signature. This message only supports `ATTACHED` signatures, where the payload that is signed is included alongside the signature itself in the same file.", "id": "PgpSignedAttestation", "properties": {"contentType": {"description": "Type (for example schema) of the attestation payload that was signed. The verifier must ensure that the provided type is one that the verifier supports, and that the attestation payload is a valid instantiation of that type (for example by validating a JSON schema).", "enum": ["CONTENT_TYPE_UNSPECIFIED", "SIMPLE_SIGNING_JSON"], "enumDescriptions": ["`ContentType` is not set.", "Atomic format attestation signature. See https://github.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e7/docs/atomic-signature.md The payload extracted from `signature` is a JSON blob conforming to the linked schema."], "type": "string"}, "pgpKeyId": {"description": "The cryptographic fingerprint of the key used to generate the signature, as output by, e.g. `gpg --list-keys`. This should be the version 4, full 160-bit fingerprint, expressed as a 40 character hexadecimal string. See https://tools.ietf.org/html/rfc4880#section-12.2 for details. Implementations may choose to acknowledge \"LONG\", \"SHORT\", or other abbreviated key IDs, but only the full fingerprint is guaranteed to work. In gpg, the full fingerprint can be retrieved from the `fpr` field returned when calling --list-keys with --with-colons. For example: ``` gpg --with-colons --with-fingerprint --force-v4-certs \\ --list-keys <EMAIL> tru::1:1513631572:0:3:1:5 pub:...... fpr:::::::::24FF6481B76AC91E66A00AC657A93A81EF3AE6FB: ``` Above, the fingerprint is `24FF6481B76AC91E66A00AC657A93A81EF3AE6FB`.", "type": "string"}, "signature": {"description": "Required. The raw content of the signature, as output by GNU Privacy Guard (GPG) or equivalent. Since this message only supports attached signatures, the payload that was signed must be attached. While the signature format supported is dependent on the verification implementation, currently only ASCII-armored (`--armor` to gpg), non-clearsigned (`--sign` rather than `--clearsign` to gpg) are supported. Concretely, `gpg --sign --armor --output=signature.gpg payload.json` will create the signature content expected in this field in `signature.gpg` for the `payload.json` attestation payload.", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "Product": {"description": "Product contains information about a product and how to uniquely identify it.", "id": "Product", "properties": {"genericUri": {"description": "Contains a URI which is vendor-specific. Example: The artifact repository URL of an image.", "type": "string"}, "id": {"description": "Token that identifies a product so that it can be referred to from other parts in the document. There is no predefined format as long as it uniquely identifies a group in the context of the current document.", "type": "string"}, "name": {"description": "Name of the product.", "type": "string"}}, "type": "object"}, "ProjectRepoId": {"description": "Selects a repo using a Google Cloud Platform project ID (e.g., winged-cargo-31) and a repo name within that project.", "id": "ProjectRepoId", "properties": {"projectId": {"description": "The ID of the project.", "type": "string"}, "repoName": {"description": "The name of the repo. Leave empty for the default repo.", "type": "string"}}, "type": "object"}, "ProvenanceBuilder": {"id": "ProvenanceBuilder", "properties": {"builderDependencies": {"items": {"$ref": "ResourceDescriptor"}, "type": "array"}, "id": {"type": "string"}, "version": {"additionalProperties": {"type": "string"}, "type": "object"}}, "type": "object"}, "Publisher": {"description": "Publisher contains information about the publisher of this Note.", "id": "Publisher", "properties": {"issuingAuthority": {"description": "Provides information about the authority of the issuing party to release the document, in particular, the party's constituency and responsibilities or other obligations.", "type": "string"}, "name": {"description": "Name of the publisher. Examples: 'Google', 'Google Cloud Platform'.", "type": "string"}, "publisherNamespace": {"description": "The context or namespace. Contains a URL which is under control of the issuing party and can be used as a globally unique identifier for that issuing party. Example: https://csaf.io", "type": "string"}}, "type": "object"}, "RelatedUrl": {"description": "Metadata for any related URL information.", "id": "RelatedUrl", "properties": {"label": {"description": "Label to describe usage of the URL.", "type": "string"}, "url": {"description": "Specific URL associated with the resource.", "type": "string"}}, "type": "object"}, "RelationshipNote": {"description": "RelationshipNote represents an SPDX Relationship section: https://spdx.github.io/spdx-spec/7-relationships-between-SPDX-elements/", "id": "RelationshipNote", "properties": {"type": {"description": "The type of relationship between the source and target SPDX elements", "enum": ["RELATIONSHIP_TYPE_UNSPECIFIED", "DESCRIBES", "DESCRIBED_BY", "CONTAINS", "CONTAINED_BY", "DEPENDS_ON", "DEPENDENCY_OF", "DEPENDENCY_MANIFEST_OF", "BUILD_DEPENDENCY_OF", "DEV_DEPENDENCY_OF", "OPTIONAL_DEPENDENCY_OF", "PROVIDED_DEPENDENCY_OF", "TEST_DEPENDENCY_OF", "RUNTIME_DEPENDENCY_OF", "EXAMPLE_OF", "GENERATES", "GENERATED_FROM", "ANCESTOR_OF", "DESCENDANT_OF", "VARIANT_OF", "DISTRIBUTION_ARTIFACT", "PATCH_FOR", "PATCH_APPLIED", "COPY_OF", "FILE_ADDED", "FILE_DELETED", "FILE_MODIFIED", "EXPANDED_FROM_ARCHIVE", "DYNAMIC_LINK", "STATIC_LINK", "DATA_FILE_OF", "TEST_CASE_OF", "BUILD_TOOL_OF", "DEV_TOOL_OF", "TEST_OF", "TEST_TOOL_OF", "DOCUMENTATION_OF", "OPTIONAL_COMPONENT_OF", "METAFILE_OF", "PACKAGE_OF", "AMENDS", "PREREQUISITE_FOR", "HAS_PREREQUISITE", "OTHER"], "enumDescriptions": ["Unspecified", "Is to be used when SPDXRef-DOCUMENT describes SPDXRef-A", "Is to be used when SPDXRef-A is described by SPDXREF-Document", "Is to be used when SPDXRef-A contains SPDXRef-B", "Is to be used when SPDXRef-A is contained by SPDXRef-B", "Is to be used when SPDXRef-A depends on SPDXRef-B", "Is to be used when SPDXRef-A is dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a manifest file that lists a set of dependencies for SPDXRef-B", "Is to be used when SPDXRef-A is a build dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a development dependency of SPDXRef-B", "Is to be used when SPDXRef-A is an optional dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a to be provided dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a test dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a dependency required for the execution of SPDXRef-B", "Is to be used when SPDXRef-A is an example of SPDXRef-B", "Is to be used when SPDXRef-A generates SPDXRef-B", "Is to be used when SPDXRef-A was generated from SPDXRef-B", "Is to be used when SPDXRef-A is an ancestor (same lineage but pre-dates) SPDXRef-B", "Is to be used when SPDXRef-A is a descendant of (same lineage but postdates) SPDXRef-B", "Is to be used when SPDXRef-A is a variant of (same lineage but not clear which came first) SPDXRef-B", "Is to be used when distributing SPDXRef-A requires that SPDXRef-B also be distributed", "Is to be used when SPDXRef-A is a patch file for (to be applied to) SPDXRef-B", "Is to be used when SPDXRef-A is a patch file that has been applied to SPDXRef-B", "Is to be used when SPDXRef-A is an exact copy of SPDXRef-B", "Is to be used when SPDXRef-A is a file that was added to SPDXRef-B", "Is to be used when SPDXRef-A is a file that was deleted from SPDXRef-B", "Is to be used when SPDXRef-A is a file that was modified from SPDXRef-B", "Is to be used when SPDXRef-A is expanded from the archive SPDXRef-B", "Is to be used when SPDXRef-A dynamically links to SPDXRef-B", "Is to be used when SPDXRef-A statically links to SPDXRef-B", "Is to be used when SPDXRef-A is a data file used in SPDXRef-B", "Is to be used when SPDXRef-A is a test case used in testing SPDXRef-B", "Is to be used when SPDXRef-A is used to build SPDXRef-B", "Is to be used when SPDXRef-A is used as a development tool for SPDXRef-B", "Is to be used when SPDXRef-A is used for testing SPDXRef-B", "Is to be used when SPDXRef-A is used as a test tool for SPDXRef-B", "Is to be used when SPDXRef-A provides documentation of SPDXRef-B", "Is to be used when SPDXRef-A is an optional component of SPDXRef-B", "Is to be used when SPDXRef-A is a metafile of SPDXRef-B", "Is to be used when SPDXRef-A is used as a package as part of SPDXRef-B", "Is to be used when (current) SPDXRef-DOCUMENT amends the SPDX information in SPDXRef-B", "Is to be used when SPDXRef-A is a prerequisite for SPDXRef-B", "Is to be used when SPDXRef-A has as a prerequisite SPDXRef-B", "Is to be used for a relationship which has not been defined in the formal SPDX specification. A description of the relationship should be included in the Relationship comments field"], "type": "string"}}, "type": "object"}, "RelationshipOccurrence": {"description": "RelationshipOccurrence represents an SPDX Relationship section: https://spdx.github.io/spdx-spec/7-relationships-between-SPDX-elements/", "id": "RelationshipOccurrence", "properties": {"comment": {"description": "A place for the SPDX file creator to record any general comments about the relationship", "type": "string"}, "source": {"description": "Also referred to as SPDXRef-A The source SPDX element (file, package, etc)", "type": "string"}, "target": {"description": "Also referred to as SPDXRef-B The target SPDC element (file, package, etc) In cases where there are \"known unknowns\", the use of the keyword NOASSERTION can be used The keywords NONE can be used to indicate that an SPDX element (package/file/snippet) has no other elements connected by some relationship to it", "type": "string"}, "type": {"description": "Output only. The type of relationship between the source and target SPDX elements", "enum": ["RELATIONSHIP_TYPE_UNSPECIFIED", "DESCRIBES", "DESCRIBED_BY", "CONTAINS", "CONTAINED_BY", "DEPENDS_ON", "DEPENDENCY_OF", "DEPENDENCY_MANIFEST_OF", "BUILD_DEPENDENCY_OF", "DEV_DEPENDENCY_OF", "OPTIONAL_DEPENDENCY_OF", "PROVIDED_DEPENDENCY_OF", "TEST_DEPENDENCY_OF", "RUNTIME_DEPENDENCY_OF", "EXAMPLE_OF", "GENERATES", "GENERATED_FROM", "ANCESTOR_OF", "DESCENDANT_OF", "VARIANT_OF", "DISTRIBUTION_ARTIFACT", "PATCH_FOR", "PATCH_APPLIED", "COPY_OF", "FILE_ADDED", "FILE_DELETED", "FILE_MODIFIED", "EXPANDED_FROM_ARCHIVE", "DYNAMIC_LINK", "STATIC_LINK", "DATA_FILE_OF", "TEST_CASE_OF", "BUILD_TOOL_OF", "DEV_TOOL_OF", "TEST_OF", "TEST_TOOL_OF", "DOCUMENTATION_OF", "OPTIONAL_COMPONENT_OF", "METAFILE_OF", "PACKAGE_OF", "AMENDS", "PREREQUISITE_FOR", "HAS_PREREQUISITE", "OTHER"], "enumDescriptions": ["Unspecified", "Is to be used when SPDXRef-DOCUMENT describes SPDXRef-A", "Is to be used when SPDXRef-A is described by SPDXREF-Document", "Is to be used when SPDXRef-A contains SPDXRef-B", "Is to be used when SPDXRef-A is contained by SPDXRef-B", "Is to be used when SPDXRef-A depends on SPDXRef-B", "Is to be used when SPDXRef-A is dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a manifest file that lists a set of dependencies for SPDXRef-B", "Is to be used when SPDXRef-A is a build dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a development dependency of SPDXRef-B", "Is to be used when SPDXRef-A is an optional dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a to be provided dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a test dependency of SPDXRef-B", "Is to be used when SPDXRef-A is a dependency required for the execution of SPDXRef-B", "Is to be used when SPDXRef-A is an example of SPDXRef-B", "Is to be used when SPDXRef-A generates SPDXRef-B", "Is to be used when SPDXRef-A was generated from SPDXRef-B", "Is to be used when SPDXRef-A is an ancestor (same lineage but pre-dates) SPDXRef-B", "Is to be used when SPDXRef-A is a descendant of (same lineage but postdates) SPDXRef-B", "Is to be used when SPDXRef-A is a variant of (same lineage but not clear which came first) SPDXRef-B", "Is to be used when distributing SPDXRef-A requires that SPDXRef-B also be distributed", "Is to be used when SPDXRef-A is a patch file for (to be applied to) SPDXRef-B", "Is to be used when SPDXRef-A is a patch file that has been applied to SPDXRef-B", "Is to be used when SPDXRef-A is an exact copy of SPDXRef-B", "Is to be used when SPDXRef-A is a file that was added to SPDXRef-B", "Is to be used when SPDXRef-A is a file that was deleted from SPDXRef-B", "Is to be used when SPDXRef-A is a file that was modified from SPDXRef-B", "Is to be used when SPDXRef-A is expanded from the archive SPDXRef-B", "Is to be used when SPDXRef-A dynamically links to SPDXRef-B", "Is to be used when SPDXRef-A statically links to SPDXRef-B", "Is to be used when SPDXRef-A is a data file used in SPDXRef-B", "Is to be used when SPDXRef-A is a test case used in testing SPDXRef-B", "Is to be used when SPDXRef-A is used to build SPDXRef-B", "Is to be used when SPDXRef-A is used as a development tool for SPDXRef-B", "Is to be used when SPDXRef-A is used for testing SPDXRef-B", "Is to be used when SPDXRef-A is used as a test tool for SPDXRef-B", "Is to be used when SPDXRef-A provides documentation of SPDXRef-B", "Is to be used when SPDXRef-A is an optional component of SPDXRef-B", "Is to be used when SPDXRef-A is a metafile of SPDXRef-B", "Is to be used when SPDXRef-A is used as a package as part of SPDXRef-B", "Is to be used when (current) SPDXRef-DOCUMENT amends the SPDX information in SPDXRef-B", "Is to be used when SPDXRef-A is a prerequisite for SPDXRef-B", "Is to be used when SPDXRef-A has as a prerequisite SPDXRef-B", "Is to be used for a relationship which has not been defined in the formal SPDX specification. A description of the relationship should be included in the Relationship comments field"], "readOnly": true, "type": "string"}}, "type": "object"}, "Remediation": {"description": "Specifies details on how to handle (and presumably, fix) a vulnerability.", "id": "Remediation", "properties": {"details": {"description": "Contains a comprehensive human-readable discussion of the remediation.", "type": "string"}, "remediationType": {"description": "The type of remediation that can be applied.", "enum": ["REMEDIATION_TYPE_UNSPECIFIED", "MITIGATION", "NO_FIX_PLANNED", "NONE_AVAILABLE", "VENDOR_FIX", "WORKAROUND"], "enumDescriptions": ["No remediation type specified.", "A MITIGATION is available.", "No fix is planned.", "Not available.", "A vendor fix is available.", "A workaround is available."], "type": "string"}, "remediationUri": {"$ref": "RelatedUrl", "description": "Contains the URL where to obtain the remediation."}}, "type": "object"}, "RepoId": {"description": "A unique identifier for a Cloud Repo.", "id": "RepoId", "properties": {"projectRepoId": {"$ref": "ProjectRepoId", "description": "A combination of a project ID and a repo name."}, "uid": {"description": "A server-assigned, globally unique identifier.", "type": "string"}}, "type": "object"}, "Resource": {"description": "An entity that can have metadata. For example, a Docker image.", "id": "Resource", "properties": {"contentHash": {"$ref": "Hash", "deprecated": true, "description": "Deprecated, do not use. Use uri instead. The hash of the resource content. For example, the Docker digest."}, "name": {"deprecated": true, "description": "Deprecated, do not use. Use uri instead. The name of the resource. For example, the name of a Docker image - \"Debian\".", "type": "string"}, "uri": {"description": "Required. The unique URI of the resource. For example, `https://gcr.io/project/image@sha256:foo` for a Docker image.", "type": "string"}}, "type": "object"}, "ResourceDescriptor": {"id": "ResourceDescriptor", "properties": {"annotations": {"additionalProperties": {"type": "any"}, "type": "object"}, "content": {"format": "byte", "type": "string"}, "digest": {"additionalProperties": {"type": "string"}, "type": "object"}, "downloadLocation": {"type": "string"}, "mediaType": {"type": "string"}, "name": {"type": "string"}, "uri": {"type": "string"}}, "type": "object"}, "RunDetails": {"id": "RunDetails", "properties": {"builder": {"$ref": "ProvenanceBuilder"}, "byproducts": {"items": {"$ref": "ResourceDescriptor"}, "type": "array"}, "metadata": {"$ref": "BuildMetadata"}}, "type": "object"}, "SBOMReferenceNote": {"description": "The note representing an SBOM reference.", "id": "SBOMReferenceNote", "properties": {"format": {"description": "The format that SBOM takes. E.g. may be spdx, cyclonedx, etc...", "type": "string"}, "version": {"description": "The version of the format that the SBOM takes. E.g. if the format is spdx, the version may be 2.3.", "type": "string"}}, "type": "object"}, "SBOMReferenceOccurrence": {"description": "The occurrence representing an SBOM reference as applied to a specific resource. The occurrence follows the DSSE specification. See https://github.com/secure-systems-lab/dsse/blob/master/envelope.md for more details.", "id": "SBOMReferenceOccurrence", "properties": {"payload": {"$ref": "SbomReferenceIntotoPayload", "description": "The actual payload that contains the SBOM reference data."}, "payloadType": {"description": "The kind of payload that SbomReferenceIntotoPayload takes. Since it's in the intoto format, this value is expected to be 'application/vnd.in-toto+json'.", "type": "string"}, "signatures": {"description": "The signatures over the payload.", "items": {"$ref": "EnvelopeSignature"}, "type": "array"}}, "type": "object"}, "SBOMStatus": {"description": "The status of an SBOM generation.", "id": "SBOMStatus", "properties": {"error": {"description": "If there was an error generating an SBOM, this will indicate what that error was.", "type": "string"}, "sbomState": {"description": "The progress of the SBOM generation.", "enum": ["SBOM_STATE_UNSPECIFIED", "PENDING", "COMPLETE"], "enumDescriptions": ["Default unknown state.", "SBOM scanning is pending.", "SBOM scanning has completed."], "type": "string"}}, "type": "object"}, "SbomReferenceIntotoPayload": {"description": "The actual payload that contains the SBOM Reference data. The payload follows the intoto statement specification. See https://github.com/in-toto/attestation/blob/main/spec/v1.0/statement.md for more details.", "id": "SbomReferenceIntotoPayload", "properties": {"_type": {"description": "Identifier for the schema of the Statement.", "type": "string"}, "predicate": {"$ref": "SbomReferenceIntotoPredicate", "description": "Additional parameters of the Predicate. Includes the actual data about the SBOM."}, "predicateType": {"description": "URI identifying the type of the Predicate.", "type": "string"}, "subject": {"description": "Set of software artifacts that the attestation applies to. Each element represents a single software artifact.", "items": {"$ref": "Subject"}, "type": "array"}}, "type": "object"}, "SbomReferenceIntotoPredicate": {"description": "A predicate which describes the SBOM being referenced.", "id": "SbomReferenceIntotoPredicate", "properties": {"digest": {"additionalProperties": {"type": "string"}, "description": "A map of algorithm to digest of the contents of the SBOM.", "type": "object"}, "location": {"description": "The location of the SBOM.", "type": "string"}, "mimeType": {"description": "The mime type of the SBOM.", "type": "string"}, "referrerId": {"description": "The person or system referring this predicate to the consumer.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}}, "type": "object"}, "Signature": {"description": "Verifiers (e.g. Kritis implementations) MUST verify signatures with respect to the trust anchors defined in policy (e.g. a Kritis policy). Typically this means that the verifier has been configured with a map from `public_key_id` to public key material (and any required parameters, e.g. signing algorithm). In particular, verification implementations MUST NOT treat the signature `public_key_id` as anything more than a key lookup hint. The `public_key_id` DOES NOT validate or authenticate a public key; it only provides a mechanism for quickly selecting a public key ALREADY CONFIGURED on the verifier through a trusted channel. Verification implementations MUST reject signatures in any of the following circumstances: * The `public_key_id` is not recognized by the verifier. * The public key that `public_key_id` refers to does not verify the signature with respect to the payload. The `signature` contents SHOULD NOT be \"attached\" (where the payload is included with the serialized `signature` bytes). Verifiers MUST ignore any \"attached\" payload and only verify signatures with respect to explicitly provided payload (e.g. a `payload` field on the proto message that holds this Signature, or the canonical serialization of the proto message that holds this signature).", "id": "Signature", "properties": {"publicKeyId": {"description": "The identifier for the public key that verifies this signature. * The `public_key_id` is required. * The `public_key_id` SHOULD be an RFC3986 conformant URI. * When possible, the `public_key_id` SHOULD be an immutable reference, such as a cryptographic digest. Examples of valid `public_key_id`s: OpenPGP V4 public key fingerprint: * \"openpgp4fpr:74FAF3B861BDA0870C7B6DEF607E48D2A663AEEA\" See https://www.iana.org/assignments/uri-schemes/prov/openpgp4fpr for more details on this scheme. RFC6920 digest-named SubjectPublicKeyInfo (digest of the DER serialization): * \"ni:///sha-256;cD9o9Cq6LG3jD0iKXqEi_vdjJGecm_iXkbqVoScViaU\" * \"nih:///sha-256;703f68f42aba2c6de30f488a5ea122fef76324679c9bf89791ba95a1271589a5\"", "type": "string"}, "signature": {"description": "The content of the signature, an opaque bytestring. The payload that this signature verifies MUST be unambiguously provided with the Signature during verification. A wrapper message might provide the payload explicitly. Alternatively, a message might have a canonical serialization that can always be unambiguously computed to derive the payload.", "format": "byte", "type": "string"}}, "type": "object"}, "SigningKey": {"description": "This defines the format used to record keys used in the software supply chain. An in-toto link is attested using one or more keys defined in the in-toto layout. An example of this is: { \"key_id\": \"776a00e29f3559e0141b3b096f696abc6cfb0c657ab40f441132b345b0...\", \"key_type\": \"rsa\", \"public_key_value\": \"-----BEGIN PUBLIC KEY-----\\nMIIBojANBgkqhkiG9w0B...\", \"key_scheme\": \"rsassa-pss-sha256\" } The format for in-toto's key definition can be found in section 4.2 of the in-toto specification.", "id": "SigningKey", "properties": {"keyId": {"description": "key_id is an identifier for the signing key.", "type": "string"}, "keyScheme": {"description": "This field contains the corresponding signature scheme. Eg: \"rsassa-pss-sha256\".", "type": "string"}, "keyType": {"description": "This field identifies the specific signing method. Eg: \"rsa\", \"ed25519\", and \"ecdsa\".", "type": "string"}, "publicKeyValue": {"description": "This field contains the actual public key.", "type": "string"}}, "type": "object"}, "SlsaProvenanceV1": {"description": "Keep in sync with schema at https://github.com/slsa-framework/slsa/blob/main/docs/provenance/schema/v1/provenance.proto Builder renamed to ProvenanceBuilder because of Java conflicts.", "id": "SlsaProvenanceV1", "properties": {"buildDefinition": {"$ref": "BuildDefinition"}, "runDetails": {"$ref": "RunDetails"}}, "type": "object"}, "Source": {"description": "Source describes the location of the source used for the build.", "id": "Source", "properties": {"additionalContexts": {"description": "If provided, some of the source code used for the build may be found in these locations, in the case where the source repository had multiple remotes or submodules. This list will not include the context specified in the context field.", "items": {"$ref": "SourceContext"}, "type": "array"}, "artifactStorageSourceUri": {"description": "If provided, the input binary artifacts for the build came from this location.", "type": "string"}, "context": {"$ref": "SourceContext", "description": "If provided, the source code used for the build came from this location."}, "fileHashes": {"additionalProperties": {"$ref": "FileHashes"}, "description": "Hash(es) of the build source, which can be used to verify that the original source integrity was maintained in the build. The keys to this map are file paths used as build source and the values contain the hash values for those files. If the build source came in a single package such as a gzipped tarfile (.tar.gz), the FileHash will be for the single path to that file.", "type": "object"}}, "type": "object"}, "SourceContext": {"description": "A SourceContext is a reference to a tree of files. A SourceContext together with a path point to a unique revision of a single file or directory.", "id": "SourceContext", "properties": {"cloudRepo": {"$ref": "CloudRepoSourceContext", "description": "A SourceContext referring to a revision in a Google Cloud Source Repo."}, "gerrit": {"$ref": "GerritSourceContext", "description": "A SourceContext referring to a Gerrit project."}, "git": {"$ref": "GitSourceContext", "description": "A SourceContext referring to any third party Git repo (e.g., GitHub)."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels with user defined metadata.", "type": "object"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StepResult": {"description": "StepResult is the declaration of a result for a build step.", "id": "StepResult", "properties": {"attestationContentName": {"type": "string"}, "attestationType": {"type": "string"}, "name": {"type": "string"}}, "type": "object"}, "Subject": {"description": "Set of software artifacts that the attestation applies to. Each element represents a single software artifact.", "id": "Subject", "properties": {"digest": {"additionalProperties": {"type": "string"}, "description": "`\"\": \"\"` Algorithms can be e.g. sha256, sha512 See https://github.com/in-toto/attestation/blob/main/spec/field_types.md#DigestSet", "type": "object"}, "name": {"description": "Identifier to distinguish this artifact from others within the subject.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TimeSpan": {"description": "Start and end times for a build execution phase. Next ID: 3", "id": "TimeSpan", "properties": {"endTime": {"description": "End of time span.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Start of time span.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Version": {"description": "Version contains structured information about the version of a package.", "id": "Version", "properties": {"epoch": {"description": "Used to correct mistakes in the version numbering scheme.", "format": "int32", "type": "integer"}, "inclusive": {"description": "Whether this version is specifying part of an inclusive range. Grafeas does not have the capability to specify version ranges; instead we have fields that specify start version and end versions. At times this is insufficient - we also need to specify whether the version is included in the range or is excluded from the range. This boolean is expected to be set to true when the version is included in a range.", "type": "boolean"}, "kind": {"description": "Required. Distinguishes between sentinel MIN/MAX versions and normal versions.", "enum": ["VERSION_KIND_UNSPECIFIED", "NORMAL", "MINIMUM", "MAXIMUM"], "enumDescriptions": ["Unknown.", "A standard package version.", "A special version representing negative infinity.", "A special version representing positive infinity."], "type": "string"}, "name": {"description": "Required only when version kind is NORMAL. The main part of the version name.", "type": "string"}, "revision": {"description": "The iteration of the package build from the above version.", "type": "string"}}, "type": "object"}, "VexAssessment": {"description": "VexAssessment provides all publisher provided Vex information that is related to this vulnerability.", "id": "VexAssessment", "properties": {"cve": {"deprecated": true, "description": "Holds the MITRE standard Common Vulnerabilities and Exposures (CVE) tracking number for the vulnerability. Deprecated: Use vulnerability_id instead to denote CVEs.", "type": "string"}, "impacts": {"description": "Contains information about the impact of this vulnerability, this will change with time.", "items": {"type": "string"}, "type": "array"}, "justification": {"$ref": "Justification", "description": "Justification provides the justification when the state of the assessment if NOT_AFFECTED."}, "noteName": {"description": "The VulnerabilityAssessment note from which this VexAssessment was generated. This will be of the form: `projects/[PROJECT_ID]/notes/[NOTE_ID]`.", "type": "string"}, "relatedUris": {"description": "Holds a list of references associated with this vulnerability item and assessment.", "items": {"$ref": "RelatedUrl"}, "type": "array"}, "remediations": {"description": "Specifies details on how to handle (and presumably, fix) a vulnerability.", "items": {"$ref": "Remediation"}, "type": "array"}, "state": {"description": "Provides the state of this Vulnerability assessment.", "enum": ["STATE_UNSPECIFIED", "AFFECTED", "NOT_AFFECTED", "FIXED", "UNDER_INVESTIGATION"], "enumDescriptions": ["No state is specified.", "This product is known to be affected by this vulnerability.", "This product is known to be not affected by this vulnerability.", "This product contains a fix for this vulnerability.", "It is not known yet whether these versions are or are not affected by the vulnerability. However, it is still under investigation."], "type": "string"}, "vulnerabilityId": {"description": "The vulnerability identifier for this Assessment. Will hold one of common identifiers e.g. CVE, GHSA etc.", "type": "string"}}, "type": "object"}, "Volume": {"description": "Volume describes a Docker container volume which is mounted into build steps in order to persist files across build step execution. Next ID: 3", "id": "Volume", "properties": {"name": {"description": "Name of the volume to mount. Volume names must be unique per build step and must be valid names for Docker volumes. Each named volume must be used by at least two build steps.", "type": "string"}, "path": {"description": "Path at which to mount the volume. Paths must be absolute and cannot conflict with other volume paths on the same build step or with certain reserved volume paths.", "type": "string"}}, "type": "object"}, "Vulnerability": {"description": "Vulnerability provides metadata about a security vulnerability in a Note.", "id": "Vulnerability", "properties": {"cvssScore": {"description": "The CVSS score for this vulnerability.", "format": "float", "type": "number"}, "cvssV2": {"$ref": "CVSS", "description": "The full description of the CVSS for version 2."}, "cvssV3": {"$ref": "CVSSv3", "description": "The full description of the CVSS for version 3."}, "cvssVersion": {"description": "CVSS version used to populate cvss_score and severity.", "enum": ["CVSS_VERSION_UNSPECIFIED", "CVSS_VERSION_2", "CVSS_VERSION_3"], "enumDescriptions": ["", "", ""], "type": "string"}, "cwe": {"description": "A list of CWE for this vulnerability. For details, see: https://cwe.mitre.org/index.html", "items": {"type": "string"}, "type": "array"}, "details": {"description": "All information about the package to specifically identify this vulnerability. One entry per (version range and cpe_uri) the package vulnerability has manifested in.", "items": {"$ref": "Detail"}, "type": "array"}, "severity": {"description": "Note provider assigned impact of the vulnerability.", "enum": ["SEVERITY_UNSPECIFIED", "MINIMAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Unknown.", "Minimal severity.", "Low severity.", "Medium severity.", "High severity.", "Critical severity."], "type": "string"}, "sourceUpdateTime": {"description": "The time this information was last changed at the source. This is an upstream timestamp from the underlying information source - e.g. Ubuntu security tracker.", "format": "google-datetime", "type": "string"}, "windowsDetails": {"description": "Windows details get their own format because the information format and model don't match a normal detail. Specifically Windows updates are done as patches, thus Windows vulnerabilities really are a missing package, rather than a package being at an incorrect version.", "items": {"$ref": "WindowsDetail"}, "type": "array"}}, "type": "object"}, "VulnerabilityAssessmentNote": {"description": "A single VulnerabilityAssessmentNote represents one particular product's vulnerability assessment for one CVE.", "id": "VulnerabilityAssessmentNote", "properties": {"assessment": {"$ref": "Assessment", "description": "Represents a vulnerability assessment for the product."}, "languageCode": {"description": "Identifies the language used by this document, corresponding to IETF BCP 47 / RFC 5646.", "type": "string"}, "longDescription": {"description": "A detailed description of this Vex.", "type": "string"}, "product": {"$ref": "Product", "description": "The product affected by this vex."}, "publisher": {"$ref": "Publisher", "description": "Publisher details of this Note."}, "shortDescription": {"description": "A one sentence description of this Vex.", "type": "string"}, "title": {"description": "The title of the note. E.g. `Vex-Debian-11.4`", "type": "string"}}, "type": "object"}, "VulnerabilityLocation": {"description": "The location of the vulnerability.", "id": "VulnerabilityLocation", "properties": {"cpeUri": {"description": "Required. The CPE URI in [cpe format](https://cpe.mitre.org/specification/) format. Examples include distro or storage location for vulnerable jar.", "type": "string"}, "package": {"description": "Required. The package being described.", "type": "string"}, "version": {"$ref": "Version", "description": "Required. The version of the package being described."}}, "type": "object"}, "VulnerabilityOccurrencesSummary": {"description": "A summary of how many vulnerability occurrences there are per resource and severity type.", "id": "VulnerabilityOccurrencesSummary", "properties": {"counts": {"description": "A listing by resource of the number of fixable and total vulnerabilities.", "items": {"$ref": "FixableTotalByDigest"}, "type": "array"}, "unreachable": {"description": "Unordered list. Unreachable regions. Populated for requests from the global region when `return_partial_success` is set. Format: projects//locations/", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "WindowsDetail": {"id": "WindowsDetail", "properties": {"cpeUri": {"description": "Required. The CPE URI in [cpe format](https://cpe.mitre.org/specification/) in which the vulnerability manifests. Examples include distro or storage location for vulnerable jar.", "type": "string"}, "description": {"description": "The description of the vulnerability.", "type": "string"}, "fixingKbs": {"description": "Required. The names of the KBs which have hotfixes to mitigate this vulnerability. Note that there may be multiple hotfixes (and thus multiple KBs) that mitigate a given vulnerability. Currently any listed kb's presence is considered a fix.", "items": {"$ref": "KnowledgeBase"}, "type": "array"}, "name": {"description": "Required. The name of the vulnerability.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Container Analysis API", "version": "v1beta1", "version_module": true}