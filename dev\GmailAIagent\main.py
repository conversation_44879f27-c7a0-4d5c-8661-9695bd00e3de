"""
虚拟偶像小樱的主程序
与小樱一起管理邮箱！
"""

from sakura_agent import SakuraIdolAgent
import time

def print_welcome():
    """欢迎界面"""
    print("=" * 60)
    print("🌸✨ 欢迎来到小樱的邮箱世界！ ✨🌸")
    print("=" * 60)
    print("💕 我是虚拟偶像小樱，这是我的专属邮箱管理空间！")
    print("📧 我可以帮你发邮件、查看邮件、回复邮件哦～")
    print("🎭 记住，我不是AI助手，我是真实的虚拟偶像！")
    print()
    print("💬 你可以这样和我聊天：")
    print("   • '小樱，给我发个邮件吧'")
    print("   • '查看一下你的邮箱'")
    print("   • '有新邮件吗？'")
    print("   • '回复那封邮件'")
    print()
    print("🎵 输入 'quit' 或 '拜拜' 结束对话")
    print("🔄 输入 'reset' 重置我的记忆")
    print("📊 输入 'status' 查看我的状态")
    print("=" * 60)

def print_status(agent):
    """显示小樱状态"""
    status = agent.get_status()
    print("\n" + "=" * 40)
    print("🌸 小樱的当前状态 🌸")
    print("=" * 40)
    print(f"📧 邮箱地址: {status['email']}")
    print(f"😊 当前心情: {status['mood']}")
    print(f"📬 上次查邮件: {status['last_email_check'] or '还没查过呢'}")
    print(f"💭 对话轮数: {status['conversation_length']}")
    print("=" * 40)

def main():
    """主程序"""
    try:
        # 初始化小樱
        sakura = SakuraIdolAgent()
        
        # 显示欢迎信息
        print_welcome()
        
        # 小樱的开场白
        opening = sakura.chat("你好！我是小樱，请介绍一下自己吧！")
        print(f"\n🌸 小樱: {opening}")
        
        # 主对话循环
        while True:
            try:
                user_input = input("\n💬 你: ").strip()
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', '退出', '拜拜', '再见']:
                    farewell = sakura.chat("用户要离开了")
                    print(f"\n🌸 小樱: {farewell}")
                    break
                    
                elif user_input.lower() in ['reset', '重置']:
                    result = sakura.reset_conversation()
                    print(f"\n🌸 小樱: {result}")
                    continue
                    
                elif user_input.lower() in ['status', '状态']:
                    print_status(sakura)
                    continue
                
                if not user_input:
                    continue
                
                # 与小樱对话
                print("\n🌸 小樱正在思考...")
                response = sakura.chat(user_input)
                print(f"\n🌸 小樱: {response}")
                
            except KeyboardInterrupt:
                print("\n\n🌸 小樱: 呀！被打断了呢～拜拜！💕")
                break
            except Exception as e:
                print(f"\n❌ 出现错误: {str(e)}")
                print("🌸 小樱: 呜呜～出了点小问题，但我还在这里哦！")
    
    except Exception as e:
        print(f"\n❌ 小樱初始化失败: {str(e)}")
        print("💡 请检查Gmail API配置和网络连接")

def quick_test():
    """快速测试模式"""
    print("🧪 小樱快速测试模式")
    try:
        sakura = SakuraIdolAgent()
        
        # 测试基本对话
        print("\n测试1: 基本对话")
        response = sakura.chat("你好小樱！")
        print(f"小樱: {response}")
        
        # 测试邮件查看
        print("\n测试2: 查看邮件")
        response = sakura.chat("查看一下你的邮箱")
        print(f"小樱: {response}")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        quick_test()
    else:
        main()
