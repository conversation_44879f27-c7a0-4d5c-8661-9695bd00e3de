"""
小樱的LLM客户端
基于systemtimeAIagent的架构
"""

import openai
from config import Config
from prompts import SAKURA_SYSTEM_PROMPT

class SakuraLLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL,
            timeout=60.0,  # 增加超时时间到60秒
            max_retries=3   # 最多重试3次
        )
        self.model = Config.LLM_MODEL
        
    def chat(self, messages, functions=None):
        """与小樱对话"""
        import time

        for attempt in range(3):  # 最多尝试3次
            try:
                print(f"🌸 小樱正在思考中... (尝试 {attempt + 1}/3)")

                # 使用tools格式（新版OpenAI API）
                if functions:
                    tools = [{"type": "function", "function": func} for func in functions]
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[{"role": "system", "content": SAKURA_SYSTEM_PROMPT}] + messages,
                        tools=tools,
                        tool_choice="auto",
                        temperature=0.9,  # 更高的创造性，让小樱更生动
                        max_tokens=1000
                    )
                else:
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[{"role": "system", "content": SAKURA_SYSTEM_PROMPT}] + messages,
                        temperature=0.9,
                        max_tokens=1000
                    )
                return response

            except Exception as e:
                error_msg = str(e)
                if attempt < 2:  # 不是最后一次尝试
                    if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                        print(f"⏰ 小樱的思考超时了，让我再试一次... ({attempt + 1}/3)")
                        time.sleep(2)  # 等待2秒后重试
                        continue
                    elif "connection" in error_msg.lower():
                        print(f"🌐 网络连接有问题，小樱再试一次... ({attempt + 1}/3)")
                        time.sleep(3)  # 等待3秒后重试
                        continue

                # 最后一次尝试失败，或者其他类型错误
                raise Exception(f"小樱的大脑出了点小问题呢～: {str(e)}")
    
    def chat_simple(self, user_message):
        """简单对话（无函数调用）"""
        try:
            messages = [{"role": "user", "content": user_message}]
            response = self.chat(messages)
            return response.choices[0].message.content
        except Exception as e:
            return f"呜呜～小樱有点困惑了：{str(e)}"
