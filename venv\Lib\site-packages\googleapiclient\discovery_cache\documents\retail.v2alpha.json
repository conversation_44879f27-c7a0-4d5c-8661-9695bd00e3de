{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://retail.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Retail", "description": "Vertex AI Search for commerce API is made up of Retail Search, Browse and Recommendations. These discovery AI solutions help you implement personalized search, browse and recommendations, based on machine learning models, across your websites and mobile applications.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/recommendations", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "retail:v2alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://retail.mtls.googleapis.com/", "name": "retail", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"methods": {"enrollSolution": {"description": "The method enrolls a solution of type Retail Search into a project. The Recommendations AI solution type is enrolled by default when your project enables Retail API, so you don't need to call the enrollSolution method for recommendations.", "flatPath": "v2alpha/projects/{projectsId}:enrollSolution", "httpMethod": "POST", "id": "retail.projects.enrollSolution", "parameterOrder": ["project"], "parameters": {"project": {"description": "Required. Full resource name of parent. Format: `projects/{project_number_or_id}`", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+project}:enrollSolution", "request": {"$ref": "GoogleCloudRetailV2alphaEnrollSolutionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getAlertConfig": {"description": "Get the AlertConfig of the requested project.", "flatPath": "v2alpha/projects/{projectsId}/alertConfig", "httpMethod": "GET", "id": "retail.projects.getAlertConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full AlertConfig resource name. Format: projects/{project_number}/alertConfig", "location": "path", "pattern": "^projects/[^/]+/alertConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaAlertConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getLoggingConfig": {"description": "Gets the LoggingConfig of the requested project.", "flatPath": "v2alpha/projects/{projectsId}/loggingConfig", "httpMethod": "GET", "id": "retail.projects.getLoggingConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full LoggingConfig resource name. Format: projects/{project_number}/loggingConfig", "location": "path", "pattern": "^projects/[^/]+/loggingConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaLoggingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getRetailProject": {"description": "Gets the project. Throws `NOT_FOUND` if the project wasn't initialized for the Retail API service.", "flatPath": "v2alpha/projects/{projectsId}/retailProject", "httpMethod": "GET", "id": "retail.projects.getRetailProject", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of the project. Format: `projects/{project_number_or_id}/retailProject`", "location": "path", "pattern": "^projects/[^/]+/retailProject$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaProject"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listEnrolledSolutions": {"description": "Lists all the retail API solutions the project has enrolled.", "flatPath": "v2alpha/projects/{projectsId}:enrolledSolutions", "httpMethod": "GET", "id": "retail.projects.listEnrolledSolutions", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Full resource name of parent. Format: `projects/{project_number_or_id}`", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}:enrolledSolutions", "response": {"$ref": "GoogleCloudRetailV2alphaListEnrolledSolutionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateAlertConfig": {"description": "Update the alert config of the requested project.", "flatPath": "v2alpha/projects/{projectsId}/alertConfig", "httpMethod": "PATCH", "id": "retail.projects.updateAlertConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Immutable. The name of the AlertConfig singleton resource. Format: projects/*/alertConfig", "location": "path", "pattern": "^projects/[^/]+/alertConfig$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided AlertConfig to update. If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaAlertConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaAlertConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateLoggingConfig": {"description": "Updates the LoggingConfig of the requested project.", "flatPath": "v2alpha/projects/{projectsId}/loggingConfig", "httpMethod": "PATCH", "id": "retail.projects.updateLoggingConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Immutable. The name of the LoggingConfig singleton resource. Format: projects/*/loggingConfig", "location": "path", "pattern": "^projects/[^/]+/loggingConfig$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided LoggingConfig to update. The following are the only supported fields: * LoggingConfig.default_log_generation_rule * LoggingConfig.service_log_generation_rules If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaLoggingConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaLoggingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"locations": {"resources": {"catalogs": {"methods": {"completeQuery": {"description": "Completes the specified prefix with keyword suggestions. This feature is only available for users who have Retail Search enabled. Enable Retail Search on Cloud Console before using this feature.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}:completeQuery", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.completeQuery", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Required. Catalog for which the completion is performed. Full resource name of catalog, such as `projects/*/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "dataset": {"description": "Determines which dataset to use for fetching completion. \"user-data\" will use the dataset imported through CompletionService.ImportCompletionData. `cloud-retail` will use the dataset generated by Cloud Retail based on user events. If left empty, completions will be fetched from the `user-data` dataset. Current supported values: * user-data * cloud-retail: This option requires enabling auto-learning function first. See [guidelines](https://cloud.google.com/retail/docs/completion-overview#generated-completion-dataset).", "location": "query", "type": "string"}, "deviceType": {"description": "The device type context for completion suggestions. We recommend that you leave this field empty. It can apply different suggestions on different device types, e.g. `DESKTOP`, `<PERSON><PERSON><PERSON><PERSON>`. If it is empty, the suggestions are across all device types. Supported formats: * `UNKNOWN_DEVICE_TYPE` * `DESKTOP` * `<PERSON><PERSON><PERSON><PERSON>` * A customized string starts with `OTHER_`, e.g. `OTHER_IPHONE`.", "location": "query", "type": "string"}, "enableAttributeSuggestions": {"description": "If true, attribute suggestions are enabled and provided in the response. This field is only available for the `cloud-retail` dataset.", "location": "query", "type": "boolean"}, "entity": {"description": "The entity for customers who run multiple entities, domains, sites, or regions, for example, `Google US`, `Google Ads`, `Waymo`, `google.com`, `youtube.com`, etc. If this is set, it must be an exact match with UserEvent.entity to get per-entity autocomplete results. This field will be applied to `completion_results` only. It has no effect on the `attribute_results`. Also, this entity should be limited to 256 characters, if too long, it will be truncated to 256 characters in both generation and serving time, and may lead to mis-match. To ensure it works, please set the entity with string within 256 characters.", "location": "query", "type": "string"}, "languageCodes": {"description": "Note that this field applies for `user-data` dataset only. For requests with `cloud-retail` dataset, setting this field has no effect. The language filters applied to the output suggestions. If set, it should contain the language of the query. If not set, suggestions are returned without considering language restrictions. This is the BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47). The maximum number of language codes is 3.", "location": "query", "repeated": true, "type": "string"}, "maxSuggestions": {"description": "Completion max suggestions. If left unset or set to 0, then will fallback to the configured value CompletionConfig.max_suggestions. The maximum allowed max suggestions is 20. If it is set higher, it will be capped by 20.", "format": "int32", "location": "query", "type": "integer"}, "query": {"description": "Required. The query used to generate suggestions. The maximum number of allowed characters is 255.", "location": "query", "type": "string"}, "visitorId": {"description": "Recommended field. A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}}, "path": "v2alpha/{+catalog}:completeQuery", "response": {"$ref": "GoogleCloudRetailV2alphaCompleteQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportAnalyticsMetrics": {"description": "Exports analytics metrics. `Operation.response` is of type `ExportAnalyticsMetricsResponse`. `Operation.metadata` is of type `ExportMetadata`.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}:exportAnalyticsMetrics", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.exportAnalyticsMetrics", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Required. Full resource name of the parent catalog. Expected format: `projects/*/locations/*/catalogs/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+catalog}:exportAnalyticsMetrics", "request": {"$ref": "GoogleCloudRetailV2alphaExportAnalyticsMetricsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getAttributesConfig": {"description": "Gets an AttributesConfig.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/attributesConfig", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.getAttributesConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full AttributesConfig resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/attributesConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/attributesConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaAttributesConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getCompletionConfig": {"description": "Gets a CompletionConfig.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/completionConfig", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.getCompletionConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full CompletionConfig resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/completionConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/completionConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaCompletionConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getConversationalSearchCustomizationConfig": {"description": "Returns the conversational search customization config for a given catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/conversationalSearchCustomizationConfig", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.getConversationalSearchCustomizationConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the parent catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}/conversationalSearchCustomizationConfig", "response": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchCustomizationConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getDefaultBranch": {"description": "Get which branch is currently default branch set by CatalogService.SetDefaultBranch method under a specified parent catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}:getDefaultBranch", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.getDefaultBranch", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "The parent catalog resource name, such as `projects/*/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+catalog}:getDefaultBranch", "response": {"$ref": "GoogleCloudRetailV2alphaGetDefaultBranchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getGenerativeQuestionFeature": {"description": "Manages overal generative question feature state -- enables toggling feature on and off.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/generativeQuestionFeature", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.getGenerativeQuestionFeature", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Required. Resource name of the parent catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+catalog}/generativeQuestionFeature", "response": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionsFeatureConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the Catalogs associated with the project.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Catalogs to return. If unspecified, defaults to 50. The maximum allowed value is 1000. Values above 1000 will be coerced to 1000. If this field is negative, an INVALID_ARGUMENT is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token ListCatalogsResponse.next_page_token, received from a previous CatalogService.ListCatalogs call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to CatalogService.ListCatalogs must match the call that provided the page token. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account resource name with an associated location. If the caller does not have permission to list Catalogs under this location, regardless of whether or not this location exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/catalogs", "response": {"$ref": "GoogleCloudRetailV2alphaListCatalogsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the Catalogs.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Immutable. The fully qualified resource name of the catalog.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Catalog to update. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaCatalog"}, "response": {"$ref": "GoogleCloudRetailV2alphaCatalog"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setDefaultBranch": {"description": "Set a specified branch id as default branch. API methods such as SearchService.Search, ProductService.GetProduct, ProductService.ListProducts will treat requests using \"default_branch\" to the actual branch id set as default. For example, if `projects/*/locations/*/catalogs/*/branches/1` is set as default, setting SearchRequest.branch to `projects/*/locations/*/catalogs/*/branches/default_branch` is equivalent to setting SearchRequest.branch to `projects/*/locations/*/catalogs/*/branches/1`. Using multiple branches can be useful when developers would like to have a staging branch to test and verify for future usage. When it becomes ready, developers switch on the staging branch using this API while keeping using `projects/*/locations/*/catalogs/*/branches/default_branch` as SearchRequest.branch to route the traffic to this staging branch. CAUTION: If you have live predict/search traffic, switching the default branch could potentially cause outages if the ID space of the new branch is very different from the old one. More specifically: * PredictionService will only return product IDs from branch {newBranch}. * SearchService will only return product IDs from branch {newBranch} (if branch is not explicitly set). * UserEventService will only join events with products from branch {newBranch}.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}:setDefaultBranch", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.setDefaultBranch", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Full resource name of the catalog, such as `projects/*/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+catalog}:setDefaultBranch", "request": {"$ref": "GoogleCloudRetailV2alphaSetDefaultBranchRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateAttributesConfig": {"description": "Updates the AttributesConfig. The catalog attributes in the request will be updated in the catalog, or inserted if they do not exist. Existing catalog attributes not included in the request will remain unchanged. Attributes that are assigned to products, but do not exist at the catalog level, are always included in the response. The product attribute is assigned default values for missing catalog attribute fields, e.g., searchable and dynamic facetable options.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/attributesConfig", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.updateAttributesConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Immutable. The fully qualified resource name of the attribute config. Format: `projects/*/locations/*/catalogs/*/attributesConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/attributesConfig$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided AttributesConfig to update. The following is the only supported field: * AttributesConfig.catalog_attributes If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaAttributesConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaAttributesConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateCompletionConfig": {"description": "Updates the CompletionConfigs.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/completionConfig", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.updateCompletionConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Immutable. Fully qualified name `projects/*/locations/*/catalogs/*/completionConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/completionConfig$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided CompletionConfig to update. The following are the only supported fields: * CompletionConfig.matching_order * CompletionConfig.max_suggestions * CompletionConfig.min_prefix_length * CompletionConfig.auto_learning If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaCompletionConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaCompletionConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateConversationalSearchCustomizationConfig": {"description": "Updates the conversational search customization config for a given catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/conversationalSearchCustomizationConfig", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.updateConversationalSearchCustomizationConfig", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Required. Resource name of the catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided ConversationalSearchCustomizationConfig to update. If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+catalog}/conversationalSearchCustomizationConfig", "request": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchCustomizationConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchCustomizationConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateGenerativeQuestion": {"description": "Allows management of individual questions.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/generativeQuestion", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.updateGenerativeQuestion", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Required. Resource name of the catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided GenerativeQuestionConfig to update. The following are NOT supported: * GenerativeQuestionConfig.frequency If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+catalog}/generativeQuestion", "request": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateGenerativeQuestionFeature": {"description": "Manages overal generative question feature state -- enables toggling feature on and off.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/generativeQuestionFeature", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.updateGenerativeQuestionFeature", "parameterOrder": ["catalog"], "parameters": {"catalog": {"description": "Required. Resource name of the affected catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided GenerativeQuestionsFeatureConfig to update. If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+catalog}/generativeQuestionFeature", "request": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionsFeatureConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionsFeatureConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"attributesConfig": {"methods": {"addCatalogAttribute": {"description": "Adds the specified CatalogAttribute to the AttributesConfig. If the CatalogAttribute to add already exists, an ALREADY_EXISTS error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/attributesConfig:addCatalogAttribute", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.attributesConfig.addCatalogAttribute", "parameterOrder": ["attributesConfig"], "parameters": {"attributesConfig": {"description": "Required. Full AttributesConfig resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/attributesConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/attributesConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+attributesConfig}:addCatalogAttribute", "request": {"$ref": "GoogleCloudRetailV2alphaAddCatalogAttributeRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaAttributesConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "batchRemoveCatalogAttributes": {"description": "Removes all specified CatalogAttributes from the AttributesConfig.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/attributesConfig:batchRemoveCatalogAttributes", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.attributesConfig.batchRemoveCatalogAttributes", "parameterOrder": ["attributesConfig"], "parameters": {"attributesConfig": {"description": "Required. The attributes config resource shared by all catalog attributes being deleted. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/attributesConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/attributesConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+attributesConfig}:batchRemoveCatalogAttributes", "request": {"$ref": "GoogleCloudRetailV2alphaBatchRemoveCatalogAttributesRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaBatchRemoveCatalogAttributesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeCatalogAttribute": {"description": "Removes the specified CatalogAttribute from the AttributesConfig. If the CatalogAttribute to remove does not exist, a NOT_FOUND error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/attributesConfig:removeCatalogAttribute", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.attributesConfig.removeCatalogAttribute", "parameterOrder": ["attributesConfig"], "parameters": {"attributesConfig": {"description": "Required. Full AttributesConfig resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/attributesConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/attributesConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+attributesConfig}:removeCatalogAttribute", "request": {"$ref": "GoogleCloudRetailV2alphaRemoveCatalogAttributeRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaAttributesConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "replaceCatalogAttribute": {"description": "Replaces the specified CatalogAttribute in the AttributesConfig by updating the catalog attribute with the same CatalogAttribute.key. If the CatalogAttribute to replace does not exist, a NOT_FOUND error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/attributesConfig:replaceCatalogAttribute", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.attributesConfig.replaceCatalogAttribute", "parameterOrder": ["attributesConfig"], "parameters": {"attributesConfig": {"description": "Required. Full AttributesConfig resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/attributesConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/attributesConfig$", "required": true, "type": "string"}}, "path": "v2alpha/{+attributesConfig}:replaceCatalogAttribute", "request": {"$ref": "GoogleCloudRetailV2alphaReplaceCatalogAttributeRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaAttributesConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "branches": {"methods": {"get": {"description": "Retrieves a Branch.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.branches.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the branch to retrieve. Format: `projects/*/locations/global/catalogs/default_catalog/branches/some_branch_id`. \"default_branch\" can be used as a special branch_id, it returns the default branch that has been set for the catalog.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The view to apply to the returned Branch. Defaults to [Branch.BranchView.BASIC] if unspecified. See documentation of fields of Branch to find what fields are excluded from BASIC view.", "enum": ["BRANCH_VIEW_UNSPECIFIED", "BRANCH_VIEW_BASIC", "BRANCH_VIEW_FULL"], "enumDescriptions": ["The value when it's unspecified. This defaults to the BASIC view.", "Includes basic metadata about the branch, but not statistical fields. See documentation of fields of Branch to find what fields are excluded from BASIC view.", "Includes all fields of a Branch."], "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaBranch"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all instances of Branch under the specified parent Catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.branches.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent catalog resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The view to apply to the returned Branch. Defaults to [Branch.BranchView.BASIC] if unspecified. See documentation of fields of Branch to find what fields are excluded from BASIC view.", "enum": ["BRANCH_VIEW_UNSPECIFIED", "BRANCH_VIEW_BASIC", "BRANCH_VIEW_FULL"], "enumDescriptions": ["The value when it's unspecified. This defaults to the BASIC view.", "Includes basic metadata about the branch, but not statistical fields. See documentation of fields of Branch to find what fields are excluded from BASIC view.", "Includes all fields of a Branch."], "location": "query", "type": "string"}}, "path": "v2alpha/{+parent}/branches", "response": {"$ref": "GoogleCloudRetailV2alphaListBranchesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/operations/{operationsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.branches.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "places": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/places/{placesId}/operations/{operationsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.branches.places.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/places/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "products": {"methods": {"addFulfillmentPlaces": {"description": "We recommend that you use the ProductService.AddLocalInventories method instead of the ProductService.AddFulfillmentPlaces method. ProductService.AddLocalInventories achieves the same results but provides more fine-grained control over ingesting local inventory data. Incrementally adds place IDs to Product.fulfillment_info.place_ids. This process is asynchronous and does not require the Product to exist before updating fulfillment information. If the request is valid, the update will be enqueued and processed downstream. As a consequence, when a response is returned, the added place IDs are not immediately manifested in the Product queried by ProductService.GetProduct or ProductService.ListProducts. The returned Operations will be obsolete after 1 day, and GetOperation API will return NOT_FOUND afterwards. If conflicting updates are issued, the Operations associated with the stale updates will not be marked as done until being obsolete.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}:addFulfillmentPlaces", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.addFulfillmentPlaces", "parameterOrder": ["product"], "parameters": {"product": {"description": "Required. Full resource name of Product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`. If the caller does not have permission to access the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+product}:addFulfillmentPlaces", "request": {"$ref": "GoogleCloudRetailV2alphaAddFulfillmentPlacesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "addLocalInventories": {"description": "Updates local inventory information for a Product at a list of places, while respecting the last update timestamps of each inventory field. This process is asynchronous and does not require the Product to exist before updating inventory information. If the request is valid, the update will be enqueued and processed downstream. As a consequence, when a response is returned, updates are not immediately manifested in the Product queried by ProductService.GetProduct or ProductService.ListProducts. Local inventory information can only be modified using this method. ProductService.CreateProduct and ProductService.UpdateProduct has no effect on local inventories. The returned Operations will be obsolete after 1 day, and GetOperation API will return NOT_FOUND afterwards. If conflicting updates are issued, the Operations associated with the stale updates will not be marked as done until being obsolete.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}:addLocalInventories", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.addLocalInventories", "parameterOrder": ["product"], "parameters": {"product": {"description": "Required. Full resource name of Product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`. If the caller does not have permission to access the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+product}:addLocalInventories", "request": {"$ref": "GoogleCloudRetailV2alphaAddLocalInventoriesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a Product.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent catalog resource name, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+$", "required": true, "type": "string"}, "productId": {"description": "Required. The ID to use for the Product, which will become the final component of the Product.name. If the caller does not have permission to create the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. This field must be unique among all Products with the same parent. Otherwise, an ALREADY_EXISTS error is returned. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}}, "path": "v2alpha/{+parent}/products", "request": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "response": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Product.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}", "httpMethod": "DELETE", "id": "retail.projects.locations.catalogs.branches.products.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "This value only applies to the case when the target product is of type PRIMARY. When deleting a product of VARIANT/COLLECTION type, this value will be ignored. When set to true, the subsequent variant products will be deleted. When set to false, if the primary product has active variant products, an error will be returned.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Full resource name of Product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`. If the caller does not have permission to delete the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the Product to delete does not exist, a NOT_FOUND error is returned. The Product to delete can neither be a Product.Type.COLLECTION Product member nor a Product.Type.PRIMARY Product with more than one variants. Otherwise, an INVALID_ARGUMENT error is returned. All inventory information for the named Product will be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "export": {"description": "Exports multiple Products.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products:export", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.export", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of a Branch, and `default_branch` for branch_id component is supported. For example `projects/1234/locations/global/catalogs/default_catalog/branches/default_branch`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/products:export", "request": {"$ref": "GoogleCloudRetailV2alphaExportProductsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Product.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.branches.products.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`. If the caller does not have permission to access the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested Product does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of multiple Products. Request processing may be synchronous. Non-existing items are created. Note that it is possible for a subset of the Products to be successfully updated.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products:import", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. `projects/1234/locations/global/catalogs/default_catalog/branches/default_branch` If no updateMask is specified, requires products.create permission. If updateMask is specified, requires products.update permission.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/products:import", "request": {"$ref": "GoogleCloudRetailV2alphaImportProductsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of Products.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.branches.products.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to apply on the list results. Supported features: * List all the products under the parent branch if filter is unset. * List Product.Type.VARIANT Products sharing the same Product.Type.PRIMARY Product. For example: `primary_product_id = \"some_product_id\"` * List Products bundled in a Product.Type.COLLECTION Product. For example: `collection_product_id = \"some_product_id\"` * List Products with a partibular type. For example: `type = \"PRIMARY\"` `type = \"VARIANT\"` `type = \"COLLECTION\"` If the field is unrecognizable, an INVALID_ARGUMENT error is returned. If the specified Product.Type.PRIMARY Product or Product.Type.COLLECTION Product does not exist, a NOT_FOUND error is returned.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of Products to return. If unspecified, defaults to 100. The maximum allowed value is 1000. Values above 1000 will be coerced to 1000. If this field is negative, an INVALID_ARGUMENT error is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token ListProductsResponse.next_page_token, received from a previous ProductService.ListProducts call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to ProductService.ListProducts must match the call that provided the page token. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent branch resource name, such as `projects/*/locations/global/catalogs/default_catalog/branches/0`. Use `default_branch` as the branch ID, to list products under the default branch. If the caller does not have permission to list Products under this branch, regardless of whether or not this branch exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "The fields of Product to return in the responses. If not set or empty, the following fields are returned: * Product.name * Product.id * Product.title * Product.uri * Product.images * Product.price_info * Product.brands If \"*\" is provided, all fields are returned. Product.name is always returned no matter what mask is set. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned.", "format": "google-fieldmask", "location": "query", "type": "string"}, "requireTotalSize": {"description": "If true and page_token is empty, ListProductsResponse.total_size is set to the total count of matched items irrespective of pagination. Notice that setting this field to true affects the performance.", "location": "query", "type": "boolean"}}, "path": "v2alpha/{+parent}/products", "response": {"$ref": "GoogleCloudRetailV2alphaListProductsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Product.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.branches.products.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the Product is not found, a new Product will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. Full resource name of the product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/product_id`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Product to update. The immutable and output only fields are NOT supported. If not set, all supported fields (the fields that are neither immutable nor output only) are updated. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned. The attribute key can be updated by setting the mask path as \"attributes.${key_name}\". If a key name is present in the mask but not in the patching product from the request, this key will be deleted after the update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "response": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Permanently deletes all selected Products under a branch. This process is asynchronous. If the request is valid, the removal will be enqueued and processed offline. Depending on the number of Products, this operation could take hours to complete. Before the operation completes, some Products may still be returned by ProductService.GetProduct or ProductService.ListProducts. Depending on the number of Products, this operation could take hours to complete. To get a sample of Products that would be deleted, set PurgeProductsRequest.force to false.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products:purge", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the branch under which the products are created. The format is `projects/${projectId}/locations/global/catalogs/${catalogId}/branches/${branchId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/products:purge", "request": {"$ref": "GoogleCloudRetailV2alphaPurgeProductsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeFulfillmentPlaces": {"description": "We recommend that you use the ProductService.RemoveLocalInventories method instead of the ProductService.RemoveFulfillmentPlaces method. ProductService.RemoveLocalInventories achieves the same results but provides more fine-grained control over ingesting local inventory data. Incrementally removes place IDs from a Product.fulfillment_info.place_ids. This process is asynchronous and does not require the Product to exist before updating fulfillment information. If the request is valid, the update will be enqueued and processed downstream. As a consequence, when a response is returned, the removed place IDs are not immediately manifested in the Product queried by ProductService.GetProduct or ProductService.ListProducts. The returned Operations will be obsolete after 1 day, and GetOperation API will return NOT_FOUND afterwards. If conflicting updates are issued, the Operations associated with the stale updates will not be marked as done until being obsolete.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}:removeFulfillmentPlaces", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.removeFulfillmentPlaces", "parameterOrder": ["product"], "parameters": {"product": {"description": "Required. Full resource name of Product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`. If the caller does not have permission to access the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+product}:removeFulfillmentPlaces", "request": {"$ref": "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeLocalInventories": {"description": "Remove local inventory information for a Product at a list of places at a removal timestamp. This process is asynchronous. If the request is valid, the removal will be enqueued and processed downstream. As a consequence, when a response is returned, removals are not immediately manifested in the Product queried by ProductService.GetProduct or ProductService.ListProducts. Local inventory information can only be removed using this method. ProductService.CreateProduct and ProductService.UpdateProduct has no effect on local inventories. The returned Operations will be obsolete after 1 day, and GetOperation API will return NOT_FOUND afterwards. If conflicting updates are issued, the Operations associated with the stale updates will not be marked as done until being obsolete.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}:removeLocalInventories", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.removeLocalInventories", "parameterOrder": ["product"], "parameters": {"product": {"description": "Required. Full resource name of Product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`. If the caller does not have permission to access the Product, regardless of whether or not it exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+product}:removeLocalInventories", "request": {"$ref": "GoogleCloudRetailV2alphaRemoveLocalInventoriesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setInventory": {"description": "Updates inventory information for a Product while respecting the last update timestamps of each inventory field. This process is asynchronous and does not require the Product to exist before updating fulfillment information. If the request is valid, the update is enqueued and processed downstream. As a consequence, when a response is returned, updates are not immediately manifested in the Product queried by ProductService.GetProduct or ProductService.ListProducts. When inventory is updated with ProductService.CreateProduct and ProductService.UpdateProduct, the specified inventory field value(s) overwrite any existing value(s) while ignoring the last update time for this field. Furthermore, the last update times for the specified inventory fields are overwritten by the times of the ProductService.CreateProduct or ProductService.UpdateProduct request. If no inventory fields are set in CreateProductRequest.product, then any pre-existing inventory information for this product is used. If no inventory fields are set in SetInventoryRequest.set_mask, then any existing inventory information is preserved. Pre-existing inventory information can only be updated with ProductService.SetInventory, ProductService.AddFulfillmentPlaces, and ProductService.RemoveFulfillmentPlaces. The returned Operations is obsolete after one day, and the GetOperation API returns `NOT_FOUND` afterwards. If conflicting updates are issued, the Operations associated with the stale updates are not marked as done until they are obsolete.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/branches/{branchesId}/products/{productsId}:setInventory", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.branches.products.setInventory", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Full resource name of the product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/product_id`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/branches/[^/]+/products/.*$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}:setInventory", "request": {"$ref": "GoogleCloudRetailV2alphaSetInventoryRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "completionData": {"methods": {"import": {"description": "Bulk import of processed completion dataset. Request processing is asynchronous. Partial updating is not supported. The operation is successfully finished only after the imported suggestions are indexed successfully and ready for serving. The process takes hours. This feature is only available for users who have Retail Search enabled. Enable Retail Search on Cloud Console before using this feature.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/completionData:import", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.completionData.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The catalog which the suggestions dataset belongs to. Format: `projects/1234/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/completionData:import", "request": {"$ref": "GoogleCloudRetailV2alphaImportCompletionDataRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "controls": {"methods": {"create": {"description": "Creates a Control. If the Control to create already exists, an ALREADY_EXISTS error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/controls", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.controls.create", "parameterOrder": ["parent"], "parameters": {"controlId": {"description": "Required. The ID to use for the Control, which will become the final component of the Control's resource name. This value should be 4-63 characters, and valid characters are /a-z-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. Full resource name of parent catalog. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/controls", "request": {"$ref": "GoogleCloudRetailV2alphaControl"}, "response": {"$ref": "GoogleCloudRetailV2alphaControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Control. If the Control to delete does not exist, a NOT_FOUND error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/controls/{controlsId}", "httpMethod": "DELETE", "id": "retail.projects.locations.catalogs.controls.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Control to delete. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/controls/{control_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/controls/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Control.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/controls/{controlsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.controls.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Control to get. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/controls/{control_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/controls/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Controls by their parent Catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/controls", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.controls.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to apply on the list results. Supported features: * List all the products under the parent branch if filter is unset. * List controls that are used in a single ServingConfig: 'serving_config = \"boosted_home_page_cvr\"'", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of results to return. If unspecified, defaults to 50. Max allowed value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListControls` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The catalog resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/controls", "response": {"$ref": "GoogleCloudRetailV2alphaListControlsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Control. Control cannot be set to a different oneof field, if so an INVALID_ARGUMENT is returned. If the Control to update does not exist, a NOT_FOUND error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/controls/{controlsId}", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.controls.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Fully qualified name `projects/*/locations/global/catalogs/*/controls/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/controls/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Control to update. The following are NOT supported: * Control.name If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaControl"}, "response": {"$ref": "GoogleCloudRetailV2alphaControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "generativeQuestion": {"methods": {"batchUpdate": {"description": "Allows management of multiple questions.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/generativeQuestion:batchUpdate", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.generativeQuestion.batchUpdate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Optional. Resource name of the parent catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/generativeQuestion:batchUpdate", "request": {"$ref": "GoogleCloudRetailV2alphaBatchUpdateGenerativeQuestionConfigsRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaBatchUpdateGenerativeQuestionConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "generativeQuestions": {"methods": {"list": {"description": "Returns all questions for a given catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/generativeQuestions", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.generativeQuestions.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the parent catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/generativeQuestions", "response": {"$ref": "GoogleCloudRetailV2alphaListGenerativeQuestionConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "merchantCenterAccountLinks": {"methods": {"create": {"description": "Creates a MerchantCenterAccountLink.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/merchantCenterAccountLinks", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.merchantCenterAccountLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The branch resource where this MerchantCenterAccountLink will be created. Format: `projects/{PROJECT_NUMBER}/locations/global/catalogs/{CATALOG_ID}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/merchantCenterAccountLinks", "request": {"$ref": "GoogleCloudRetailV2alphaMerchantCenterAccountLink"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a MerchantCenterAccountLink. If the MerchantCenterAccountLink to delete does not exist, a NOT_FOUND error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/merchantCenterAccountLinks/{merchantCenterAccountLinksId}", "httpMethod": "DELETE", "id": "retail.projects.locations.catalogs.merchantCenterAccountLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/merchantCenterAccountLinks/{merchant_center_account_link_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/merchantCenterAccountLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all MerchantCenterAccountLinks under the specified parent Catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/merchantCenterAccountLinks", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.merchantCenterAccountLinks.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent Catalog of the resource. It must match this format: `projects/{PROJECT_NUMBER}/locations/global/catalogs/{CATALOG_ID}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/merchantCenterAccountLinks", "response": {"$ref": "GoogleCloudRetailV2alphaListMerchantCenterAccountLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "models": {"methods": {"create": {"description": "Creates a new model.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.models.create", "parameterOrder": ["parent"], "parameters": {"dryRun": {"description": "Optional. Whether to run a dry run to validate the request (without actually creating the model).", "location": "query", "type": "boolean"}, "parent": {"description": "Required. The parent resource under which to create the model. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/models", "request": {"$ref": "GoogleCloudRetailV2alphaModel"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an existing model.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models/{modelsId}", "httpMethod": "DELETE", "id": "retail.projects.locations.catalogs.models.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Model to delete. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a model.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models/{modelsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.models.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Model to get. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaModel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the models linked to this event store.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.models.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Maximum number of results to return. If unspecified, defaults to 50. Max allowed value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListModels` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent for which to list models. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/models", "response": {"$ref": "GoogleCloudRetailV2alphaListModelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update of model metadata. Only fields that currently can be updated are: `filtering_option` and `periodic_tuning_state`. If other values are provided, this API method ignores them.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models/{modelsId}", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.models.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified resource name of the model. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}` catalog_id has char limit of 50. recommendation_model_id has char limit of 40.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/models/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided 'model' to update. If not set, by default updates all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaModel"}, "response": {"$ref": "GoogleCloudRetailV2alphaModel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "pause": {"description": "Pauses the training of an existing model.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models/{modelsId}:pause", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.models.pause", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the model to pause. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}:pause", "request": {"$ref": "GoogleCloudRetailV2alphaPauseModelRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaModel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Resumes the training of an existing model.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models/{modelsId}:resume", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.models.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the model to resume. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}:resume", "request": {"$ref": "GoogleCloudRetailV2alphaResumeModelRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaModel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "tune": {"description": "<PERSON><PERSON> an existing model.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/models/{modelsId}:tune", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.models.tune", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the model to tune. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}:tune", "request": {"$ref": "GoogleCloudRetailV2alphaTuneModelRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/operations/{operationsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/operations", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "placements": {"methods": {"conversationalSearch": {"description": "Performs a conversational search. This feature is only available for users who have Conversational Search enabled.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/placements/{placementsId}:conversationalSearch", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.placements.conversationalSearch", "parameterOrder": ["placement"], "parameters": {"placement": {"description": "Required. The resource name of the search engine placement, such as `projects/*/locations/global/catalogs/default_catalog/placements/default_search` or `projects/*/locations/global/catalogs/default_catalog/servingConfigs/default_serving_config` This field is used to identify the serving config name and the set of models that will be used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/placements/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+placement}:conversationalSearch", "request": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "predict": {"description": "Makes a recommendation prediction.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/placements/{placementsId}:predict", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.placements.predict", "parameterOrder": ["placement"], "parameters": {"placement": {"description": "Required. Full resource name of the format: `{placement=projects/*/locations/global/catalogs/default_catalog/servingConfigs/*}` or `{placement=projects/*/locations/global/catalogs/default_catalog/placements/*}`. We recommend using the `servingConfigs` resource. `placements` is a legacy resource. The ID of the Recommendations AI serving config or placement. Before you can request predictions from your model, you must create at least one serving config or placement for it. For more information, see [Manage serving configs] (https://cloud.google.com/retail/docs/manage-configs). The full list of available serving configs can be seen at https://console.cloud.google.com/ai/retail/catalogs/default_catalog/configs", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/placements/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+placement}:predict", "request": {"$ref": "GoogleCloudRetailV2alphaPredictRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaPredictResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Performs a search. This feature is only available for users who have Retail Search enabled. Enable Retail Search on Cloud Console before using this feature.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/placements/{placementsId}:search", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.placements.search", "parameterOrder": ["placement"], "parameters": {"placement": {"description": "Required. The resource name of the Retail Search serving config, such as `projects/*/locations/global/catalogs/default_catalog/servingConfigs/default_serving_config` or the name of the legacy placement resource, such as `projects/*/locations/global/catalogs/default_catalog/placements/default_search`. This field is used to identify the serving config name and the set of models that are used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/placements/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+placement}:search", "request": {"$ref": "GoogleCloudRetailV2alphaSearchRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "servingConfigs": {"methods": {"addControl": {"description": "Enables a Control on the specified ServingConfig. The control is added in the last position of the list of controls it belongs to (e.g. if it's a facet spec control it will be applied in the last position of servingConfig.facetSpecIds) Returns a ALREADY_EXISTS error if the control has already been applied. Returns a FAILED_PRECONDITION error if the addition could exceed maximum number of control allowed for that type of control.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}:addControl", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.servingConfigs.addControl", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. The source ServingConfig resource name . Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+servingConfig}:addControl", "request": {"$ref": "GoogleCloudRetailV2alphaAddControlRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "conversationalSearch": {"description": "Performs a conversational search. This feature is only available for users who have Conversational Search enabled.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}:conversationalSearch", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.servingConfigs.conversationalSearch", "parameterOrder": ["placement"], "parameters": {"placement": {"description": "Required. The resource name of the search engine placement, such as `projects/*/locations/global/catalogs/default_catalog/placements/default_search` or `projects/*/locations/global/catalogs/default_catalog/servingConfigs/default_serving_config` This field is used to identify the serving config name and the set of models that will be used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+placement}:conversationalSearch", "request": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a ServingConfig. A maximum of 100 ServingConfigs are allowed in a Catalog, otherwise a FAILED_PRECONDITION error is returned.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.servingConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Full resource name of parent. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "servingConfigId": {"description": "Required. The ID to use for the ServingConfig, which will become the final component of the ServingConfig's resource name. This value should be 4-63 characters, and valid characters are /a-z-_/.", "location": "query", "type": "string"}}, "path": "v2alpha/{+parent}/servingConfigs", "request": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a ServingConfig. Returns a NotFound error if the ServingConfig does not exist.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}", "httpMethod": "DELETE", "id": "retail.projects.locations.catalogs.servingConfigs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ServingConfig to delete. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a ServingConfig. Returns a NotFound error if the ServingConfig does not exist.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.servingConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ServingConfig to get. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all ServingConfigs linked to this catalog.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs", "httpMethod": "GET", "id": "retail.projects.locations.catalogs.servingConfigs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Maximum number of results to return. If unspecified, defaults to 100. If a value greater than 100 is provided, at most 100 results are returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListServingConfigs` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The catalog resource name. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/servingConfigs", "response": {"$ref": "GoogleCloudRetailV2alphaListServingConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a ServingConfig.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}", "httpMethod": "PATCH", "id": "retail.projects.locations.catalogs.servingConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Fully qualified name `projects/*/locations/global/catalogs/*/servingConfig/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided ServingConfig to update. The following are NOT supported: * ServingConfig.name If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}", "request": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "response": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "predict": {"description": "Makes a recommendation prediction.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}:predict", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.servingConfigs.predict", "parameterOrder": ["placement"], "parameters": {"placement": {"description": "Required. Full resource name of the format: `{placement=projects/*/locations/global/catalogs/default_catalog/servingConfigs/*}` or `{placement=projects/*/locations/global/catalogs/default_catalog/placements/*}`. We recommend using the `servingConfigs` resource. `placements` is a legacy resource. The ID of the Recommendations AI serving config or placement. Before you can request predictions from your model, you must create at least one serving config or placement for it. For more information, see [Manage serving configs] (https://cloud.google.com/retail/docs/manage-configs). The full list of available serving configs can be seen at https://console.cloud.google.com/ai/retail/catalogs/default_catalog/configs", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+placement}:predict", "request": {"$ref": "GoogleCloudRetailV2alphaPredictRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaPredictResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeControl": {"description": "Disables a Control on the specified ServingConfig. The control is removed from the ServingConfig. Returns a NOT_FOUND error if the Control is not enabled for the ServingConfig.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}:removeControl", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.servingConfigs.removeControl", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. The source ServingConfig resource name . Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+servingConfig}:removeControl", "request": {"$ref": "GoogleCloudRetailV2alphaRemoveControlRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Performs a search. This feature is only available for users who have Retail Search enabled. Enable Retail Search on Cloud Console before using this feature.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/servingConfigs/{servingConfigsId}:search", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.servingConfigs.search", "parameterOrder": ["placement"], "parameters": {"placement": {"description": "Required. The resource name of the Retail Search serving config, such as `projects/*/locations/global/catalogs/default_catalog/servingConfigs/default_serving_config` or the name of the legacy placement resource, such as `projects/*/locations/global/catalogs/default_catalog/placements/default_search`. This field is used to identify the serving config name and the set of models that are used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+placement}:search", "request": {"$ref": "GoogleCloudRetailV2alphaSearchRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "userEvents": {"methods": {"collect": {"description": "Writes a single user event from the browser. For larger user event payload over 16 KB, the POST method should be used instead, otherwise a 400 Bad Request error is returned. This method is used only by the Retail API JavaScript pixel and Google Tag Manager. Users should not call this method directly.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/userEvents:collect", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.userEvents.collect", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent catalog name, such as `projects/1234/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/userEvents:collect", "request": {"$ref": "GoogleCloudRetailV2alphaCollectUserEventRequest"}, "response": {"$ref": "GoogleApiHttpBody"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "export": {"description": "Exports user events. `Operation.response` is of type `ExportResponse`. `Operation.metadata` is of type `ExportMetadata`.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/userEvents:export", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.userEvents.export", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of a Catalog. For example `projects/1234/locations/global/catalogs/default_catalog`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/userEvents:export", "request": {"$ref": "GoogleCloudRetailV2alphaExportUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of User events. Request processing might be synchronous. Events that already exist are skipped. Use this method for backfilling historical user events. `Operation.response` is of type `ImportResponse`. Note that it is possible for a subset of the items to be successfully inserted. `Operation.metadata` is of type `ImportMetadata`.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/userEvents:import", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.userEvents.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. `projects/1234/locations/global/catalogs/default_catalog`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/userEvents:import", "request": {"$ref": "GoogleCloudRetailV2alphaImportUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Deletes permanently all user events specified by the filter provided. Depending on the number of events specified by the filter, this operation could take hours or days to complete. To test a filter, use the list command first.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/userEvents:purge", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.userEvents.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the catalog under which the events are created. The format is `projects/${projectId}/locations/global/catalogs/${catalogId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/userEvents:purge", "request": {"$ref": "GoogleCloudRetailV2alphaPurgeUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rejoin": {"description": "Starts a user-event rejoin operation with latest product catalog. Events are not annotated with detailed product information for products that are missing from the catalog when the user event is ingested. These events are stored as unjoined events with limited usage on training and serving. You can use this method to start a join operation on specified events with the latest version of product catalog. You can also use this method to correct events joined with the wrong product catalog. A rejoin operation can take hours or days to complete.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/userEvents:rejoin", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.userEvents.rejoin", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent catalog resource name, such as `projects/1234/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+parent}/userEvents:rejoin", "request": {"$ref": "GoogleCloudRetailV2alphaRejoinUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "write": {"description": "Writes a single user event.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/userEvents:write", "httpMethod": "POST", "id": "retail.projects.locations.catalogs.userEvents.write", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent catalog resource name, such as `projects/1234/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "writeAsync": {"description": "If set to true, the user event will be written asynchronously after validation, and the API will respond without waiting for the write. Therefore, silent failures can occur even if the API returns success. In case of silent failures, error messages can be found in Stackdriver logs.", "location": "query", "type": "boolean"}}, "path": "v2alpha/{+parent}/userEvents:write", "request": {"$ref": "GoogleCloudRetailV2alphaUserEvent"}, "response": {"$ref": "GoogleCloudRetailV2alphaUserEvent"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "retail.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v2alpha/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "retail.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v2alpha/projects/{projectsId}/operations/{operationsId}", "httpMethod": "GET", "id": "retail.projects.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v2alpha/projects/{projectsId}/operations", "httpMethod": "GET", "id": "retail.projects.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v2alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "retailProject": {"methods": {"acceptTerms": {"description": "Accepts service terms for this project. By making requests to this API, you agree to the terms of service linked below. https://cloud.google.com/retail/data-use-terms", "flatPath": "v2alpha/projects/{projectsId}/retailProject:acceptTerms", "httpMethod": "POST", "id": "retail.projects.retailProject.acceptTerms", "parameterOrder": ["project"], "parameters": {"project": {"description": "Required. Full resource name of the project. Format: `projects/{project_number_or_id}/retailProject`", "location": "path", "pattern": "^projects/[^/]+/retailProject$", "required": true, "type": "string"}}, "path": "v2alpha/{+project}:acceptTerms", "request": {"$ref": "GoogleCloudRetailV2alphaAcceptTermsRequest"}, "response": {"$ref": "GoogleCloudRetailV2alphaProject"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "20250710", "rootUrl": "https://retail.googleapis.com/", "schemas": {"GoogleApiHttpBody": {"description": "Message that represents an arbitrary HTTP body. It should only be used for payload formats that can't be represented as JSON, such as raw binary or an HTML page. This message can be used both in streaming and non-streaming API methods in the request as well as the response. It can be used as a top-level request field, which is convenient if one wants to extract parameters from either the URL or HTTP template into the request fields and also want access to the raw HTTP body. Example: message GetResourceRequest { // A unique request id. string request_id = 1; // The raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; } service ResourceService { rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); } Example with streaming methods: service CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); } Use of this type only changes how the request and response bodies are handled, all other features will continue to work unchanged.", "id": "GoogleApiHttpBody", "properties": {"contentType": {"description": "The HTTP Content-Type header value specifying the content type of the body.", "type": "string"}, "data": {"description": "The HTTP request/response body as raw binary.", "format": "byte", "type": "string"}, "extensions": {"description": "Application specific response metadata. Must be set in the first response for streaming APIs.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailLoggingErrorContext": {"description": "A description of the context in which an error occurred.", "id": "GoogleCloudRetailLoggingErrorContext", "properties": {"httpRequest": {"$ref": "GoogleCloudRetailLoggingHttpRequestContext", "description": "The HTTP request which was processed when the error was triggered."}, "reportLocation": {"$ref": "GoogleCloudRetailLoggingSourceLocation", "description": "The location in the source code where the decision was made to report the error, usually the place where it was logged."}}, "type": "object"}, "GoogleCloudRetailLoggingErrorLog": {"description": "An error log which is reported to the Error Reporting system. This proto a superset of google.devtools.clouderrorreporting.v1beta1.ReportedErrorEvent.", "id": "GoogleCloudRetailLoggingErrorLog", "properties": {"context": {"$ref": "GoogleCloudRetailLoggingErrorContext", "description": "A description of the context in which the error occurred."}, "importPayload": {"$ref": "GoogleCloudRetailLoggingImportErrorContext", "description": "The error payload that is populated on LRO import APIs."}, "message": {"description": "A message describing the error.", "type": "string"}, "requestPayload": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The API request payload, represented as a protocol buffer. Most API request types are supported. For example: \"type.googleapis.com/google.cloud.retail.v2.ProductService.CreateProductRequest\" \"type.googleapis.com/google.cloud.retail.v2.UserEventService.WriteUserEventRequest\"", "type": "object"}, "responsePayload": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The API response payload, represented as a protocol buffer. This is used to log some \"soft errors\", where the response is valid but we consider there are some quality issues like unjoined events. The following API responses are supported and no PII is included: \"google.cloud.retail.v2.PredictionService.Predict\" \"google.cloud.retail.v2.UserEventService.WriteUserEvent\" \"google.cloud.retail.v2.UserEventService.CollectUserEvent\"", "type": "object"}, "serviceContext": {"$ref": "GoogleCloudRetailLoggingServiceContext", "description": "The service context in which this error has occurred."}, "status": {"$ref": "GoogleRpcStatus", "description": "The RPC status associated with the error log."}}, "type": "object"}, "GoogleCloudRetailLoggingHttpRequestContext": {"description": "HTTP request data that is related to a reported error.", "id": "GoogleCloudRetailLoggingHttpRequestContext", "properties": {"responseStatusCode": {"description": "The HTTP response status code for the request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailLoggingImportErrorContext": {"description": "The error payload that is populated on LRO import APIs, including \"google.cloud.retail.v2.ProductService.ImportProducts\" and \"google.cloud.retail.v2.EventService.ImportUserEvents\".", "id": "GoogleCloudRetailLoggingImportErrorContext", "properties": {"catalogItem": {"description": "The detailed content which caused the error on importing a catalog item.", "type": "string"}, "gcsPath": {"description": "Cloud Storage file path of the import source. Can be set for batch operation error.", "type": "string"}, "lineNumber": {"description": "Line number of the content in file. Should be empty for permission or batch operation error.", "type": "string"}, "operationName": {"description": "The operation resource name of the LRO.", "type": "string"}, "product": {"description": "The detailed content which caused the error on importing a product.", "type": "string"}, "userEvent": {"description": "The detailed content which caused the error on importing a user event.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailLoggingServiceContext": {"description": "Describes a running service that sends errors.", "id": "GoogleCloudRetailLoggingServiceContext", "properties": {"service": {"description": "An identifier of the service. For example, \"retail.googleapis.com\".", "type": "string"}}, "type": "object"}, "GoogleCloudRetailLoggingSourceLocation": {"description": "Indicates a location in the source code of the service for which errors are reported.", "id": "GoogleCloudRetailLoggingSourceLocation", "properties": {"functionName": {"description": "Human-readable name of a function or method. For example, \"google.cloud.retail.v2.UserEventService.ImportUserEvents\".", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2AddFulfillmentPlacesMetadata": {"description": "Metadata related to the progress of the AddFulfillmentPlaces operation. Currently empty because there is no meaningful metadata populated from the ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2AddFulfillmentPlacesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2AddFulfillmentPlacesResponse": {"description": "Response of the AddFulfillmentPlacesRequest. Currently empty because there is no meaningful response populated from the ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2AddFulfillmentPlacesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2AddLocalInventoriesMetadata": {"description": "Metadata related to the progress of the AddLocalInventories operation. Currently empty because there is no meaningful metadata populated from the ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2AddLocalInventoriesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2AddLocalInventoriesResponse": {"description": "Response of the ProductService.AddLocalInventories API. Currently empty because there is no meaningful response populated from the ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2AddLocalInventoriesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2BigQueryOutputResult": {"description": "A BigQuery output result.", "id": "GoogleCloudRetailV2BigQueryOutputResult", "properties": {"datasetId": {"description": "The ID of a BigQuery Dataset.", "type": "string"}, "tableId": {"description": "The ID of a BigQuery Table.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2CreateModelMetadata": {"description": "Metadata associated with a create operation.", "id": "GoogleCloudRetailV2CreateModelMetadata", "properties": {"model": {"description": "The resource name of the model that this create applies to. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ExportAnalyticsMetricsResponse": {"description": "Response of the ExportAnalyticsMetricsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2ExportAnalyticsMetricsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2ExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2OutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2ExportErrorsConfig": {"description": "Configuration of destination for Export related errors.", "id": "GoogleCloudRetailV2ExportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage path for import errors. This must be an empty, existing Cloud Storage bucket. Export errors will be written to a file in this bucket, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ExportMetadata": {"description": "Metadata related to the progress of the Export operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2ExportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2GcsOutputResult": {"description": "A Gcs output result.", "id": "GoogleCloudRetailV2GcsOutputResult", "properties": {"outputUri": {"description": "The uri of Gcs output", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ImportCompletionDataResponse": {"description": "Response of the ImportCompletionDataRequest. If the long running operation is done, this message is returned by the google.longrunning.Operations.response field if the operation is successful.", "id": "GoogleCloudRetailV2ImportCompletionDataResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2ImportErrorsConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudRetailV2ImportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage prefix for import errors. This must be an empty, existing Cloud Storage directory. Import errors are written to sharded files in this directory, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ImportMetadata": {"description": "Metadata related to the progress of the Import operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2ImportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "notificationPubsubTopic": {"description": "Pub/Sub topic for receiving notification. If this field is set, when the import is finished, a notification is sent to specified Pub/Sub topic. The message data is JSON string of a Operation. Format of the Pub/Sub topic is `projects/{project}/topics/{topic}`.", "type": "string"}, "requestId": {"deprecated": true, "description": "Deprecated. This field is never set.", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ImportProductsResponse": {"description": "Response of the ImportProductsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2ImportProductsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2ImportErrorsConfig", "description": "Echoes the destination for the complete errors in the request if set."}}, "type": "object"}, "GoogleCloudRetailV2ImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2ImportUserEventsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2ImportErrorsConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "importSummary": {"$ref": "GoogleCloudRetailV2UserEventImportSummary", "description": "Aggregated statistics of user event import status."}}, "type": "object"}, "GoogleCloudRetailV2Model": {"description": "Metadata that describes the training and serving parameters of a Model. A Model can be associated with a ServingConfig and then queried through the Predict API.", "id": "GoogleCloudRetailV2Model", "properties": {"createTime": {"description": "Output only. Timestamp the Recommendation Model was created at.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataState": {"description": "Output only. The state of data requirements for this model: `DATA_OK` and `DATA_ERROR`. Recommendation model cannot be trained if the data is in `DATA_ERROR` state. Recommendation model can have `DATA_ERROR` state even if serving state is `ACTIVE`: models were trained successfully before, but cannot be refreshed because model no longer has sufficient data for training.", "enum": ["DATA_STATE_UNSPECIFIED", "DATA_OK", "DATA_ERROR"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The model has sufficient training data.", "The model does not have sufficient training data. Error messages can be queried via Stackdriver."], "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The display name of the model. Should be human readable, used to display Recommendation Models in the Retail Cloud Console Dashboard. UTF-8 encoded string with limit of 1024 characters.", "type": "string"}, "filteringOption": {"description": "Optional. If `RECOMMENDATIONS_FILTERING_ENABLED`, recommendation filtering by attributes is enabled for the model.", "enum": ["RECOMMENDATIONS_FILTERING_OPTION_UNSPECIFIED", "RECOMMENDATIONS_FILTERING_DISABLED", "RECOMMENDATIONS_FILTERING_ENABLED"], "enumDescriptions": ["Value used when unset. In this case, server behavior defaults to RECOMMENDATIONS_FILTERING_DISABLED.", "Recommendation filtering is disabled.", "Recommendation filtering is enabled."], "type": "string"}, "lastTuneTime": {"description": "Output only. The timestamp when the latest successful tune finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "modelFeaturesConfig": {"$ref": "GoogleCloudRetailV2ModelModelFeaturesConfig", "description": "Optional. Additional model features config."}, "name": {"description": "Required. The fully qualified resource name of the model. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}` catalog_id has char limit of 50. recommendation_model_id has char limit of 40.", "type": "string"}, "optimizationObjective": {"description": "Optional. The optimization objective e.g. `cvr`. Currently supported values: `ctr`, `cvr`, `revenue-per-order`. If not specified, we choose default based on model type. Default depends on type of recommendation: `recommended-for-you` => `ctr` `others-you-may-like` => `ctr` `frequently-bought-together` => `revenue_per_order` This field together with optimization_objective describe model metadata to use to control model training and serving. See https://cloud.google.com/retail/docs/models for more details on what the model metadata control and which combination of parameters are valid. For invalid combinations of parameters (e.g. type = `frequently-bought-together` and optimization_objective = `ctr`), you receive an error 400 if you try to create/update a recommendation with this set of knobs.", "type": "string"}, "periodicTuningState": {"description": "Optional. The state of periodic tuning. The period we use is 3 months - to do a one-off tune earlier use the `TuneModel` method. Default value is `PERIODIC_TUNING_ENABLED`.", "enum": ["PERIODIC_TUNING_STATE_UNSPECIFIED", "PERIODIC_TUNING_DISABLED", "ALL_TUNING_DISABLED", "PERIODIC_TUNING_ENABLED"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The model has periodic tuning disabled. Tuning can be reenabled by calling the `EnableModelPeriodicTuning` method or by calling the `TuneModel` method.", "The model cannot be tuned with periodic tuning OR the `TuneModel` method. Hide the options in customer UI and reject any requests through the backend self serve API.", "The model has periodic tuning enabled. Tuning can be disabled by calling the `DisableModelPeriodicTuning` method."], "type": "string"}, "servingConfigLists": {"description": "Output only. The list of valid serving configs associated with the PageOptimizationConfig.", "items": {"$ref": "GoogleCloudRetailV2ModelServingConfigList"}, "readOnly": true, "type": "array"}, "servingState": {"description": "Output only. The serving state of the model: `ACTIVE`, `NOT_ACTIVE`.", "enum": ["SERVING_STATE_UNSPECIFIED", "INACTIVE", "ACTIVE", "TUNED"], "enumDescriptions": ["Unspecified serving state.", "The model is not serving.", "The model is serving and can be queried.", "The model is trained on tuned hyperparameters and can be queried."], "readOnly": true, "type": "string"}, "trainingState": {"description": "Optional. The training state that the model is in (e.g. `TRAINING` or `PAUSED`). Since part of the cost of running the service is frequency of training - this can be used to determine when to train model in order to control cost. If not specified: the default value for `CreateModel` method is `TRAINING`. The default value for `UpdateModel` method is to keep the state the same as before.", "enum": ["TRAINING_STATE_UNSPECIFIED", "PAUSED", "TRAINING"], "enumDescriptions": ["Unspecified training state.", "The model training is paused.", "The model is training."], "type": "string"}, "tuningOperation": {"description": "Output only. The tune operation associated with the model. Can be used to determine if there is an ongoing tune for this recommendation. Empty field implies no tune is goig on.", "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of model e.g. `home-page`. Currently supported values: `recommended-for-you`, `others-you-may-like`, `frequently-bought-together`, `page-optimization`, `similar-items`, `buy-it-again`, `on-sale-items`, and `recently-viewed`(readonly value). This field together with optimization_objective describe model metadata to use to control model training and serving. See https://cloud.google.com/retail/docs/models for more details on what the model metadata control and which combination of parameters are valid. For invalid combinations of parameters (e.g. type = `frequently-bought-together` and optimization_objective = `ctr`), you receive an error 400 if you try to create/update a recommendation with this set of knobs.", "type": "string"}, "updateTime": {"description": "Output only. Timestamp the Recommendation Model was last updated. E.g. if a Recommendation Model was paused - this would be the time the pause was initiated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ModelFrequentlyBoughtTogetherFeaturesConfig": {"description": "Additional configs for the frequently-bought-together model type.", "id": "GoogleCloudRetailV2ModelFrequentlyBoughtTogetherFeaturesConfig", "properties": {"contextProductsType": {"description": "Optional. Specifies the context of the model when it is used in predict requests. Can only be set for the `frequently-bought-together` type. If it isn't specified, it defaults to MULTIPLE_CONTEXT_PRODUCTS.", "enum": ["CONTEXT_PRODUCTS_TYPE_UNSPECIFIED", "SINGLE_CONTEXT_PRODUCT", "MULTIPLE_CONTEXT_PRODUCTS"], "enumDescriptions": ["Unspecified default value, should never be explicitly set. Defaults to MULTIPLE_CONTEXT_PRODUCTS.", "Use only a single product as context for the recommendation. Typically used on pages like add-to-cart or product details.", "Use one or multiple products as context for the recommendation. Typically used on shopping cart pages."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2ModelModelFeaturesConfig": {"description": "Additional model features config.", "id": "GoogleCloudRetailV2ModelModelFeaturesConfig", "properties": {"frequentlyBoughtTogetherConfig": {"$ref": "GoogleCloudRetailV2ModelFrequentlyBoughtTogetherFeaturesConfig", "description": "Additional configs for frequently-bought-together models."}}, "type": "object"}, "GoogleCloudRetailV2ModelServingConfigList": {"description": "Represents an ordered combination of valid serving configs, which can be used for `PAGE_OPTIMIZATION` recommendations.", "id": "GoogleCloudRetailV2ModelServingConfigList", "properties": {"servingConfigIds": {"description": "Optional. A set of valid serving configs that may be used for `PAGE_OPTIMIZATION`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2OutputResult": {"description": "Output result that stores the information about where the exported data is stored.", "id": "GoogleCloudRetailV2OutputResult", "properties": {"bigqueryResult": {"description": "The BigQuery location where the result is stored.", "items": {"$ref": "GoogleCloudRetailV2BigQueryOutputResult"}, "type": "array"}, "gcsResult": {"description": "The Google Cloud Storage location where the result is stored.", "items": {"$ref": "GoogleCloudRetailV2GcsOutputResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2PurgeMetadata": {"description": "Metadata related to the progress of the Purge operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2PurgeMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2PurgeProductsMetadata": {"description": "Metadata related to the progress of the PurgeProducts operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2PurgeProductsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2PurgeProductsResponse": {"description": "Response of the PurgeProductsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRetailV2PurgeProductsResponse", "properties": {"purgeCount": {"description": "The total count of products purged as a result of the operation.", "format": "int64", "type": "string"}, "purgeSample": {"description": "A sample of the product names that will be deleted. Only populated if `force` is set to false. A max of 100 names will be returned and the names are chosen at random.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2PurgeUserEventsResponse": {"description": "Response of the PurgeUserEventsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRetailV2PurgeUserEventsResponse", "properties": {"purgedEventsCount": {"description": "The total count of events purged as a result of the operation.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2RejoinUserEventsMetadata": {"description": "Metadata for `RejoinUserEvents` method.", "id": "GoogleCloudRetailV2RejoinUserEventsMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2RejoinUserEventsResponse": {"description": "Response message for `RejoinUserEvents` method.", "id": "GoogleCloudRetailV2RejoinUserEventsResponse", "properties": {"rejoinedUserEventsCount": {"description": "Number of user events that were joined with latest product catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2RemoveFulfillmentPlacesMetadata": {"description": "Metadata related to the progress of the RemoveFulfillmentPlaces operation. Currently empty because there is no meaningful metadata populated from the ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2RemoveFulfillmentPlacesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2RemoveFulfillmentPlacesResponse": {"description": "Response of the RemoveFulfillmentPlacesRequest. Currently empty because there is no meaningful response populated from the ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2RemoveFulfillmentPlacesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2RemoveLocalInventoriesMetadata": {"description": "Metadata related to the progress of the RemoveLocalInventories operation. Currently empty because there is no meaningful metadata populated from the ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2RemoveLocalInventoriesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2RemoveLocalInventoriesResponse": {"description": "Response of the ProductService.RemoveLocalInventories API. Currently empty because there is no meaningful response populated from the ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2RemoveLocalInventoriesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2SetInventoryMetadata": {"description": "Metadata related to the progress of the SetInventory operation. Currently empty because there is no meaningful metadata populated from the ProductService.SetInventory method.", "id": "GoogleCloudRetailV2SetInventoryMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2SetInventoryResponse": {"description": "Response of the SetInventoryRequest. Currently empty because there is no meaningful response populated from the ProductService.SetInventory method.", "id": "GoogleCloudRetailV2SetInventoryResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2TuneModelMetadata": {"description": "Metadata associated with a tune operation.", "id": "GoogleCloudRetailV2TuneModelMetadata", "properties": {"model": {"description": "The resource name of the model that this tune applies to. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2TuneModelResponse": {"description": "Response associated with a tune operation.", "id": "GoogleCloudRetailV2TuneModelResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2UserEventImportSummary": {"description": "A summary of import result. The UserEventImportSummary summarizes the import status for user events.", "id": "GoogleCloudRetailV2UserEventImportSummary", "properties": {"joinedEventsCount": {"description": "Count of user events imported with complete existing catalog information.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with catalog information not found in the imported catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaAcceptTermsRequest": {"description": "Request for AcceptTerms method.", "id": "GoogleCloudRetailV2alphaAcceptTermsRequest", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaAddCatalogAttributeRequest": {"description": "Request for CatalogService.AddCatalogAttribute method.", "id": "GoogleCloudRetailV2alphaAddCatalogAttributeRequest", "properties": {"catalogAttribute": {"$ref": "GoogleCloudRetailV2alphaCatalogAttribute", "description": "Required. The CatalogAttribute to add."}}, "type": "object"}, "GoogleCloudRetailV2alphaAddControlRequest": {"description": "Request for AddControl method.", "id": "GoogleCloudRetailV2alphaAddControlRequest", "properties": {"controlId": {"description": "Required. The id of the control to apply. Assumed to be in the same catalog as the serving config - if id is not found a NOT_FOUND error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaAddFulfillmentPlacesMetadata": {"description": "Metadata related to the progress of the AddFulfillmentPlaces operation. Currently empty because there is no meaningful metadata populated from the ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2alphaAddFulfillmentPlacesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaAddFulfillmentPlacesRequest": {"description": "Request message for ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2alphaAddFulfillmentPlacesRequest", "properties": {"addTime": {"description": "The time when the fulfillment updates are issued, used to prevent out-of-order updates on fulfillment information. If not provided, the internal system time will be used.", "format": "google-datetime", "type": "string"}, "allowMissing": {"description": "If set to true, and the Product is not found, the fulfillment information will still be processed and retained for at most 1 day and processed once the Product is created. If set to false, a NOT_FOUND error is returned if the Product is not found.", "type": "boolean"}, "placeIds": {"description": "Required. The IDs for this type, such as the store IDs for \"pickup-in-store\" or the region IDs for \"same-day-delivery\" to be added for this type. Duplicate IDs will be automatically ignored. At least 1 value is required, and a maximum of 2000 values are allowed. Each value must be a string with a length limit of 10 characters, matching the pattern `[a-zA-Z0-9_-]+`, such as \"store1\" or \"REGION-2\". Otherwise, an INVALID_ARGUMENT error is returned. If the total number of place IDs exceeds 2000 for this type after adding, then the update will be rejected.", "items": {"type": "string"}, "type": "array"}, "type": {"description": "Required. The fulfillment type, including commonly used types (such as pickup in store and same day delivery), and custom types. Supported values: * \"pickup-in-store\" * \"ship-to-store\" * \"same-day-delivery\" * \"next-day-delivery\" * \"custom-type-1\" * \"custom-type-2\" * \"custom-type-3\" * \"custom-type-4\" * \"custom-type-5\" If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned. This field directly corresponds to Product.fulfillment_info.type.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaAddFulfillmentPlacesResponse": {"description": "Response of the AddFulfillmentPlacesRequest. Currently empty because there is no meaningful response populated from the ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2alphaAddFulfillmentPlacesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaAddLocalInventoriesMetadata": {"description": "Metadata related to the progress of the AddLocalInventories operation. Currently empty because there is no meaningful metadata populated from the ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2alphaAddLocalInventoriesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaAddLocalInventoriesRequest": {"description": "Request message for ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2alphaAddLocalInventoriesRequest", "properties": {"addMask": {"description": "Indicates which inventory fields in the provided list of LocalInventory to update. The field is updated to the provided value. If a field is set while the place does not have a previous local inventory, the local inventory at that store is created. If a field is set while the value of that field is not provided, the original field value, if it exists, is deleted. If the mask is not set or set with empty paths, all inventory fields will be updated. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned and the entire update will be ignored.", "format": "google-fieldmask", "type": "string"}, "addTime": {"description": "The time when the inventory updates are issued. Used to prevent out-of-order updates on local inventory fields. If not provided, the internal system time will be used.", "format": "google-datetime", "type": "string"}, "allowMissing": {"description": "If set to true, and the Product is not found, the local inventory will still be processed and retained for at most 1 day and processed once the Product is created. If set to false, a NOT_FOUND error is returned if the Product is not found.", "type": "boolean"}, "localInventories": {"description": "Required. A list of inventory information at difference places. Each place is identified by its place ID. At most 3000 inventories are allowed per request.", "items": {"$ref": "GoogleCloudRetailV2alphaLocalInventory"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaAddLocalInventoriesResponse": {"description": "Response of the ProductService.AddLocalInventories API. Currently empty because there is no meaningful response populated from the ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2alphaAddLocalInventoriesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaAlertConfig": {"description": "Project level alert config.", "id": "GoogleCloudRetailV2alphaAlertConfig", "properties": {"alertPolicies": {"description": "Alert policies for a customer. They must be unique by [AlertPolicy.alert_group]", "items": {"$ref": "GoogleCloudRetailV2alphaAlertConfigAlertPolicy"}, "type": "array"}, "name": {"description": "Required. Immutable. The name of the AlertConfig singleton resource. Format: projects/*/alertConfig", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaAlertConfigAlertPolicy": {"description": "Alert policy for a customer.", "id": "GoogleCloudRetailV2alphaAlertConfigAlertPolicy", "properties": {"alertGroup": {"description": "The feature that provides alerting capability. Supported value: - `search-data-quality` for retail search customers. - `conv-data-quality` for retail conversation customers.", "type": "string"}, "enrollStatus": {"description": "The enrollment status of a customer.", "enum": ["ENROLL_STATUS_UNSPECIFIED", "ENROLLED", "DECLINED"], "enumDescriptions": ["Default value. Used for customers who have not responded to the alert policy.", "Customer is enrolled in this policy.", "Customer declined this policy."], "type": "string"}, "recipients": {"description": "Recipients for the alert policy. One alert policy should not exceed 20 recipients.", "items": {"$ref": "GoogleCloudRetailV2alphaAlertConfigAlertPolicyRecipient"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaAlertConfigAlertPolicyRecipient": {"description": "Recipient contact information.", "id": "GoogleCloudRetailV2alphaAlertConfigAlertPolicyRecipient", "properties": {"emailAddress": {"description": "Email address of the recipient.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaAttributesConfig": {"description": "Catalog level attribute config.", "id": "GoogleCloudRetailV2alphaAttributesConfig", "properties": {"attributeConfigLevel": {"description": "Output only. The AttributeConfigLevel used for this catalog.", "enum": ["ATTRIBUTE_CONFIG_LEVEL_UNSPECIFIED", "PRODUCT_LEVEL_ATTRIBUTE_CONFIG", "CATALOG_LEVEL_ATTRIBUTE_CONFIG"], "enumDescriptions": ["Value used when unset. In this case, server behavior defaults to CATALOG_LEVEL_ATTRIBUTE_CONFIG.", "At this level, we honor the attribute configurations set in Product.attributes.", "At this level, we honor the attribute configurations set in `CatalogConfig.attribute_configs`."], "readOnly": true, "type": "string"}, "catalogAttributes": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaCatalogAttribute"}, "description": "Enable attribute(s) config at catalog level. For example, indexable, dynamic_facetable, or searchable for each attribute. The key is catalog attribute's name. For example: `color`, `brands`, `attributes.custom_attribute`, such as `attributes.xyz`. The maximum number of catalog attributes allowed in a request is 1000.", "type": "object"}, "name": {"description": "Required. Immutable. The fully qualified resource name of the attribute config. Format: `projects/*/locations/*/catalogs/*/attributesConfig`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaAudience": {"description": "An intended audience of the Product for whom it's sold.", "id": "GoogleCloudRetailV2alphaAudience", "properties": {"ageGroups": {"description": "The age groups of the audience. Strongly encouraged to use the standard values: \"newborn\" (up to 3 months old), \"infant\" (3–12 months old), \"toddler\" (1–5 years old), \"kids\" (5–13 years old), \"adult\" (typically teens or older). At most 5 values are allowed. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Google Merchant Center property [age_group](https://support.google.com/merchants/answer/6324463). Schema.org property [Product.audience.suggestedMinAge](https://schema.org/suggestedMinAge) and [Product.audience.suggestedMaxAge](https://schema.org/suggestedMaxAge).", "items": {"type": "string"}, "type": "array"}, "genders": {"description": "The genders of the audience. Strongly encouraged to use the standard values: \"male\", \"female\", \"unisex\". At most 5 values are allowed. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Google Merchant Center property [gender](https://support.google.com/merchants/answer/6324479). Schema.org property [Product.audience.suggestedGender](https://schema.org/suggestedGender).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaBatchRemoveCatalogAttributesRequest": {"description": "Request for CatalogService.BatchRemoveCatalogAttributes method.", "id": "GoogleCloudRetailV2alphaBatchRemoveCatalogAttributesRequest", "properties": {"attributeKeys": {"description": "Required. The attribute name keys of the CatalogAttributes to delete. A maximum of 1000 catalog attributes can be deleted in a batch.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaBatchRemoveCatalogAttributesResponse": {"description": "Response of the CatalogService.BatchRemoveCatalogAttributes.", "id": "GoogleCloudRetailV2alphaBatchRemoveCatalogAttributesResponse", "properties": {"deletedCatalogAttributes": {"description": "Catalog attributes that were deleted. Only pre-loaded catalog attributes that are neither in use by products nor predefined can be deleted.", "items": {"type": "string"}, "type": "array"}, "resetCatalogAttributes": {"description": "Catalog attributes that were reset. Catalog attributes that are either in use by products or are predefined attributes cannot be deleted; however, their configuration properties will reset to default values upon removal request.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaBatchUpdateGenerativeQuestionConfigsRequest": {"description": "Request for BatchUpdateGenerativeQuestionConfig method.", "id": "GoogleCloudRetailV2alphaBatchUpdateGenerativeQuestionConfigsRequest", "properties": {"requests": {"description": "Required. The updates question configs.", "items": {"$ref": "GoogleCloudRetailV2alphaUpdateGenerativeQuestionConfigRequest"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaBatchUpdateGenerativeQuestionConfigsResponse": {"description": "Aggregated response for UpdateGenerativeQuestionConfig method.", "id": "GoogleCloudRetailV2alphaBatchUpdateGenerativeQuestionConfigsResponse", "properties": {"generativeQuestionConfigs": {"description": "Optional. The updates question configs.", "items": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionConfig"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaBigQueryOutputResult": {"description": "A BigQuery output result.", "id": "GoogleCloudRetailV2alphaBigQueryOutputResult", "properties": {"datasetId": {"description": "The ID of a BigQuery Dataset.", "type": "string"}, "tableId": {"description": "The ID of a BigQuery Table.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaBigQuerySource": {"description": "BigQuery source import data from.", "id": "GoogleCloudRetailV2alphaBigQuerySource", "properties": {"dataSchema": {"description": "The schema to use when parsing the data from the source. Supported values for product imports: * `product` (default): One JSON Product per line. Each product must have a valid Product.id. * `product_merchant_center`: See [Importing catalog data from Merchant Center](https://cloud.google.com/retail/recommendations-ai/docs/upload-catalog#mc). Supported values for user events imports: * `user_event` (default): One JSON UserEvent per line. * `user_event_ga360`: The schema is available here: https://support.google.com/analytics/answer/3437719. * `user_event_ga4`: The schema is available here: https://support.google.com/analytics/answer/7029846. Supported values for autocomplete imports: * `suggestions` (default): One JSON completion suggestion per line. * `denylist`: One JSON deny suggestion per line. * `allowlist`: One JSON allow suggestion per line.", "type": "string"}, "datasetId": {"description": "Required. The BigQuery data set to copy the data from with a length limit of 1,024 characters.", "type": "string"}, "gcsStagingDir": {"description": "Intermediate Cloud Storage directory used for the import with a length limit of 2,000 characters. Can be specified if one wants to have the BigQuery export to a specific Cloud Storage directory.", "type": "string"}, "partitionDate": {"$ref": "GoogleTypeDate", "description": "BigQuery time partitioned table's _PARTITIONDATE in YYYY-MM-DD format."}, "projectId": {"description": "The project ID (can be project # or ID) that the BigQuery source is in with a length limit of 128 characters. If not specified, inherits the project ID from the parent request.", "type": "string"}, "tableId": {"description": "Required. The BigQuery table to copy the data from with a length limit of 1,024 characters.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaBranch": {"description": "A data branch that stores all instances of Products.", "id": "GoogleCloudRetailV2alphaBranch", "properties": {"displayName": {"description": "Output only. Human readable name of the branch to display in the UI.", "readOnly": true, "type": "string"}, "isDefault": {"description": "Output only. Indicates whether this branch is set as the default branch of its parent catalog.", "readOnly": true, "type": "boolean"}, "lastProductImportTime": {"description": "Output only. Timestamp of last import through ProductService.ImportProducts. Empty value means no import has been made to this branch.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. Full resource name of the branch, such as `projects/*/locations/global/catalogs/default_catalog/branches/branch_id`.", "type": "string"}, "productCountStats": {"description": "Output only. Statistics for number of products in the branch, provided for different scopes. This field is not populated in BranchView.BASIC view.", "items": {"$ref": "GoogleCloudRetailV2alphaBranchProductCountStatistic"}, "readOnly": true, "type": "array"}, "qualityMetrics": {"description": "Output only. The quality metrics measured among products of this branch. See QualityMetric.requirement_key for supported metrics. Metrics could be missing if failed to retrieve. This field is not populated in BranchView.BASIC view.", "items": {"$ref": "GoogleCloudRetailV2alphaBranchQualityMetric"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaBranchProductCountStatistic": {"description": "A statistic about the number of products in a branch.", "id": "GoogleCloudRetailV2alphaBranchProductCountStatistic", "properties": {"counts": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "The number of products in scope broken down into different groups. The key is a group representing a set of products, and the value is the number of products in that group. Note: keys in this map may change over time. Possible keys: * \"primary-in-stock\", products have Product.Type.PRIMARY type and Product.Availability.IN_STOCK availability. * \"primary-out-of-stock\", products have Product.Type.PRIMARY type and Product.Availability.OUT_OF_STOCK availability. * \"primary-preorder\", products have Product.Type.PRIMARY type and Product.Availability.PREORDER availability. * \"primary-backorder\", products have Product.Type.PRIMARY type and Product.Availability.BACKORDER availability. * \"variant-in-stock\", products have Product.Type.VARIANT type and Product.Availability.IN_STOCK availability. * \"variant-out-of-stock\", products have Product.Type.VARIANT type and Product.Availability.OUT_OF_STOCK availability. * \"variant-preorder\", products have Product.Type.VARIANT type and Product.Availability.PREORDER availability. * \"variant-backorder\", products have Product.Type.VARIANT type and Product.Availability.BACKORDER availability. * \"price-discounted\", products have [Product.price_info.price] < [Product.price_info.original_price].", "type": "object"}, "scope": {"description": "[ProductCountScope] of the [counts].", "enum": ["PRODUCT_COUNT_SCOPE_UNSPECIFIED", "ALL_PRODUCTS", "LAST_24_HOUR_UPDATE"], "enumDescriptions": ["Default value for enum. This value is not used in the API response.", "Scope for all existing products in the branch. Useful for understanding how many products there are in a branch.", "Scope for products created or updated in the last 24 hours."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaBranchQualityMetric": {"description": "Metric measured on a group of Products against a certain quality requirement. Contains the number of products that pass the check and the number of products that don't.", "id": "GoogleCloudRetailV2alphaBranchQualityMetric", "properties": {"qualifiedProductCount": {"description": "Number of products passing the quality requirement check. We only check searchable products.", "format": "int32", "type": "integer"}, "requirementKey": {"description": "The key that represents a quality requirement rule. Supported keys: * \"has-valid-uri\": product has a valid and accessible uri. * \"available-expire-time-conformance\": Product.available_time is early than \"now\", and Product.expire_time is greater than \"now\". * \"has-searchable-attributes\": product has at least one attribute set to searchable. * \"has-description\": product has non-empty description. * \"has-at-least-bigram-title\": Product title has at least two words. A comprehensive title helps to improve search quality. * \"variant-has-image\": the variant products has at least one image. You may ignore this metric if all your products are at primary level. * \"variant-has-price-info\": the variant products has price_info set. You may ignore this metric if all your products are at primary level. * \"has-publish-time\": product has non-empty publish_time.", "type": "string"}, "suggestedQualityPercentThreshold": {"description": "Value from 0 to 100 representing the suggested percentage of products that meet the quality requirements to get good search and recommendation performance. 100 * (qualified_product_count) / (qualified_product_count + unqualified_product_count) should be greater or equal to this suggestion.", "format": "double", "type": "number"}, "unqualifiedProductCount": {"description": "Number of products failing the quality requirement check. We only check searchable products.", "format": "int32", "type": "integer"}, "unqualifiedSampleProducts": {"description": "A list of a maximum of 100 sample products that do not qualify for this requirement. This field is only populated in the response to BranchService.GetBranch API, and is always empty for BranchService.ListBranches. Only the following fields are set in the Product. * Product.name * Product.id * Product.title", "items": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalog": {"description": "The catalog configuration.", "id": "GoogleCloudRetailV2alphaCatalog", "properties": {"displayName": {"description": "Required. Immutable. The catalog display name. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "merchantCenterLinkingConfig": {"$ref": "GoogleCloudRetailV2alphaMerchantCenterLinkingConfig", "description": "The Merchant Center linking configuration. After a link is added, the data stream from Merchant Center to Cloud Retail will be enabled automatically. The requester must have access to the Merchant Center account in order to make changes to this field."}, "name": {"description": "Required. Immutable. The fully qualified resource name of the catalog.", "type": "string"}, "productLevelConfig": {"$ref": "GoogleCloudRetailV2alphaProductLevelConfig", "description": "Required. The product level configuration."}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalogAttribute": {"description": "Catalog level attribute config for an attribute. For example, if customers want to enable/disable facet for a specific attribute.", "id": "GoogleCloudRetailV2alphaCatalogAttribute", "properties": {"dynamicFacetableOption": {"description": "If DYNAMIC_FACETABLE_ENABLED, attribute values are available for dynamic facet. Could only be DYNAMIC_FACETABLE_DISABLED if CatalogAttribute.indexable_option is INDEXABLE_DISABLED. Otherwise, an INVALID_ARGUMENT error is returned. Must be specified, otherwise throws INVALID_FORMAT error.", "enum": ["DYNAMIC_FACETABLE_OPTION_UNSPECIFIED", "DYNAMIC_FACETABLE_ENABLED", "DYNAMIC_FACETABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Dynamic facetable option enabled for an attribute.", "Dynamic facetable option disabled for an attribute."], "type": "string"}, "exactSearchableOption": {"description": "If EXACT_SEARCHABLE_ENABLED, attribute values will be exact searchable. This property only applies to textual custom attributes and requires indexable set to enabled to enable exact-searchable. If unset, the server behavior defaults to EXACT_SEARCHABLE_DISABLED.", "enum": ["EXACT_SEARCHABLE_OPTION_UNSPECIFIED", "EXACT_SEARCHABLE_ENABLED", "EXACT_SEARCHABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Exact searchable option enabled for an attribute.", "Exact searchable option disabled for an attribute."], "type": "string"}, "facetConfig": {"$ref": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfig", "description": "Contains facet options."}, "inUse": {"description": "Output only. Indicates whether this attribute has been used by any products. `True` if at least one Product is using this attribute in Product.attributes. Otherwise, this field is `False`. CatalogAttribute can be pre-loaded by using CatalogService.AddCatalogAttribute or CatalogService.UpdateAttributesConfig APIs. This field is `False` for pre-loaded CatalogAttributes. Only pre-loaded catalog attributes that are neither in use by products nor predefined can be deleted. Catalog attributes that are either in use by products or are predefined attributes cannot be deleted; however, their configuration properties will reset to default values upon removal request. After catalog changes, it takes about 10 minutes for this field to update.", "readOnly": true, "type": "boolean"}, "indexableOption": {"description": "When AttributesConfig.attribute_config_level is CATALOG_LEVEL_ATTRIBUTE_CONFIG, if INDEXABLE_ENABLED attribute values are indexed so that it can be filtered, faceted, or boosted in SearchService.Search. Must be specified when AttributesConfig.attribute_config_level is CATALOG_LEVEL_ATTRIBUTE_CONFIG, otherwise throws INVALID_FORMAT error.", "enum": ["INDEXABLE_OPTION_UNSPECIFIED", "INDEXABLE_ENABLED", "INDEXABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Indexable option enabled for an attribute.", "Indexable option disabled for an attribute."], "type": "string"}, "key": {"description": "Required. Attribute name. For example: `color`, `brands`, `attributes.custom_attribute`, such as `attributes.xyz`. To be indexable, the attribute name can contain only alpha-numeric characters and underscores. For example, an attribute named `attributes.abc_xyz` can be indexed, but an attribute named `attributes.abc-xyz` cannot be indexed. If the attribute key starts with `attributes.`, then the attribute is a custom attribute. Attributes such as `brands`, `patterns`, and `title` are built-in and called system attributes.", "type": "string"}, "recommendationsFilteringOption": {"description": "When AttributesConfig.attribute_config_level is CATALOG_LEVEL_ATTRIBUTE_CONFIG, if RECOMMENDATIONS_FILTERING_ENABLED, attribute values are filterable for recommendations. This option works for categorical features only, does not work for numerical features, inventory filtering.", "enum": ["RECOMMENDATIONS_FILTERING_OPTION_UNSPECIFIED", "RECOMMENDATIONS_FILTERING_DISABLED", "RECOMMENDATIONS_FILTERING_ENABLED"], "enumDescriptions": ["Value used when unset. In this case, server behavior defaults to RECOMMENDATIONS_FILTERING_DISABLED.", "Recommendation filtering is disabled.", "Recommendation filtering is enabled."], "type": "string"}, "retrievableOption": {"description": "If RETRIEVABLE_ENABLED, attribute values are retrievable in the search results. If unset, the server behavior defaults to RETRIEVABLE_DISABLED.", "enum": ["RETRIEVABLE_OPTION_UNSPECIFIED", "RETRIEVABLE_ENABLED", "RETRIEVABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Retrievable option enabled for an attribute.", "Retrievable option disabled for an attribute."], "type": "string"}, "searchableOption": {"description": "When AttributesConfig.attribute_config_level is CATALOG_LEVEL_ATTRIBUTE_CONFIG, if SEAR<PERSON>ABLE_ENABLED, attribute values are searchable by text queries in SearchService.Search. If SEARCH<PERSON>LE_ENABLED but attribute type is numerical, attribute values will not be searchable by text queries in SearchService.Search, as there are no text values associated to numerical attributes. Must be specified, when AttributesConfig.attribute_config_level is CATALOG_LEVEL_ATTRIBUTE_CONFIG, otherwise throws INVALID_FORMAT error.", "enum": ["SEARCHABLE_OPTION_UNSPECIFIED", "SEARCHABLE_ENABLED", "SEARCHABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Searchable option enabled for an attribute.", "Searchable option disabled for an attribute."], "type": "string"}, "type": {"description": "Output only. The type of this attribute. This is derived from the attribute in Product.attributes.", "enum": ["UNKNOWN", "TEXTUAL", "NUMERICAL"], "enumDescriptions": ["The type of the attribute is unknown. Used when type cannot be derived from attribute that is not in_use.", "Textual attribute.", "Numerical attribute."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalogAttributeFacetConfig": {"description": "Possible options for the facet that corresponds to the current attribute config.", "id": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfig", "properties": {"facetIntervals": {"description": "If you don't set the facet SearchRequest.FacetSpec.FacetKey.intervals in the request to a numerical attribute, then we use the computed intervals with rounded bounds obtained from all its product numerical attribute values. The computed intervals might not be ideal for some attributes. Therefore, we give you the option to overwrite them with the facet_intervals field. The maximum of facet intervals per CatalogAttribute is 40. Each interval must have a lower bound or an upper bound. If both bounds are provided, then the lower bound must be smaller or equal than the upper bound.", "items": {"$ref": "GoogleCloudRetailV2alphaInterval"}, "type": "array"}, "ignoredFacetValues": {"description": "Each instance represents a list of attribute values to ignore as facet values for a specific time range. The maximum number of instances per CatalogAttribute is 25.", "items": {"$ref": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigIgnoredFacetValues"}, "type": "array"}, "mergedFacet": {"$ref": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigMergedFacet", "description": "Use this field only if you want to merge a facet key into another facet key."}, "mergedFacetValues": {"description": "Each instance replaces a list of facet values by a merged facet value. If a facet value is not in any list, then it will stay the same. To avoid conflicts, only paths of length 1 are accepted. In other words, if \"dark_blue\" merged into \"BLUE\", then the latter can't merge into \"blues\" because this would create a path of length 2. The maximum number of instances of MergedFacetValue per CatalogAttribute is 100. This feature is available only for textual custom attributes.", "items": {"$ref": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigMergedFacetValue"}, "type": "array"}, "rerankConfig": {"$ref": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigRerankConfig", "description": "Set this field only if you want to rerank based on facet values engaged by the user for the current key. This option is only possible for custom facetable textual keys."}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigIgnoredFacetValues": {"description": "Facet values to ignore on facets during the specified time range for the given SearchResponse.Facet.key attribute.", "id": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigIgnoredFacetValues", "properties": {"endTime": {"description": "If start time is empty and end time is not empty, then ignore these facet values before end time.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Time range for the current list of facet values to ignore. If multiple time ranges are specified for an facet value for the current attribute, consider all of them. If both are empty, ignore always. If start time and end time are set, then start time must be before end time. If start time is not empty and end time is empty, then will ignore these facet values after the start time.", "format": "google-datetime", "type": "string"}, "values": {"description": "List of facet values to ignore for the following time range. The facet values are the same as the attribute values. There is a limit of 10 values per instance of IgnoredFacetValues. Each value can have at most 128 characters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigMergedFacet": {"description": "The current facet key (i.e. attribute config) maps into the merged_facet_key. A facet key can have at most one child. The current facet key and the merged facet key need both to be textual custom attributes or both numerical custom attributes (same type).", "id": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigMergedFacet", "properties": {"mergedFacetKey": {"description": "The merged facet key should be a valid facet key that is different than the facet key of the current catalog attribute. We refer this is merged facet key as the child of the current catalog attribute. This merged facet key can't be a parent of another facet key (i.e. no directed path of length 2). This merged facet key needs to be either a textual custom attribute or a numerical custom attribute.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigMergedFacetValue": {"description": "Replaces a set of textual facet values by the same (possibly different) merged facet value. Each facet value should appear at most once as a value per CatalogAttribute. This feature is available only for textual custom attributes.", "id": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigMergedFacetValue", "properties": {"mergedValue": {"description": "All the previous values are replaced by this merged facet value. This merged_value must be non-empty and can have up to 128 characters.", "type": "string"}, "values": {"description": "All the facet values that are replaces by the same merged_value that follows. The maximum number of values per MergedFacetValue is 25. Each value can have up to 128 characters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigRerankConfig": {"description": "Options to rerank based on facet values engaged by the user for the current key. That key needs to be a custom textual key and facetable. To use this control, you also need to pass all the facet keys engaged by the user in the request using the field [SearchRequest.FacetSpec]. In particular, if you don't pass the facet keys engaged that you want to rerank on, this control won't be effective. Moreover, to obtain better results, the facet values that you want to rerank on should be close to English (ideally made of words, underscores, and spaces).", "id": "GoogleCloudRetailV2alphaCatalogAttributeFacetConfigRerankConfig", "properties": {"facetValues": {"description": "If empty, rerank on all facet values for the current key. Otherwise, will rerank on the facet values from this list only.", "items": {"type": "string"}, "type": "array"}, "rerankFacet": {"description": "If set to true, then we also rerank the dynamic facets based on the facet values engaged by the user for the current attribute key during serving.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaCollectUserEventRequest": {"description": "Request message for CollectUserEvent method.", "id": "GoogleCloudRetailV2alphaCollectUserEventRequest", "properties": {"ets": {"description": "The event timestamp in milliseconds. This prevents browser caching of otherwise identical get requests. The name is abbreviated to reduce the payload bytes.", "format": "int64", "type": "string"}, "prebuiltRule": {"description": "The prebuilt rule name that can convert a specific type of raw_json. For example: \"ga4_bq\" rule for the GA4 user event schema.", "type": "string"}, "rawJson": {"description": "An arbitrary serialized JSON string that contains necessary information that can comprise a user event. When this field is specified, the user_event field will be ignored. Note: line-delimited JSON is not supported, a single JSON only.", "type": "string"}, "uri": {"description": "The URL including cgi-parameters but excluding the hash fragment with a length limit of 5,000 characters. This is often more useful than the referer URL, because many browsers only send the domain for 3rd party requests.", "type": "string"}, "userEvent": {"description": "Required. URL encoded UserEvent proto with a length limit of 2,000,000 characters.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaColorInfo": {"description": "The color information of a Product.", "id": "GoogleCloudRetailV2alphaColorInfo", "properties": {"colorFamilies": {"description": "The standard color families. Strongly recommended to use the following standard color groups: \"<PERSON>\", \"Pink\", \"Orange\", \"Yellow\", \"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>\", \"White\", \"Gray\", \"Black\" and \"Mixed\". Normally it is expected to have only 1 color family. May consider using single \"Mixed\" instead of multiple values. A maximum of 5 values are allowed. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Google Merchant Center property [color](https://support.google.com/merchants/answer/6324487). Schema.org property [Product.color](https://schema.org/color). The colorFamilies field as a system attribute is not a required field but strongly recommended to be specified. Google Search models treat this field as more important than a custom product attribute when specified.", "items": {"type": "string"}, "type": "array"}, "colors": {"description": "The color display names, which may be different from standard color family names, such as the color aliases used in the website frontend. Normally it is expected to have only 1 color. May consider using single \"Mixed\" instead of multiple values. A maximum of 75 colors are allowed. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Google Merchant Center property [color](https://support.google.com/merchants/answer/6324487). Schema.org property [Product.color](https://schema.org/color).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaCompleteQueryResponse": {"description": "Response of the autocomplete query.", "id": "GoogleCloudRetailV2alphaCompleteQueryResponse", "properties": {"attributeResults": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaCompleteQueryResponseAttributeResult"}, "description": "A map of matched attribute suggestions. This field is only available for `cloud-retail` dataset. Current supported keys: * `brands` * `categories`", "type": "object"}, "attributionToken": {"description": "A unique complete token. This should be included in the UserEvent.completion_detail for search events resulting from this completion, which enables accurate attribution of complete model performance.", "type": "string"}, "completionResults": {"description": "Results of the matching suggestions. The result list is ordered and the first result is top suggestion.", "items": {"$ref": "GoogleCloudRetailV2alphaCompleteQueryResponseCompletionResult"}, "type": "array"}, "recentSearchResults": {"deprecated": true, "description": "Deprecated. Matched recent searches of this user. The maximum number of recent searches is 10. This field is a restricted feature. If you want to enable it, contact Retail Search support. This feature is only available when CompleteQueryRequest.visitor_id field is set and UserEvent is imported. The recent searches satisfy the follow rules: * They are ordered from latest to oldest. * They are matched with CompleteQueryRequest.query case insensitively. * They are transformed to lower case. * They are UTF-8 safe. Recent searches are deduplicated. More recent searches will be reserved when duplication happens.", "items": {"$ref": "GoogleCloudRetailV2alphaCompleteQueryResponseRecentSearchResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaCompleteQueryResponseAttributeResult": {"description": "Resource that represents attribute results.", "id": "GoogleCloudRetailV2alphaCompleteQueryResponseAttributeResult", "properties": {"suggestions": {"description": "The list of suggestions for the attribute.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaCompleteQueryResponseCompletionResult": {"description": "Resource that represents completion results.", "id": "GoogleCloudRetailV2alphaCompleteQueryResponseCompletionResult", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaCustomAttribute"}, "description": "Custom attributes for the suggestion term. * For `user-data`, the attributes are additional custom attributes ingested through BigQuery. * For `cloud-retail`, the attributes are product attributes generated by Cloud Retail. It requires UserEvent.product_details is imported properly.", "type": "object"}, "facets": {"description": "Facet information for the suggestion term. Gives the number of items resulting from a search with this suggestion term for each facet. This is an experimental feature for limited customers. If you want to receive this facet information, reach out to the Retail support team.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchResponseFacet"}, "type": "array"}, "suggestion": {"description": "The suggestion for the query.", "type": "string"}, "totalProductCount": {"description": "Total number of products associated with a search with this suggestion. This is an experimental feature for limited customers. If you want to receive this product count information, reach out to the Retail support team.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaCompleteQueryResponseRecentSearchResult": {"deprecated": true, "description": "Deprecated: Recent search of this user.", "id": "GoogleCloudRetailV2alphaCompleteQueryResponseRecentSearchResult", "properties": {"recentSearch": {"description": "The recent search query.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCompletionConfig": {"description": "Catalog level autocomplete config for customers to customize autocomplete feature's settings.", "id": "GoogleCloudRetailV2alphaCompletionConfig", "properties": {"allowlistInputConfig": {"$ref": "GoogleCloudRetailV2alphaCompletionDataInputConfig", "description": "Output only. The source data for the latest import of the autocomplete allowlist phrases.", "readOnly": true}, "autoLearning": {"description": "If set to true, the auto learning function is enabled. Auto learning uses user data to generate suggestions using ML techniques. Default value is false. Only after enabling auto learning can users use `cloud-retail` data in CompleteQueryRequest.", "type": "boolean"}, "denylistInputConfig": {"$ref": "GoogleCloudRetailV2alphaCompletionDataInputConfig", "description": "Output only. The source data for the latest import of the autocomplete denylist phrases.", "readOnly": true}, "lastAllowlistImportOperation": {"description": "Output only. Name of the LRO corresponding to the latest allowlist import. Can use GetOperation API to retrieve the latest state of the Long Running Operation.", "readOnly": true, "type": "string"}, "lastDenylistImportOperation": {"description": "Output only. Name of the LRO corresponding to the latest denylist import. Can use GetOperation API to retrieve the latest state of the Long Running Operation.", "readOnly": true, "type": "string"}, "lastSuggestionsImportOperation": {"description": "Output only. Name of the LRO corresponding to the latest suggestion terms list import. Can use GetOperation API method to retrieve the latest state of the Long Running Operation.", "readOnly": true, "type": "string"}, "matchingOrder": {"description": "Specifies the matching order for autocomplete suggestions, e.g., a query consisting of 'sh' with 'out-of-order' specified would suggest \"women's shoes\", whereas a query of 'red s' with 'exact-prefix' specified would suggest \"red shoes\". Currently supported values: * 'out-of-order' * 'exact-prefix' Default value: 'exact-prefix'.", "type": "string"}, "maxSuggestions": {"description": "The maximum number of autocomplete suggestions returned per term. Default value is 20. If left unset or set to 0, then will fallback to default value. Value range is 1 to 20.", "format": "int32", "type": "integer"}, "minPrefixLength": {"description": "The minimum number of characters needed to be typed in order to get suggestions. Default value is 2. If left unset or set to 0, then will fallback to default value. Value range is 1 to 20.", "format": "int32", "type": "integer"}, "name": {"description": "Required. Immutable. Fully qualified name `projects/*/locations/*/catalogs/*/completionConfig`", "type": "string"}, "suggestionsInputConfig": {"$ref": "GoogleCloudRetailV2alphaCompletionDataInputConfig", "description": "Output only. The source data for the latest import of the autocomplete suggestion phrases.", "readOnly": true}}, "type": "object"}, "GoogleCloudRetailV2alphaCompletionDataInputConfig": {"description": "The input config source for completion data.", "id": "GoogleCloudRetailV2alphaCompletionDataInputConfig", "properties": {"bigQuerySource": {"$ref": "GoogleCloudRetailV2alphaBigQuerySource", "description": "Required. BigQuery input source. Add the IAM permission \"BigQuery Data Viewer\" for <EMAIL> before using this feature otherwise an error is thrown."}}, "type": "object"}, "GoogleCloudRetailV2alphaCompletionDetail": {"description": "Detailed completion information including completion attribution token and clicked completion info.", "id": "GoogleCloudRetailV2alphaCompletionDetail", "properties": {"completionAttributionToken": {"description": "Completion attribution token in CompleteQueryResponse.attribution_token.", "type": "string"}, "selectedPosition": {"description": "End user selected CompleteQueryResponse.CompletionResult.suggestion position, starting from 0.", "format": "int32", "type": "integer"}, "selectedSuggestion": {"description": "End user selected CompleteQueryResponse.CompletionResult.suggestion.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCondition": {"description": "Metadata that is used to define a condition that triggers an action. A valid condition must specify at least one of 'query_terms' or 'products_filter'. If multiple fields are specified, the condition is met if all the fields are satisfied e.g. if a set of query terms and product_filter are set, then only items matching the product_filter for requests with a query matching the query terms wil get boosted.", "id": "GoogleCloudRetailV2alphaCondition", "properties": {"activeTimeRange": {"description": "Range of time(s) specifying when Condition is active. Condition true if any time range matches.", "items": {"$ref": "GoogleCloudRetailV2alphaConditionTimeRange"}, "type": "array"}, "pageCategories": {"description": "Used to support browse uses cases. A list (up to 10 entries) of categories or departments. The format should be the same as UserEvent.page_categories;", "items": {"type": "string"}, "type": "array"}, "queryTerms": {"description": "A list (up to 10 entries) of terms to match the query on. If not specified, match all queries. If many query terms are specified, the condition is matched if any of the terms is a match (i.e. using the OR operator).", "items": {"$ref": "GoogleCloudRetailV2alphaConditionQueryTerm"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaConditionQueryTerm": {"description": "Query terms that we want to match on.", "id": "GoogleCloudRetailV2alphaConditionQueryTerm", "properties": {"fullMatch": {"description": "Whether this is supposed to be a full or partial match.", "type": "boolean"}, "value": {"description": "The value of the term to match on. Value cannot be empty. Value can have at most 3 terms if specified as a partial match. Each space separated string is considered as one term. For example, \"a b c\" is 3 terms and allowed, but \" a b c d\" is 4 terms and not allowed for a partial match.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaConditionTimeRange": {"description": "Used for time-dependent conditions. Example: Want to have rule applied for week long sale.", "id": "GoogleCloudRetailV2alphaConditionTimeRange", "properties": {"endTime": {"description": "End of time range. Range is inclusive.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Start of time range. Range is inclusive.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaControl": {"description": "Configures dynamic metadata that can be linked to a ServingConfig and affect search or recommendation results at serving time.", "id": "GoogleCloudRetailV2alphaControl", "properties": {"associatedServingConfigIds": {"description": "Output only. List of serving config ids that are associated with this control in the same Catalog. Note the association is managed via the ServingConfig, this is an output only denormalized view.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "displayName": {"description": "Required. The human readable control display name. Used in Retail UI. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is thrown.", "type": "string"}, "facetSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestFacetSpec", "deprecated": true, "description": "A facet specification to perform faceted search. Note that this field is deprecated and will throw NOT_IMPLEMENTED if used for creating a control."}, "name": {"description": "Immutable. Fully qualified name `projects/*/locations/global/catalogs/*/controls/*`", "type": "string"}, "rule": {"$ref": "GoogleCloudRetailV2alphaRule", "description": "A rule control - a condition-action pair. Enacts a set action when the condition is triggered. For example: Boost \"gShoe\" when query full matches \"Running Shoes\"."}, "searchSolutionUseCase": {"description": "Specifies the use case for the control. Affects what condition fields can be set. Only settable by search controls. Will default to SEARCH_SOLUTION_USE_CASE_SEARCH if not specified. Currently only allow one search_solution_use_case per control.", "items": {"enum": ["SEARCH_SOLUTION_USE_CASE_UNSPECIFIED", "SEARCH_SOLUTION_USE_CASE_SEARCH", "SEARCH_SOLUTION_USE_CASE_BROWSE"], "enumDescriptions": ["The value when it's unspecified. In this case, server behavior defaults to SEARCH_SOLUTION_USE_CASE_SEARCH.", "Search use case. Expects the traffic has a non-empty query.", "Browse use case. Expects the traffic has an empty query."], "type": "string"}, "type": "array"}, "solutionTypes": {"description": "Required. Immutable. The solution types that the control is used for. Currently we support setting only one type of solution at creation time. Only `SOLUTION_TYPE_SEARCH` value is supported at the moment. If no solution type is provided at creation time, will default to SOLUTION_TYPE_SEARCH.", "items": {"enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Retail Search."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchCustomizationConfig": {"description": "The public proto to represent the conversational search customization config. It will be converted to the internal proto in the backend.", "id": "GoogleCloudRetailV2alphaConversationalSearchCustomizationConfig", "properties": {"catalog": {"description": "Required. Resource name of the catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "type": "string"}, "intentClassificationConfig": {"$ref": "GoogleCloudRetailV2alphaIntentClassificationConfig", "description": "Optional. The configs for intent classification."}, "retailerDisplayName": {"description": "Optional. The retailer's display name that could be used in our LLM answers. Example - \"Google\"", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchRequest": {"description": "Request message for ConversationalSearchService.ConversationalSearch method.", "id": "GoogleCloudRetailV2alphaConversationalSearchRequest", "properties": {"branch": {"description": "Required. The branch resource name, such as `projects/*/locations/global/catalogs/default_catalog/branches/0`. Use \"default_branch\" as the branch ID or leave this field empty, to search products under the default branch.", "type": "string"}, "conversationId": {"description": "Optional. This field specifies the conversation id, which maintains the state of the conversation between client side and server side. Use the value from the previous ConversationalSearchResponse.conversation_id. For the initial request, this should be empty.", "type": "string"}, "conversationalFilteringSpec": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchRequestConversationalFilteringSpec", "description": "Optional. This field specifies all conversational filtering related parameters."}, "pageCategories": {"description": "Optional. The categories associated with a category page. Must be set for category navigation queries to achieve good search quality. The format should be the same as UserEvent.page_categories; To represent full path of category, use '>' sign to separate different hierarchies. If '>' is part of the category name, replace it with other character(s). Category pages include special pages such as sales or promotions. For instance, a special sale page may have the category hierarchy: \"pageCategories\" : [\"Sales > 2017 Black Friday Deals\"].", "items": {"type": "string"}, "type": "array"}, "query": {"description": "Optional. Raw search query to be searched for. If this field is empty, the request is considered a category browsing request.", "type": "string"}, "searchParams": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchRequestSearchParams", "description": "Optional. Search parameters."}, "userInfo": {"$ref": "GoogleCloudRetailV2alphaUserInfo", "description": "Optional. User information."}, "userLabels": {"additionalProperties": {"type": "string"}, "description": "Optional. The user labels applied to a resource must meet the following requirements: * Each resource can have multiple labels, up to a maximum of 64. * Each label must be a key-value pair. * Keys have a minimum length of 1 character and a maximum length of 63 characters and cannot be empty. Values can be empty and have a maximum length of 63 characters. * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. All characters must use UTF-8 encoding, and international characters are allowed. * The key portion of a label must be unique. However, you can use the same key with multiple resources. * Keys must start with a lowercase letter or international character. See [Google Cloud Document](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements) for more details.", "type": "object"}, "visitorId": {"description": "Required. A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. This should be the same identifier as UserEvent.visitor_id. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchRequestConversationalFilteringSpec": {"description": "This field specifies all conversational filtering related parameters addition to conversational retail search.", "id": "GoogleCloudRetailV2alphaConversationalSearchRequestConversationalFilteringSpec", "properties": {"conversationalFilteringMode": {"description": "Optional. Mode to control Conversational Filtering. Defaults to Mode.DISABLED if it's unset.", "enum": ["MODE_UNSPECIFIED", "CONVERSATIONAL_FILTER_ONLY"], "enumDescriptions": ["Default value.", "Enabled Conversational Filtering without default Conversational Search."], "type": "string"}, "enableConversationalFiltering": {"deprecated": true, "description": "Optional. This field is deprecated. Please use ConversationalFilteringSpec.conversational_filtering_mode instead.", "type": "boolean"}, "userAnswer": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchRequestUserAnswer", "description": "Optional. This field specifies the current user answer during the conversational filtering search. It can be either user selected from suggested answers or user input plain text."}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchRequestSearchParams": {"description": "Search parameters.", "id": "GoogleCloudRetailV2alphaConversationalSearchRequestSearchParams", "properties": {"boostSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestBoostSpec", "description": "Optional. The boost spec to specify the boosting of search results. The syntax of the boost spec is the same as SearchRequest.boost_spec."}, "canonicalFilter": {"description": "Optional. The canonical filter string to restrict search results. The syntax of the canonical filter string is the same as SearchRequest.canonical_filter.", "type": "string"}, "filter": {"description": "Optional. The filter string to restrict search results. The syntax of the filter string is the same as SearchRequest.filter.", "type": "string"}, "sortBy": {"description": "Optional. The sort string to specify the sorting of search results. The syntax of the sort string is the same as SearchRequest.sort.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchRequestUserAnswer": {"description": "This field specifies the current user answer during the conversational filtering search. This can be either user selected from suggested answers or user input plain text.", "id": "GoogleCloudRetailV2alphaConversationalSearchRequestUserAnswer", "properties": {"selectedAnswer": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchRequestUserAnswerSelectedAnswer", "description": "Optional. This field specifies the selected answer during the conversational search. This should be a subset of ConversationalSearchResponse.followup_question.suggested_answers."}, "textAnswer": {"description": "This field specifies the incremental input text from the user during the conversational search.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchRequestUserAnswerSelectedAnswer": {"description": "This field specifies the selected answers during the conversational search.", "id": "GoogleCloudRetailV2alphaConversationalSearchRequestUserAnswerSelectedAnswer", "properties": {"productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "Optional. This field specifies the selected answer which is a attribute key-value."}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchResponse": {"description": "Response message for ConversationalSearchService.ConversationalSearch method.", "id": "GoogleCloudRetailV2alphaConversationalSearchResponse", "properties": {"conversationId": {"description": "Conversation UUID. This field will be stored in client side storage to maintain the conversation session with server and will be used for next search request's ConversationalSearchRequest.conversation_id to restore conversation state in server.", "type": "string"}, "conversationalFilteringResult": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponseConversationalFilteringResult", "description": "This field specifies all related information that is needed on client side for UI rendering of conversational filtering search."}, "refinedSearch": {"description": "The proposed refined search queries. They can be used to fetch the relevant search results. When using CONVERSATIONAL_FILTER_ONLY mode, the refined_query from search response will be populated here.", "items": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponseRefinedSearch"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchResponseConversationalFilteringResult": {"description": "This field specifies all related information that is needed on client side for UI rendering of conversational filtering search.", "id": "GoogleCloudRetailV2alphaConversationalSearchResponseConversationalFilteringResult", "properties": {"additionalFilter": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponseConversationalFilteringResultAdditionalFilter", "description": "This is the incremental additional filters implied from the current user answer. User should add the suggested addition filters to the previous ConversationalSearchRequest.search_params.filter and SearchRequest.filter, and use the merged filter in the follow up requests."}, "followupQuestion": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponseFollowupQuestion", "description": "The conversational filtering question."}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchResponseConversationalFilteringResultAdditionalFilter": {"description": "Additional filter that client side need to apply.", "id": "GoogleCloudRetailV2alphaConversationalSearchResponseConversationalFilteringResultAdditionalFilter", "properties": {"productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "Product attribute value, including an attribute key and an attribute value. Other types can be added here in the future."}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchResponseFollowupQuestion": {"description": "The conversational followup question generated for Intent refinement.", "id": "GoogleCloudRetailV2alphaConversationalSearchResponseFollowupQuestion", "properties": {"followupQuestion": {"description": "The conversational followup question generated for Intent refinement.", "type": "string"}, "suggestedAnswers": {"description": "The answer options provided to client for the follow-up question.", "items": {"$ref": "GoogleCloudRetailV2alphaConversationalSearchResponseFollowupQuestionSuggestedAnswer"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchResponseFollowupQuestionSuggestedAnswer": {"description": "Suggested answers to the follow-up question. If it's numerical attribute, only ProductAttributeInterval will be set. If it's textual attribute, only productAttributeValue will be set.", "id": "GoogleCloudRetailV2alphaConversationalSearchResponseFollowupQuestionSuggestedAnswer", "properties": {"productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "Product attribute value, including an attribute key and an attribute value. Other types can be added here in the future."}}, "type": "object"}, "GoogleCloudRetailV2alphaConversationalSearchResponseRefinedSearch": {"description": "The proposed refined search for intent-refinement/bundled shopping conversation. When using CONVERSATIONAL_FILTER_ONLY mode, the refined_query from search response will be populated here.", "id": "GoogleCloudRetailV2alphaConversationalSearchResponseRefinedSearch", "properties": {"query": {"description": "The query to be used for search.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCreateMerchantCenterAccountLinkMetadata": {"description": "Common metadata related to the progress of the operations.", "id": "GoogleCloudRetailV2alphaCreateMerchantCenterAccountLinkMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCreateModelMetadata": {"description": "Metadata associated with a create operation.", "id": "GoogleCloudRetailV2alphaCreateModelMetadata", "properties": {"model": {"description": "The resource name of the model that this create applies to. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaCustomAttribute": {"description": "A custom attribute that is not explicitly modeled in Product.", "id": "GoogleCloudRetailV2alphaCustomAttribute", "properties": {"indexable": {"deprecated": true, "description": "This field is normally ignored unless AttributesConfig.attribute_config_level of the Catalog is set to the deprecated 'PRODUCT_LEVEL_ATTRIBUTE_CONFIG' mode. For information about product-level attribute configuration, see [Configuration modes](https://cloud.google.com/retail/docs/attribute-config#config-modes). If true, custom attribute values are indexed, so that they can be filtered, faceted or boosted in SearchService.Search. This field is ignored in a UserEvent. See SearchRequest.filter, SearchRequest.facet_specs and SearchRequest.boost_spec for more details.", "type": "boolean"}, "numbers": {"description": "The numerical values of this custom attribute. For example, `[2.3, 15.4]` when the key is \"lengths_cm\". Exactly one of text or numbers should be set. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"format": "double", "type": "number"}, "type": "array"}, "searchable": {"deprecated": true, "description": "This field is normally ignored unless AttributesConfig.attribute_config_level of the Catalog is set to the deprecated 'PRODUCT_LEVEL_ATTRIBUTE_CONFIG' mode. For information about product-level attribute configuration, see [Configuration modes](https://cloud.google.com/retail/docs/attribute-config#config-modes). If true, custom attribute values are searchable by text queries in SearchService.Search. This field is ignored in a UserEvent. Only set if type text is set. Otherwise, a INVALID_ARGUMENT error is returned.", "type": "boolean"}, "text": {"description": "The textual values of this custom attribute. For example, `[\"yellow\", \"green\"]` when the key is \"color\". Empty string is not allowed. Otherwise, an INVALID_ARGUMENT error is returned. Exactly one of text or numbers should be set. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaDoubleList": {"description": "A message with a list of double values.", "id": "GoogleCloudRetailV2alphaDoubleList", "properties": {"values": {"description": "The list of double values.", "items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaEnrollSolutionMetadata": {"description": "Metadata related to the EnrollSolution method. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2alphaEnrollSolutionMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaEnrollSolutionRequest": {"description": "Request for EnrollSolution method.", "id": "GoogleCloudRetailV2alphaEnrollSolutionRequest", "properties": {"solution": {"description": "Required. Solution to enroll.", "enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Retail Search."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaEnrollSolutionResponse": {"description": "Response for EnrollSolution method.", "id": "GoogleCloudRetailV2alphaEnrollSolutionResponse", "properties": {"enrolledSolution": {"description": "Retail API solution that the project has enrolled.", "enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Retail Search."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaExperimentInfo": {"description": "Metadata for active A/B testing experiment.", "id": "GoogleCloudRetailV2alphaExperimentInfo", "properties": {"experiment": {"description": "The fully qualified resource name of the experiment that provides the serving config under test, should an active experiment exist. For example: `projects/*/locations/global/catalogs/default_catalog/experiments/experiment_id`", "type": "string"}, "servingConfigExperiment": {"$ref": "GoogleCloudRetailV2alphaExperimentInfoServingConfigExperiment", "description": "A/B test between existing Cloud Retail Search ServingConfigs."}}, "type": "object"}, "GoogleCloudRetailV2alphaExperimentInfoServingConfigExperiment": {"description": "Metadata for active serving config A/B tests.", "id": "GoogleCloudRetailV2alphaExperimentInfoServingConfigExperiment", "properties": {"experimentServingConfig": {"description": "The fully qualified resource name of the serving config `Experiment.VariantArm.serving_config_id` responsible for generating the search response. For example: `projects/*/locations/*/catalogs/*/servingConfigs/*`.", "type": "string"}, "originalServingConfig": {"description": "The fully qualified resource name of the original SearchRequest.placement in the search request prior to reassignment by experiment API. For example: `projects/*/locations/*/catalogs/*/servingConfigs/*`.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaExportAnalyticsMetricsRequest": {"description": "Request message for the `ExportAnalyticsMetrics` method.", "id": "GoogleCloudRetailV2alphaExportAnalyticsMetricsRequest", "properties": {"filter": {"description": "A filtering expression to specify restrictions on returned metrics. The expression is a sequence of terms. Each term applies a restriction to the returned metrics. Use this expression to restrict results to a specific time range. Currently we expect only one types of fields: * `timestamp`: This can be specified twice, once with a less than operator and once with a greater than operator. The `timestamp` restriction should result in one, contiguous, valid, `timestamp` range. Some examples of valid filters expressions: * Example 1: `timestamp > \"2012-04-23T18:25:43.511Z\" timestamp < \"2012-04-23T18:30:43.511Z\"` * Example 2: `timestamp > \"2012-04-23T18:25:43.511Z\"`", "type": "string"}, "outputConfig": {"$ref": "GoogleCloudRetailV2alphaOutputConfig", "description": "Required. The output location of the data."}}, "type": "object"}, "GoogleCloudRetailV2alphaExportAnalyticsMetricsResponse": {"description": "Response of the ExportAnalyticsMetricsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2alphaExportAnalyticsMetricsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2alphaExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2alphaOutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2alphaExportErrorsConfig": {"description": "Configuration of destination for Export related errors.", "id": "GoogleCloudRetailV2alphaExportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage path for import errors. This must be an empty, existing Cloud Storage bucket. Export errors will be written to a file in this bucket, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaExportMetadata": {"description": "Metadata related to the progress of the Export operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2alphaExportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaExportProductsRequest": {"description": "Request message for ExportProducts method.", "id": "GoogleCloudRetailV2alphaExportProductsRequest", "properties": {"filter": {"description": "A filtering expression to specify restrictions on returned events. The expression is a sequence of terms. Each term applies a restriction to the returned products. Use this expression to restrict results to a specific time range, tag, or stock state or to filter products by product type. For example, `lastModifiedTime > \"2012-04-23T18:25:43.511Z\" lastModifiedTime<\"2012-04-23T18:25:43.511Z\" productType=primary` We expect only four types of fields: * `lastModifiedTime`: This can be specified twice, once with a less than operator and once with a greater than operator. The `lastModifiedTime` restriction should result in one, contiguous, valid, last-modified, time range. * `productType`: Supported values are `primary` and `variant`. The Boolean operators `OR` and `NOT` are supported if the expression is enclosed in parentheses and must be separated from the `productType` values by a space. * `availability`: Supported values are `IN_STOCK`, `OUT_OF_STOCK`, `PREORDER`, and `BACKORDER`. Boolean operators `OR` and `NOT` are supported if the expression is enclosed in parentheses and must be separated from the `availability` values by a space. * `Tag expressions`: Restricts output to products that match all of the specified tags. Boolean operators `OR` and `NOT` are supported if the expression is enclosed in parentheses and the operators are separated from the tag values by a space. Also supported is '`-\"tagA\"`', which is equivalent to '`NOT \"tagA\"`'. Tag values must be double-quoted, UTF-8 encoded strings and have a size limit of 1,000 characters. Some examples of valid filters expressions: * Example 1: `lastModifiedTime > \"2012-04-23T18:25:43.511Z\" lastModifiedTime < \"2012-04-23T18:30:43.511Z\"` * Example 2: `lastModifiedTime > \"2012-04-23T18:25:43.511Z\" productType = \"variant\"` * Example 3: `tag=(\"Red\" OR \"Blue\") tag=\"New-Arrival\" tag=(NOT \"promotional\") productType = \"primary\" lastModifiedTime < \"2018-04-23T18:30:43.511Z\"` * Example 4: `lastModifiedTime > \"2012-04-23T18:25:43.511Z\"` * Example 5: `availability = (IN_STOCK OR BACKORDER)`", "type": "string"}, "outputConfig": {"$ref": "GoogleCloudRetailV2alphaOutputConfig", "description": "Required. The output location of the data."}}, "type": "object"}, "GoogleCloudRetailV2alphaExportProductsResponse": {"description": "Response of the ExportProductsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2alphaExportProductsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2alphaExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2alphaOutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2alphaExportUserEventsRequest": {"description": "Request message for the `ExportUserEvents` method.", "id": "GoogleCloudRetailV2alphaExportUserEventsRequest", "properties": {"filter": {"description": "A filtering expression to specify restrictions on returned events. The expression is a sequence of terms. Each term applies a restriction to the returned user events. Use this expression to restrict results to a specific time range or to filter events by eventType. For example, `eventTime > \"2012-04-23T18:25:43.511Z\" eventsMissingCatalogItems eventTime<\"2012-04-23T18:25:43.511Z\" eventType=search` We expect only three types of fields: * `eventTime`: This can be specified twice, once with a less than operator and once with a greater than operator. The `eventTime` restriction should result in one, contiguous, valid, `eventTime` range. * `eventType`: Boolean operators `OR` and `NOT` are supported if the expression is enclosed in parentheses and the operators are separated from the tag values by a space. * `eventsMissingCatalogItems`: This restricts results to events for which catalog items were not found in the catalog. The default behavior is to return only those events for which catalog items were found. Some examples of valid filters expressions: * Example 1: `eventTime > \"2012-04-23T18:25:43.511Z\" eventTime < \"2012-04-23T18:30:43.511Z\"` * Example 2: `eventTime > \"2012-04-23T18:25:43.511Z\" eventType = detail-page-view` * Example 3: `eventsMissingCatalogItems eventType = (NOT search) eventTime < \"2018-04-23T18:30:43.511Z\"` * Example 4: `eventTime > \"2012-04-23T18:25:43.511Z\"` * Example 5: `eventType = (detail-page-view OR search)` * Example 6: `eventsMissingCatalogItems`", "type": "string"}, "outputConfig": {"$ref": "GoogleCloudRetailV2alphaOutputConfig", "description": "Required. The output location of the data."}}, "type": "object"}, "GoogleCloudRetailV2alphaExportUserEventsResponse": {"description": "Response of the ExportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2alphaExportUserEventsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2alphaExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2alphaOutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2alphaFulfillmentInfo": {"description": "Fulfillment information, such as the store IDs for in-store pickup or region IDs for different shipping methods.", "id": "GoogleCloudRetailV2alphaFulfillmentInfo", "properties": {"placeIds": {"description": "The IDs for this type, such as the store IDs for FulfillmentInfo.type.pickup-in-store or the region IDs for FulfillmentInfo.type.same-day-delivery. A maximum of 3000 values are allowed. Each value must be a string with a length limit of 30 characters, matching the pattern `[a-zA-Z0-9_-]+`, such as \"store1\" or \"REGION-2\". Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}, "type": {"description": "The fulfillment type, including commonly used types (such as pickup in store and same day delivery), and custom types. Customers have to map custom types to their display names before rendering UI. Supported values: * \"pickup-in-store\" * \"ship-to-store\" * \"same-day-delivery\" * \"next-day-delivery\" * \"custom-type-1\" * \"custom-type-2\" * \"custom-type-3\" * \"custom-type-4\" * \"custom-type-5\" If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaGcsOutputResult": {"description": "A Gcs output result.", "id": "GoogleCloudRetailV2alphaGcsOutputResult", "properties": {"outputUri": {"description": "The uri of Gcs output", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaGcsSource": {"description": "Google Cloud Storage location for input content.", "id": "GoogleCloudRetailV2alphaGcsSource", "properties": {"dataSchema": {"description": "The schema to use when parsing the data from the source. Supported values for product imports: * `product` (default): One JSON Product per line. Each product must have a valid Product.id. * `product_merchant_center`: See [Importing catalog data from Merchant Center](https://cloud.google.com/retail/recommendations-ai/docs/upload-catalog#mc). Supported values for user events imports: * `user_event` (default): One JSON UserEvent per line. * `user_event_ga360`: Using https://support.google.com/analytics/answer/3437719. Supported values for control imports: * `control` (default): One JSON Control per line. Supported values for catalog attribute imports: * `catalog_attribute` (default): One CSV CatalogAttribute per line.", "type": "string"}, "inputUris": {"description": "Required. Google Cloud Storage URIs to input files. URI can be up to 2000 characters long. URIs can match the full object path (for example, `gs://bucket/directory/object.json`) or a pattern matching one or more files, such as `gs://bucket/directory/*.json`. A request can contain at most 100 files, and each file can be up to 2 GB. See [Importing product information](https://cloud.google.com/retail/recommendations-ai/docs/upload-catalog) for the expected file format and setup instructions.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaGenerativeQuestionConfig": {"description": "Configuration for a single generated question.", "id": "GoogleCloudRetailV2alphaGenerativeQuestionConfig", "properties": {"allowedInConversation": {"description": "Optional. Whether the question is asked at serving time.", "type": "boolean"}, "catalog": {"description": "Required. Resource name of the catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "type": "string"}, "exampleValues": {"description": "Output only. Values that can be used to answer the question.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "facet": {"description": "Required. The facet to which the question is associated.", "type": "string"}, "finalQuestion": {"description": "Optional. The question that will be used at serving time. Question can have a max length of 300 bytes. When not populated, generated_question should be used.", "type": "string"}, "frequency": {"description": "Output only. The ratio of how often a question was asked.", "format": "float", "readOnly": true, "type": "number"}, "generatedQuestion": {"description": "Output only. The LLM generated question.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaGenerativeQuestionsFeatureConfig": {"description": "Configuration for overall generative question feature state.", "id": "GoogleCloudRetailV2alphaGenerativeQuestionsFeatureConfig", "properties": {"catalog": {"description": "Required. Resource name of the affected catalog. Format: projects/{project}/locations/{location}/catalogs/{catalog}", "type": "string"}, "featureEnabled": {"description": "Optional. Determines whether questions will be used at serving time. Note: This feature cannot be enabled until initial data requirements are satisfied.", "type": "boolean"}, "minimumProducts": {"description": "Optional. Minimum number of products in the response to trigger follow-up questions. Value must be 0 or positive.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaGetDefaultBranchResponse": {"description": "Response message of CatalogService.GetDefaultBranch.", "id": "GoogleCloudRetailV2alphaGetDefaultBranchResponse", "properties": {"branch": {"description": "Full resource name of the branch id currently set as default branch.", "type": "string"}, "note": {"description": "This corresponds to SetDefaultBranchRequest.note field, when this branch was set as default.", "type": "string"}, "setTime": {"description": "The time when this branch is set to default.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaImage": {"description": "Product image. Recommendations AI and Retail Search use product images to improve prediction and search results. Product images can be returned in results, and are shown in prediction or search previews in the console. Please try to provide correct product images and avoid using images with size too small.", "id": "GoogleCloudRetailV2alphaImage", "properties": {"height": {"description": "Height of the image in number of pixels. This field must be nonnegative. Otherwise, an INVALID_ARGUMENT error is returned.", "format": "int32", "type": "integer"}, "uri": {"description": "Required. URI of the image. This field must be a valid UTF-8 encoded URI with a length limit of 5,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. Google Merchant Center property [image_link](https://support.google.com/merchants/answer/6324350). Schema.org property [Product.image](https://schema.org/image).", "type": "string"}, "width": {"description": "Width of the image in number of pixels. This field must be nonnegative. Otherwise, an INVALID_ARGUMENT error is returned.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaImportCompletionDataRequest": {"description": "Request message for ImportCompletionData methods.", "id": "GoogleCloudRetailV2alphaImportCompletionDataRequest", "properties": {"inputConfig": {"$ref": "GoogleCloudRetailV2alphaCompletionDataInputConfig", "description": "Required. The desired input location of the data."}, "notificationPubsubTopic": {"description": "Pub/Sub topic for receiving notification. If this field is set, when the import is finished, a notification is sent to specified Pub/Sub topic. The message data is JSON string of a Operation. Format of the Pub/Sub topic is `projects/{project}/topics/{topic}`.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaImportCompletionDataResponse": {"description": "Response of the ImportCompletionDataRequest. If the long running operation is done, this message is returned by the google.longrunning.Operations.response field if the operation is successful.", "id": "GoogleCloudRetailV2alphaImportCompletionDataResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaImportErrorsConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudRetailV2alphaImportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage prefix for import errors. This must be an empty, existing Cloud Storage directory. Import errors are written to sharded files in this directory, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaImportMetadata": {"description": "Metadata related to the progress of the Import operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2alphaImportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "notificationPubsubTopic": {"description": "Pub/Sub topic for receiving notification. If this field is set, when the import is finished, a notification is sent to specified Pub/Sub topic. The message data is JSON string of a Operation. Format of the Pub/Sub topic is `projects/{project}/topics/{topic}`.", "type": "string"}, "requestId": {"deprecated": true, "description": "Deprecated. This field is never set.", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "transformedUserEventsMetadata": {"$ref": "GoogleCloudRetailV2alphaTransformedUserEventsMetadata", "description": "Metadata related to transform user events."}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaImportProductsRequest": {"description": "Request message for Import methods.", "id": "GoogleCloudRetailV2alphaImportProductsRequest", "properties": {"errorsConfig": {"$ref": "GoogleCloudRetailV2alphaImportErrorsConfig", "description": "The desired location of errors incurred during the Import."}, "inputConfig": {"$ref": "GoogleCloudRetailV2alphaProductInputConfig", "description": "Required. The desired input location of the data."}, "notificationPubsubTopic": {"description": "Full Pub/Sub topic name for receiving notification. If this field is set, when the import is finished, a notification is sent to specified Pub/Sub topic. The message data is JSON string of a Operation. Format of the Pub/Sub topic is `projects/{project}/topics/{topic}`. It has to be within the same project as ImportProductsRequest.parent. Make sure that both `<EMAIL>` and `<EMAIL>` have the `pubsub.topics.publish` IAM permission on the topic. Only supported when ImportProductsRequest.reconciliation_mode is set to `FULL`.", "type": "string"}, "reconciliationMode": {"description": "The mode of reconciliation between existing products and the products to be imported. Defaults to ReconciliationMode.INCREMENTAL.", "enum": ["RECONCILIATION_MODE_UNSPECIFIED", "INCREMENTAL", "FULL"], "enumDescriptions": ["Defaults to INCREMENTAL.", "Inserts new products or updates existing products.", "Calculates diff and replaces the entire product dataset. Existing products may be deleted if they are not present in the source location."], "type": "string"}, "requestId": {"deprecated": true, "description": "Deprecated. This field has no effect.", "type": "string"}, "skipDefaultBranchProtection": {"description": "If true, this performs the FULL import even if it would delete a large proportion of the products in the default branch, which could potentially cause outages if you have live predict/search traffic. Only supported when ImportProductsRequest.reconciliation_mode is set to `FULL`.", "type": "boolean"}, "updateMask": {"description": "Indicates which fields in the provided imported `products` to update. If not set, all fields are updated. If provided, only the existing product fields are updated. Missing products will not be created.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaImportProductsResponse": {"description": "Response of the ImportProductsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2alphaImportProductsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2alphaImportErrorsConfig", "description": "Echoes the destination for the complete errors in the request if set."}}, "type": "object"}, "GoogleCloudRetailV2alphaImportUserEventsRequest": {"description": "Request message for the ImportUserEvents request.", "id": "GoogleCloudRetailV2alphaImportUserEventsRequest", "properties": {"errorsConfig": {"$ref": "GoogleCloudRetailV2alphaImportErrorsConfig", "description": "The desired location of errors incurred during the Import. Cannot be set for inline user event imports."}, "inputConfig": {"$ref": "GoogleCloudRetailV2alphaUserEventInputConfig", "description": "Required. The desired input location of the data."}}, "type": "object"}, "GoogleCloudRetailV2alphaImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2alphaImportUserEventsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2alphaImportErrorsConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "importSummary": {"$ref": "GoogleCloudRetailV2alphaUserEventImportSummary", "description": "Aggregated statistics of user event import status."}}, "type": "object"}, "GoogleCloudRetailV2alphaIntentClassificationConfig": {"description": "The public proto to represent the intent classification config. It will be converted to the internal proto in the backend.", "id": "GoogleCloudRetailV2alphaIntentClassificationConfig", "properties": {"blocklistKeywords": {"description": "Optional. A list of keywords that will be used to classify the query to the \"BLOCKLISTED\" intent type. The keywords are case insensitive.", "items": {"type": "string"}, "type": "array"}, "disabledIntentTypes": {"description": "Optional. A list of intent types that will be disabled for this customer. The intent types must match one of the predefined intent types defined at https://cloud.google.com/retail/docs/reference/rpc/google.cloud.retail.v2alpha#querytype", "items": {"type": "string"}, "type": "array"}, "example": {"description": "Optional. A list of examples for intent classification.", "items": {"$ref": "GoogleCloudRetailV2alphaIntentClassificationConfigExample"}, "type": "array"}, "inlineSource": {"$ref": "GoogleCloudRetailV2alphaIntentClassificationConfigInlineSource", "description": "Optional. Inline source for intent classifications."}, "modelPreamble": {"description": "Optional. Customers can use the preamble to specify any requirements for blocklisting intent classification. This preamble will be added to the blocklisting intent classification model prompt.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaIntentClassificationConfigExample": {"description": "An example for intent classification.", "id": "GoogleCloudRetailV2alphaIntentClassificationConfigExample", "properties": {"classifiedPositive": {"description": "Required. Whether the example is classified positively.", "type": "boolean"}, "intentType": {"description": "Optional. The intent_type must match one of the predefined intent types defined at https://cloud.google.com/retail/docs/reference/rpc/google.cloud.retail.v2alpha#querytype", "type": "string"}, "query": {"description": "Required. Example query.", "type": "string"}, "reason": {"description": "Optional. The reason for the intent classification. This is used to explain the intent classification decision.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaIntentClassificationConfigInlineForceIntent": {"description": "An inline force intent classification configuration.", "id": "GoogleCloudRetailV2alphaIntentClassificationConfigInlineForceIntent", "properties": {"intentType": {"description": "Optional. The intent_type must match one of the predefined intent types defined at https://cloud.google.com/retail/docs/reference/rpc/google.cloud.retail.v2alpha#querytype", "type": "string"}, "operation": {"description": "Optional. The operation to perform for the query.", "enum": ["OPERATION_UNSPECIFIED", "EXACT_MATCH", "CONTAINS"], "enumDescriptions": ["Unspecified match operation.", "Exact match.", "Contains match."], "type": "string"}, "query": {"description": "Optional. A example query.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaIntentClassificationConfigInlineSource": {"description": "Inline source for intent classifications.", "id": "GoogleCloudRetailV2alphaIntentClassificationConfigInlineSource", "properties": {"inlineForceIntents": {"description": "Optional. A list of inline force intent classifications.", "items": {"$ref": "GoogleCloudRetailV2alphaIntentClassificationConfigInlineForceIntent"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaInterval": {"description": "A floating point interval.", "id": "GoogleCloudRetailV2alphaInterval", "properties": {"exclusiveMaximum": {"description": "Exclusive upper bound.", "format": "double", "type": "number"}, "exclusiveMinimum": {"description": "Exclusive lower bound.", "format": "double", "type": "number"}, "maximum": {"description": "Inclusive upper bound.", "format": "double", "type": "number"}, "minimum": {"description": "Inclusive lower bound.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudRetailV2alphaListBranchesResponse": {"description": "Response for BranchService.ListBranches method.", "id": "GoogleCloudRetailV2alphaListBranchesResponse", "properties": {"branches": {"description": "The Branches.", "items": {"$ref": "GoogleCloudRetailV2alphaBranch"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaListCatalogsResponse": {"description": "Response for CatalogService.ListCatalogs method.", "id": "GoogleCloudRetailV2alphaListCatalogsResponse", "properties": {"catalogs": {"description": "All the customer's Catalogs.", "items": {"$ref": "GoogleCloudRetailV2alphaCatalog"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as ListCatalogsRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaListControlsResponse": {"description": "Response for ListControls method.", "id": "GoogleCloudRetailV2alphaListControlsResponse", "properties": {"controls": {"description": "All the Controls for a given catalog.", "items": {"$ref": "GoogleCloudRetailV2alphaControl"}, "type": "array"}, "nextPageToken": {"description": "Pagination token, if not returned indicates the last page.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaListEnrolledSolutionsResponse": {"description": "Response for ListEnrolledSolutions method.", "id": "GoogleCloudRetailV2alphaListEnrolledSolutionsResponse", "properties": {"enrolledSolutions": {"description": "Retail API solutions that the project has enrolled.", "items": {"enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Retail Search."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaListGenerativeQuestionConfigsResponse": {"description": "Response for ListQuestions method.", "id": "GoogleCloudRetailV2alphaListGenerativeQuestionConfigsResponse", "properties": {"generativeQuestionConfigs": {"description": "All the questions for a given catalog.", "items": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionConfig"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaListMerchantCenterAccountLinksResponse": {"description": "Response for MerchantCenterAccountLinkService.ListMerchantCenterAccountLinks method.", "id": "GoogleCloudRetailV2alphaListMerchantCenterAccountLinksResponse", "properties": {"merchantCenterAccountLinks": {"description": "The links.", "items": {"$ref": "GoogleCloudRetailV2alphaMerchantCenterAccountLink"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaListModelsResponse": {"description": "Response to a ListModelRequest.", "id": "GoogleCloudRetailV2alphaListModelsResponse", "properties": {"models": {"description": "List of Models.", "items": {"$ref": "GoogleCloudRetailV2alphaModel"}, "type": "array"}, "nextPageToken": {"description": "Pagination token, if not returned indicates the last page.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaListProductsResponse": {"description": "Response message for ProductService.ListProducts method.", "id": "GoogleCloudRetailV2alphaListProductsResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as ListProductsRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "products": {"description": "The Products.", "items": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "type": "array"}, "totalSize": {"description": "The total count of matched Products irrespective of pagination. The total number of Products returned by pagination may be less than the total_size that matches. This field is ignored if ListProductsRequest.require_total_size is not set or ListProductsRequest.page_token is not empty.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaListServingConfigsResponse": {"description": "Response for ListServingConfigs method.", "id": "GoogleCloudRetailV2alphaListServingConfigsResponse", "properties": {"nextPageToken": {"description": "Pagination token, if not returned indicates the last page.", "type": "string"}, "servingConfigs": {"description": "All the ServingConfigs for a given catalog.", "items": {"$ref": "GoogleCloudRetailV2alphaServingConfig"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaLocalInventory": {"description": "The inventory information at a place (e.g. a store) identified by a place ID.", "id": "GoogleCloudRetailV2alphaLocalInventory", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaCustomAttribute"}, "description": "Optional. Additional local inventory attributes, for example, store name, promotion tags, etc. This field needs to pass all below criteria, otherwise an INVALID_ARGUMENT error is returned: * At most 30 attributes are allowed. * The key must be a UTF-8 encoded string with a length limit of 32 characters. * The key must match the pattern: `a-zA-Z0-9*`. For example, key0LikeThis or KEY_1_LIKE_THIS. * The attribute values must be of the same type (text or number). * Only 1 value is allowed for each attribute. * For text values, the length limit is 256 UTF-8 characters. * The attribute does not support search. The `searchable` field should be unset or set to false. * The max summed total bytes of custom attribute keys and values per product is 5MiB.", "type": "object"}, "availability": {"description": "Optional. The availability of the Product at this place_id. Default to Availability.IN_STOCK. For primary products with variants set the availability of the primary as Availability.OUT_OF_STOCK and set the true availability at the variant level. This way the primary product will be considered \"in stock\" as long as it has at least one variant in stock. For primary products with no variants set the true availability at the primary level. Corresponding properties: Google Merchant Center property [availability](https://support.google.com/merchants/answer/6324448). Schema.org property [Offer.availability](https://schema.org/availability). This field is currently only used by the Recommendations API. For Search, please make use of fulfillment_types or custom attributes for similar behaviour. See [here]( https://cloud.google.com/retail/docs/local-inventory-updates#local-inventory-update-methods) for more details.", "enum": ["AVAILABILITY_UNSPECIFIED", "IN_STOCK", "OUT_OF_STOCK", "PREORDER", "BACKORDER"], "enumDescriptions": ["Default product availability. Default to Availability.IN_STOCK if unset.", "Product in stock.", "Product out of stock.", "Product that is in pre-order state.", "Product that is back-ordered (i.e. temporarily out of stock)."], "type": "string"}, "fulfillmentTypes": {"description": "Optional. Supported fulfillment types. Valid fulfillment type values include commonly used types (such as pickup in store and same day delivery), and custom types. Customers have to map custom types to their display names before rendering UI. Supported values: * \"pickup-in-store\" * \"ship-to-store\" * \"same-day-delivery\" * \"next-day-delivery\" * \"custom-type-1\" * \"custom-type-2\" * \"custom-type-3\" * \"custom-type-4\" * \"custom-type-5\" If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned. All the elements must be distinct. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}, "placeId": {"description": "Optional. The place ID for the current set of inventory information.", "type": "string"}, "priceInfo": {"$ref": "GoogleCloudRetailV2alphaPriceInfo", "description": "Optional. Product price and cost information. Google Merchant Center property [price](https://support.google.com/merchants/answer/6324371)."}}, "type": "object"}, "GoogleCloudRetailV2alphaLoggingConfig": {"description": "Project level logging config to control what level of log will be generated and written to Cloud Logging.", "id": "GoogleCloudRetailV2alphaLoggingConfig", "properties": {"defaultLogGenerationRule": {"$ref": "GoogleCloudRetailV2alphaLoggingConfigLogGenerationRule", "description": "The log generation rule that applies by default to all services supporting log generation. It can be overridden by ServiceLogGenerationRule for service level control."}, "name": {"description": "Required. Immutable. The name of the LoggingConfig singleton resource. Format: projects/*/loggingConfig", "type": "string"}, "serviceLogGenerationRules": {"description": "Controls logging configurations more granularly for each supported service. This overrides the default_log_generation_rule for the services specified. For those not mentioned, they will fallback to the default log generation rule.", "items": {"$ref": "GoogleCloudRetailV2alphaLoggingConfigServiceLogGenerationRule"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaLoggingConfigLogGenerationRule": {"description": "The logging configurations for services supporting log generation.", "id": "GoogleCloudRetailV2alphaLoggingConfigLogGenerationRule", "properties": {"infoLogSampleRate": {"description": "The log sample rate for INFO level log entries. You can use this to reduce the number of entries generated for INFO level logs. DO NOT set this field if the logging_level is not LoggingLevel.LOG_ALL. Otherwise, an INVALID_ARGUMENT error is returned. Sample rate for INFO logs defaults to 1 when unset (generate and send all INFO logs to Cloud Logging). Its value must be greater than 0 and less than or equal to 1.", "format": "float", "type": "number"}, "loggingLevel": {"description": "The logging level. By default it is set to `LOG_WARNINGS_AND_ABOVE`.", "enum": ["LOGGING_LEVEL_UNSPECIFIED", "LOGGING_DISABLED", "LOG_ERRORS_AND_ABOVE", "LOG_WARNINGS_AND_ABOVE", "LOG_ALL"], "enumDescriptions": ["Default value. Defaults to `LOG_FOR_WARNINGS_AND_ABOVE` if unset.", "No log will be generated and sent to Cloud Logging.", "Log for operations resulted in fatal error.", "In addition to `LOG_ERRORS_AND_ABOVE`, also log for operations that have soft errors, quality suggestions.", "Log all operations, including successful ones."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaLoggingConfigServiceLogGenerationRule": {"description": "The granular logging configurations for supported services.", "id": "GoogleCloudRetailV2alphaLoggingConfigServiceLogGenerationRule", "properties": {"logGenerationRule": {"$ref": "GoogleCloudRetailV2alphaLoggingConfigLogGenerationRule", "description": "The log generation rule that applies to this service."}, "serviceName": {"description": "Required. Supported service names: \"CatalogService\", \"CompletionService\", \"ControlService\", \"MerchantCenterStreaming\", \"ModelService\", \"PredictionService\", \"ProductService\", \"ServingConfigService\", \"UserEventService\",", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaMerchantCenterAccountLink": {"description": "Represents a link between a Merchant Center account and a branch. After a link is established, products from the linked Merchant Center account are streamed to the linked branch.", "id": "GoogleCloudRetailV2alphaMerchantCenterAccountLink", "properties": {"branchId": {"description": "Required. The branch ID (e.g. 0/1/2) within the catalog that products from merchant_center_account_id are streamed to. When updating this field, an empty value will use the currently configured default branch. However, changing the default branch later on won't change the linked branch here. A single branch ID can only have one linked Merchant Center account ID.", "type": "string"}, "feedFilters": {"description": "Criteria for the Merchant Center feeds to be ingested via the link. All offers will be ingested if the list is empty. Otherwise the offers will be ingested from selected feeds.", "items": {"$ref": "GoogleCloudRetailV2alphaMerchantCenterAccountLinkMerchantCenterFeedFilter"}, "type": "array"}, "feedLabel": {"description": "The FeedLabel used to perform filtering. Note: this replaces [region_id](https://developers.google.com/shopping-content/reference/rest/v2.1/products#Product.FIELDS.feed_label). Example value: `US`. Example value: `FeedLabel1`.", "type": "string"}, "id": {"description": "Output only. Immutable. MerchantCenterAccountLink identifier, which is the final component of name. This field is auto generated and follows the convention: `BranchId_MerchantCenterAccountId`. `projects/*/locations/global/catalogs/default_catalog/merchantCenterAccountLinks/id_1`.", "readOnly": true, "type": "string"}, "languageCode": {"description": "Language of the title/description and other string attributes. Use language tags defined by [BCP 47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). ISO 639-1. This specifies the language of offers in Merchant Center that will be accepted. If empty, no language filtering will be performed. Example value: `en`.", "type": "string"}, "merchantCenterAccountId": {"description": "Required. The linked [Merchant center account id](https://developers.google.com/shopping-content/guides/accountstatuses). The account must be a standalone account or a sub-account of a MCA.", "format": "int64", "type": "string"}, "name": {"description": "Output only. Immutable. Full resource name of the Merchant Center Account Link, such as `projects/*/locations/global/catalogs/default_catalog/merchantCenterAccountLinks/merchant_center_account_link`.", "readOnly": true, "type": "string"}, "projectId": {"description": "Output only. Google Cloud project ID.", "readOnly": true, "type": "string"}, "source": {"description": "Optional. An optional arbitrary string that could be used as a tag for tracking link source.", "type": "string"}, "state": {"description": "Output only. Represents the state of the link.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ACTIVE", "FAILED"], "enumDescriptions": ["Default value.", "Link is created and LRO is not complete.", "<PERSON> is active.", "Link creation failed."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaMerchantCenterAccountLinkMerchantCenterFeedFilter": {"description": "Merchant Center Feed filter criterion.", "id": "GoogleCloudRetailV2alphaMerchantCenterAccountLinkMerchantCenterFeedFilter", "properties": {"dataSourceId": {"description": "AFM data source ID.", "format": "int64", "type": "string"}, "primaryFeedId": {"deprecated": true, "description": "Merchant Center primary feed ID. Deprecated: use data_source_id instead.", "format": "int64", "type": "string"}, "primaryFeedName": {"description": "Merchant Center primary feed name. The name is used for the display purposes only.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaMerchantCenterFeedFilter": {"description": "Merchant Center Feed filter criterion.", "id": "GoogleCloudRetailV2alphaMerchantCenterFeedFilter", "properties": {"dataSourceId": {"description": "AFM data source ID.", "format": "int64", "type": "string"}, "primaryFeedName": {"description": "Merchant Center primary feed name. The name is used for the display purposes only.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaMerchantCenterLink": {"description": "Represents a link between a Merchant Center account and a branch. After a link is established, products from the linked Merchant Center account are streamed to the linked branch.", "id": "GoogleCloudRetailV2alphaMerchantCenterLink", "properties": {"branchId": {"description": "The branch ID (e.g. 0/1/2) within this catalog that products from merchant_center_account_id are streamed to. When updating this field, an empty value will use the currently configured default branch. However, changing the default branch later on won't change the linked branch here. A single branch ID can only have one linked Merchant Center account ID.", "type": "string"}, "destinations": {"description": "String representing the destination to import for, all if left empty. List of possible values is given in [Included destination](https://support.google.com/merchants/answer/7501026). List of allowed string values: \"Shopping_ads\", \"Buy_on_google_listings\", \"Display_ads\", \"Local_inventory _ads\", \"Free_listings\", \"Free_local_listings\" NOTE: The string values are case sensitive.", "items": {"type": "string"}, "type": "array"}, "feeds": {"description": "Criteria for the Merchant Center feeds to be ingested via the link. All offers will be ingested if the list is empty. Otherwise the offers will be ingested from selected feeds.", "items": {"$ref": "GoogleCloudRetailV2alphaMerchantCenterFeedFilter"}, "type": "array"}, "languageCode": {"description": "Language of the title/description and other string attributes. Use language tags defined by [BCP 47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). ISO 639-1. This specifies the language of offers in Merchant Center that will be accepted. If empty no language filtering will be performed. Example value: `en`.", "type": "string"}, "merchantCenterAccountId": {"description": "Required. The linked [Merchant Center account ID](https://developers.google.com/shopping-content/guides/accountstatuses). The account must be a standalone account or a sub-account of a MCA.", "format": "int64", "type": "string"}, "regionCode": {"description": "Region code of offers to accept. 2-letter Uppercase ISO 3166-1 alpha-2 code. List of values can be found [here](https://www.iana.org/assignments/language-subtag-registry/language-subtag-registry) under the `region` tag. If left blank no region filtering will be performed. Example value: `US`.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaMerchantCenterLinkingConfig": {"description": "Configures Merchant Center linking. Links contained in the config will be used to sync data from a Merchant Center account to a Cloud Retail branch.", "id": "GoogleCloudRetailV2alphaMerchantCenterLinkingConfig", "properties": {"links": {"description": "Links between Merchant Center accounts and branches.", "items": {"$ref": "GoogleCloudRetailV2alphaMerchantCenterLink"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaModel": {"description": "Metadata that describes the training and serving parameters of a Model. A Model can be associated with a ServingConfig and then queried through the Predict API.", "id": "GoogleCloudRetailV2alphaModel", "properties": {"createTime": {"description": "Output only. Timestamp the Recommendation Model was created at.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataState": {"description": "Output only. The state of data requirements for this model: `DATA_OK` and `DATA_ERROR`. Recommendation model cannot be trained if the data is in `DATA_ERROR` state. Recommendation model can have `DATA_ERROR` state even if serving state is `ACTIVE`: models were trained successfully before, but cannot be refreshed because model no longer has sufficient data for training.", "enum": ["DATA_STATE_UNSPECIFIED", "DATA_OK", "DATA_ERROR"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The model has sufficient training data.", "The model does not have sufficient training data. Error messages can be queried via Stackdriver."], "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The display name of the model. Should be human readable, used to display Recommendation Models in the Retail Cloud Console Dashboard. UTF-8 encoded string with limit of 1024 characters.", "type": "string"}, "filteringOption": {"description": "Optional. If `RECOMMENDATIONS_FILTERING_ENABLED`, recommendation filtering by attributes is enabled for the model.", "enum": ["RECOMMENDATIONS_FILTERING_OPTION_UNSPECIFIED", "RECOMMENDATIONS_FILTERING_DISABLED", "RECOMMENDATIONS_FILTERING_ENABLED"], "enumDescriptions": ["Value used when unset. In this case, server behavior defaults to RECOMMENDATIONS_FILTERING_DISABLED.", "Recommendation filtering is disabled.", "Recommendation filtering is enabled."], "type": "string"}, "lastTuneTime": {"description": "Output only. The timestamp when the latest successful tune finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "modelFeaturesConfig": {"$ref": "GoogleCloudRetailV2alphaModelModelFeaturesConfig", "description": "Optional. Additional model features config."}, "name": {"description": "Required. The fully qualified resource name of the model. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}` catalog_id has char limit of 50. recommendation_model_id has char limit of 40.", "type": "string"}, "optimizationObjective": {"description": "Optional. The optimization objective e.g. `cvr`. Currently supported values: `ctr`, `cvr`, `revenue-per-order`. If not specified, we choose default based on model type. Default depends on type of recommendation: `recommended-for-you` => `ctr` `others-you-may-like` => `ctr` `frequently-bought-together` => `revenue_per_order` This field together with optimization_objective describe model metadata to use to control model training and serving. See https://cloud.google.com/retail/docs/models for more details on what the model metadata control and which combination of parameters are valid. For invalid combinations of parameters (e.g. type = `frequently-bought-together` and optimization_objective = `ctr`), you receive an error 400 if you try to create/update a recommendation with this set of knobs.", "type": "string"}, "pageOptimizationConfig": {"$ref": "GoogleCloudRetailV2alphaModelPageOptimizationConfig", "description": "Optional. The page optimization config."}, "periodicTuningState": {"description": "Optional. The state of periodic tuning. The period we use is 3 months - to do a one-off tune earlier use the `TuneModel` method. Default value is `PERIODIC_TUNING_ENABLED`.", "enum": ["PERIODIC_TUNING_STATE_UNSPECIFIED", "PERIODIC_TUNING_DISABLED", "ALL_TUNING_DISABLED", "PERIODIC_TUNING_ENABLED"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The model has periodic tuning disabled. Tuning can be reenabled by calling the `EnableModelPeriodicTuning` method or by calling the `TuneModel` method.", "The model cannot be tuned with periodic tuning OR the `TuneModel` method. Hide the options in customer UI and reject any requests through the backend self serve API.", "The model has periodic tuning enabled. Tuning can be disabled by calling the `DisableModelPeriodicTuning` method."], "type": "string"}, "servingConfigLists": {"description": "Output only. The list of valid serving configs associated with the PageOptimizationConfig.", "items": {"$ref": "GoogleCloudRetailV2alphaModelServingConfigList"}, "readOnly": true, "type": "array"}, "servingState": {"description": "Output only. The serving state of the model: `ACTIVE`, `NOT_ACTIVE`.", "enum": ["SERVING_STATE_UNSPECIFIED", "INACTIVE", "ACTIVE", "TUNED"], "enumDescriptions": ["Unspecified serving state.", "The model is not serving.", "The model is serving and can be queried.", "The model is trained on tuned hyperparameters and can be queried."], "readOnly": true, "type": "string"}, "trainingState": {"description": "Optional. The training state that the model is in (e.g. `TRAINING` or `PAUSED`). Since part of the cost of running the service is frequency of training - this can be used to determine when to train model in order to control cost. If not specified: the default value for `CreateModel` method is `TRAINING`. The default value for `UpdateModel` method is to keep the state the same as before.", "enum": ["TRAINING_STATE_UNSPECIFIED", "PAUSED", "TRAINING"], "enumDescriptions": ["Unspecified training state.", "The model training is paused.", "The model is training."], "type": "string"}, "tuningOperation": {"description": "Output only. The tune operation associated with the model. Can be used to determine if there is an ongoing tune for this recommendation. Empty field implies no tune is goig on.", "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of model e.g. `home-page`. Currently supported values: `recommended-for-you`, `others-you-may-like`, `frequently-bought-together`, `page-optimization`, `similar-items`, `buy-it-again`, `on-sale-items`, and `recently-viewed`(readonly value). This field together with optimization_objective describe model metadata to use to control model training and serving. See https://cloud.google.com/retail/docs/models for more details on what the model metadata control and which combination of parameters are valid. For invalid combinations of parameters (e.g. type = `frequently-bought-together` and optimization_objective = `ctr`), you receive an error 400 if you try to create/update a recommendation with this set of knobs.", "type": "string"}, "updateTime": {"description": "Output only. Timestamp the Recommendation Model was last updated. E.g. if a Recommendation Model was paused - this would be the time the pause was initiated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaModelFrequentlyBoughtTogetherFeaturesConfig": {"description": "Additional configs for the frequently-bought-together model type.", "id": "GoogleCloudRetailV2alphaModelFrequentlyBoughtTogetherFeaturesConfig", "properties": {"contextProductsType": {"description": "Optional. Specifies the context of the model when it is used in predict requests. Can only be set for the `frequently-bought-together` type. If it isn't specified, it defaults to MULTIPLE_CONTEXT_PRODUCTS.", "enum": ["CONTEXT_PRODUCTS_TYPE_UNSPECIFIED", "SINGLE_CONTEXT_PRODUCT", "MULTIPLE_CONTEXT_PRODUCTS"], "enumDescriptions": ["Unspecified default value, should never be explicitly set. Defaults to MULTIPLE_CONTEXT_PRODUCTS.", "Use only a single product as context for the recommendation. Typically used on pages like add-to-cart or product details.", "Use one or multiple products as context for the recommendation. Typically used on shopping cart pages."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaModelModelFeaturesConfig": {"description": "Additional model features config.", "id": "GoogleCloudRetailV2alphaModelModelFeaturesConfig", "properties": {"frequentlyBoughtTogetherConfig": {"$ref": "GoogleCloudRetailV2alphaModelFrequentlyBoughtTogetherFeaturesConfig", "description": "Additional configs for frequently-bought-together models."}}, "type": "object"}, "GoogleCloudRetailV2alphaModelPageOptimizationConfig": {"description": "The PageOptimizationConfig for model training. This determines how many panels to optimize for, and which serving configs to consider for each panel. The purpose of this model is to optimize which ServingConfig to show on which panels in way that optimizes the visitors shopping journey.", "id": "GoogleCloudRetailV2alphaModelPageOptimizationConfig", "properties": {"pageOptimizationEventType": {"description": "Required. The type of UserEvent this page optimization is shown for. Each page has an associated event type - this will be the corresponding event type for the page that the page optimization model is used on. Supported types: * `add-to-cart`: Products being added to cart. * `detail-page-view`: Products detail page viewed. * `home-page-view`: Homepage viewed * `category-page-view`: Homepage viewed * `shopping-cart-page-view`: User viewing a shopping cart. `home-page-view` only allows models with type `recommended-for-you`. All other page_optimization_event_type allow all Model.types.", "type": "string"}, "panels": {"description": "Required. A list of panel configurations. Limit = 5.", "items": {"$ref": "GoogleCloudRetailV2alphaModelPageOptimizationConfigPanel"}, "type": "array"}, "restriction": {"description": "Optional. How to restrict results across panels e.g. can the same ServingConfig be shown on multiple panels at once. If unspecified, default to `UNIQUE_MODEL_RESTRICTION`.", "enum": ["RESTRICTION_UNSPECIFIED", "NO_RESTRICTION", "UNIQUE_SERVING_CONFIG_RESTRICTION", "UNIQUE_MODEL_RESTRICTION", "UNIQUE_MODEL_TYPE_RESTRICTION"], "enumDescriptions": ["Unspecified value for restriction.", "Allow any ServingConfig to be show on any number of panels. Example: `Panel1 candidates`: pdp_ctr, pdp_cvr, home_page_ctr_no_diversity `Panel2 candidates`: home_page_ctr_no_diversity, home_page_ctr_diversity, pdp_cvr_no_diversity `Restriction` = NO_RESTRICTION `Valid combinations`: * * (pdp_ctr, home_page_ctr_no_diversity) * (pdp_ctr, home_page_ctr_diversity) * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_cvr, home_page_ctr_no_diversity) * (pdp_cvr, home_page_ctr_diversity) * (pdp_cvr, pdp_cvr_no_diversity) * (home_page_ctr_no_diversity, home_page_ctr_no_diversity) * (home_page_ctr_no_diversity, home_page_ctr_diversity) * (home_page_ctr_no_diversity, pdp_cvr_no_diversity) * `Invalid combinations`: []", "Do not allow the same ServingConfig.name to be shown on multiple panels. Example: `Panel1 candidates`: * pdp_ctr, pdp_cvr, home_page_ctr_no_diversity * `Panel2 candidates`: * home_page_ctr_no_diversity, home_page_ctr_diversity_low, pdp_cvr_no_diversity * `Restriction` = `UNIQUE_SERVING_CONFIG_RESTRICTION` `Valid combinations`: * * (pdp_ctr, home_page_ctr_no_diversity) * (pdp_ctr, home_page_ctr_diversity_low) * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_cvr, home_page_ctr_no_diversity) * (pdp_cvr, home_page_ctr_diversity_low) * (pdp_cvr, pdp_cvr_no_diversity) * (home_page_ctr_no_diversity, home_page_ctr_diversity_low) * (home_page_ctr_no_diversity, pdp_cvr_no_diversity) * `Invalid combinations`: * * (home_page_ctr_no_diversity, home_page_ctr_no_diversity) *", "Do not allow multiple ServingConfigs with same Model.name to be show on on different panels. Example: `Panel1 candidates`: * pdp_ctr, pdp_cvr, home_page_ctr_no_diversity * `Panel2 candidates`: * home_page_ctr_no_diversity, home_page_ctr_diversity_low, pdp_cvr_no_diversity * `Restriction` = `UNIQUE_MODEL_RESTRICTION` `Valid combinations`: * * (pdp_ctr, home_page_ctr_no_diversity) * (pdp_ctr, home_page_ctr_diversity) * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_cvr, home_page_ctr_no_diversity) * (pdp_cvr, home_page_ctr_diversity_low) * (home_page_ctr_no_diversity, pdp_cvr_no_diversity) * `Invalid combinations`: * * (home_page_ctr_no_diversity, home_page_ctr_no_diversity) * (pdp_cvr, pdp_cvr_no_diversity) *", "Do not allow multiple ServingConfigs with same Model.type to be shown on different panels. Example: `Panel1 candidates`: * pdp_ctr, pdp_cvr, home_page_ctr_no_diversity * `Panel2 candidates`: * home_page_ctr_no_diversity, home_page_ctr_diversity_low, pdp_cvr_no_diversity * `Restriction` = `UNIQUE_MODEL_RESTRICTION` `Valid combinations`: * * (pdp_ctr, home_page_ctr_no_diversity) * (pdp_ctr, home_page_ctr_diversity) * (pdp_cvr, home_page_ctr_no_diversity) * (pdp_cvr, home_page_ctr_diversity_low) * (home_page_ctr_no_diversity, pdp_cvr_no_diversity) * `Invalid combinations`: * * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_ctr, pdp_cvr_no_diversity) * (pdp_cvr, pdp_cvr_no_diversity) * (home_page_ctr_no_diversity, home_page_ctr_no_diversity) * (home_page_ctr_no_diversity, home_page_ctr_diversity) *"], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaModelPageOptimizationConfigCandidate": {"description": "A candidate to consider for a given panel. Currently only ServingConfig are valid candidates.", "id": "GoogleCloudRetailV2alphaModelPageOptimizationConfigCandidate", "properties": {"servingConfigId": {"description": "This has to be a valid ServingConfig identifier. For example, for a ServingConfig with full name: `projects/*/locations/global/catalogs/default_catalog/servingConfigs/my_candidate_config`, this would be `my_candidate_config`.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaModelPageOptimizationConfigPanel": {"description": "An individual panel with a list of ServingConfigs to consider for it.", "id": "GoogleCloudRetailV2alphaModelPageOptimizationConfigPanel", "properties": {"candidates": {"description": "Required. The candidates to consider on the panel.", "items": {"$ref": "GoogleCloudRetailV2alphaModelPageOptimizationConfigCandidate"}, "type": "array"}, "defaultCandidate": {"$ref": "GoogleCloudRetailV2alphaModelPageOptimizationConfigCandidate", "description": "Required. The default candidate. If the model fails at serving time, we fall back to the default."}, "displayName": {"description": "Optional. The name to display for the panel.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaModelServingConfigList": {"description": "Represents an ordered combination of valid serving configs, which can be used for `PAGE_OPTIMIZATION` recommendations.", "id": "GoogleCloudRetailV2alphaModelServingConfigList", "properties": {"servingConfigIds": {"description": "Optional. A set of valid serving configs that may be used for `PAGE_OPTIMIZATION`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaOutputConfig": {"description": "The output configuration setting.", "id": "GoogleCloudRetailV2alphaOutputConfig", "properties": {"bigqueryDestination": {"$ref": "GoogleCloudRetailV2alphaOutputConfigBigQueryDestination", "description": "The BigQuery location where the output is to be written to."}, "gcsDestination": {"$ref": "GoogleCloudRetailV2alphaOutputConfigGcsDestination", "description": "The Google Cloud Storage location where the output is to be written to."}}, "type": "object"}, "GoogleCloudRetailV2alphaOutputConfigBigQueryDestination": {"description": "The BigQuery output destination configuration.", "id": "GoogleCloudRetailV2alphaOutputConfigBigQueryDestination", "properties": {"datasetId": {"description": "Required. The ID of a BigQuery Dataset.", "type": "string"}, "tableIdPrefix": {"description": "Required. The prefix of exported BigQuery tables.", "type": "string"}, "tableType": {"description": "Required. Describes the table type. The following values are supported: * `table`: A BigQuery native table. * `view`: A virtual table defined by a SQL query.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaOutputConfigGcsDestination": {"description": "The Google Cloud Storage output destination configuration.", "id": "GoogleCloudRetailV2alphaOutputConfigGcsDestination", "properties": {"outputUriPrefix": {"description": "Required. The output uri prefix for saving output data to json files. Some mapping examples are as follows: output_uri_prefix sample output(assuming the object is foo.json) ======================== ============================================= gs://bucket/ gs://bucket/foo.json gs://bucket/folder/ gs://bucket/folder/foo.json gs://bucket/folder/item_ gs://bucket/folder/item_foo.json", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaOutputResult": {"description": "Output result that stores the information about where the exported data is stored.", "id": "GoogleCloudRetailV2alphaOutputResult", "properties": {"bigqueryResult": {"description": "The BigQuery location where the result is stored.", "items": {"$ref": "GoogleCloudRetailV2alphaBigQueryOutputResult"}, "type": "array"}, "gcsResult": {"description": "The Google Cloud Storage location where the result is stored.", "items": {"$ref": "GoogleCloudRetailV2alphaGcsOutputResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaPauseModelRequest": {"description": "Request for pausing training of a model.", "id": "GoogleCloudRetailV2alphaPauseModelRequest", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaPinControlMetadata": {"description": "Metadata for pinning to be returned in the response. This is used for distinguishing between applied vs dropped pins.", "id": "GoogleCloudRetailV2alphaPinControlMetadata", "properties": {"allMatchedPins": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaPinControlMetadataProductPins"}, "description": "Map of all matched pins, keyed by pin position.", "type": "object"}, "droppedPins": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaPinControlMetadataProductPins"}, "description": "Map of pins that were dropped due to overlap with other matching pins, keyed by pin position.", "type": "object"}}, "type": "object"}, "GoogleCloudRetailV2alphaPinControlMetadataProductPins": {"description": "List of product ids which have associated pins.", "id": "GoogleCloudRetailV2alphaPinControlMetadataProductPins", "properties": {"productId": {"description": "List of product ids which have associated pins.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaPredictRequest": {"description": "Request message for Predict method.", "id": "GoogleCloudRetailV2alphaPredictRequest", "properties": {"filter": {"description": "Filter for restricting prediction results with a length limit of 5,000 characters. Accepts values for tags and the `filterOutOfStockItems` flag. * Tag expressions. Restricts predictions to products that match all of the specified tags. Boolean operators `OR` and `NOT` are supported if the expression is enclosed in parentheses, and must be separated from the tag values by a space. `-\"tagA\"` is also supported and is equivalent to `NOT \"tagA\"`. Tag values must be double quoted UTF-8 encoded strings with a size limit of 1,000 characters. Note: \"Recently viewed\" models don't support tag filtering at the moment. * filterOutOfStockItems. Restricts predictions to products that do not have a stockState value of OUT_OF_STOCK. Examples: * tag=(\"Red\" OR \"Blue\") tag=\"New-Arrival\" tag=(NOT \"promotional\") * filterOutOfStockItems tag=(-\"promotional\") * filterOutOfStockItems If your filter blocks all prediction results, the API will return *no* results. If instead you want empty result sets to return generic (unfiltered) popular products, set `strictFiltering` to False in `PredictRequest.params`. Note that the API will never return items with storageStatus of \"EXPIRED\" or \"DELETED\" regardless of filter choices. If `filterSyntaxV2` is set to true under the `params` field, then attribute-based expressions are expected instead of the above described tag-based syntax. Examples: * (colors: ANY(\"Red\", \"Blue\")) AND NOT (categories: ANY(\"Phones\")) * (availability: ANY(\"IN_STOCK\")) AND (colors: ANY(\"Red\") OR categories: ANY(\"Phones\")) For more information, see [Filter recommendations](https://cloud.google.com/retail/docs/filter-recs).", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels applied to a resource must meet the following requirements: * Each resource can have multiple labels, up to a maximum of 64. * Each label must be a key-value pair. * Keys have a minimum length of 1 character and a maximum length of 63 characters and cannot be empty. Values can be empty and have a maximum length of 63 characters. * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. All characters must use UTF-8 encoding, and international characters are allowed. * The key portion of a label must be unique. However, you can use the same key with multiple resources. * Keys must start with a lowercase letter or international character. See [Google Cloud Document](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements) for more details.", "type": "object"}, "pageSize": {"description": "Maximum number of results to return. Set this property to the number of prediction results needed. If zero, the service will choose a reasonable default. The maximum allowed value is 100. Values above 100 will be coerced to 100.", "format": "int32", "type": "integer"}, "pageToken": {"deprecated": true, "description": "This field is not used; leave it unset.", "type": "string"}, "params": {"additionalProperties": {"type": "any"}, "description": "Additional domain specific parameters for the predictions. Allowed values: * `returnProduct`: <PERSON>olean. If set to true, the associated product object will be returned in the `results.metadata` field in the prediction response. * `returnScore`: Boolean. If set to true, the prediction 'score' corresponding to each returned product will be set in the `results.metadata` field in the prediction response. The given 'score' indicates the probability of a product being clicked/purchased given the user's context and history. * `strictFiltering`: Boolean. True by default. If set to false, the service will return generic (unfiltered) popular products instead of empty if your filter blocks all prediction results. * `priceRerankLevel`: String. Default empty. If set to be non-empty, then it needs to be one of {'no-price-reranking', 'low-price-reranking', 'medium-price-reranking', 'high-price-reranking'}. This gives request-level control and adjusts prediction results based on product price. * `diversityLevel`: String. Default empty. If set to be non-empty, then it needs to be one of {'no-diversity', 'low-diversity', 'medium-diversity', 'high-diversity', 'auto-diversity'}. This gives request-level control and adjusts prediction results based on product category. * `filterSyntaxV2`: Boolean. False by default. If set to true, the `filter` field is interpreteted according to the new, attribute-based syntax.", "type": "object"}, "userEvent": {"$ref": "GoogleCloudRetailV2alphaUserEvent", "description": "Required. Context about the user, what they are looking at and what action they took to trigger the predict request. Note that this user event detail won't be ingested to userEvent logs. Thus, a separate userEvent write request is required for event logging. Don't set UserEvent.visitor_id or UserInfo.user_id to the same fixed ID for different users. If you are trying to receive non-personalized recommendations (not recommended; this can negatively impact model performance), instead set UserEvent.visitor_id to a random unique ID and leave UserInfo.user_id unset."}, "validateOnly": {"description": "Use validate only mode for this prediction query. If set to true, a dummy model will be used that returns arbitrary products. Note that the validate only mode should only be used for testing the API, or if the model is not ready.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaPredictResponse": {"description": "Response message for predict method.", "id": "GoogleCloudRetailV2alphaPredictResponse", "properties": {"attributionToken": {"description": "A unique attribution token. This should be included in the UserEvent logs resulting from this recommendation, which enables accurate attribution of recommendation model performance.", "type": "string"}, "missingIds": {"description": "IDs of products in the request that were missing from the inventory.", "items": {"type": "string"}, "type": "array"}, "results": {"description": "A list of recommended products. The order represents the ranking (from the most relevant product to the least).", "items": {"$ref": "GoogleCloudRetailV2alphaPredictResponsePredictionResult"}, "type": "array"}, "validateOnly": {"description": "True if the validateOnly property was set in the request.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaPredictResponsePredictionResult": {"description": "PredictionResult represents the recommendation prediction results.", "id": "GoogleCloudRetailV2alphaPredictResponsePredictionResult", "properties": {"id": {"description": "ID of the recommended product", "type": "string"}, "metadata": {"additionalProperties": {"type": "any"}, "description": "Additional product metadata / annotations. Possible values: * `product`: JSON representation of the product. Is set if `returnProduct` is set to true in `PredictRequest.params`. * `score`: Prediction score in double value. Is set if `returnScore` is set to true in `PredictRequest.params`.", "type": "object"}}, "type": "object"}, "GoogleCloudRetailV2alphaPriceInfo": {"description": "The price information of a Product.", "id": "GoogleCloudRetailV2alphaPriceInfo", "properties": {"cost": {"description": "The costs associated with the sale of a particular product. Used for gross profit reporting. * Profit = price - cost Google Merchant Center property [cost_of_goods_sold](https://support.google.com/merchants/answer/9017895).", "format": "float", "type": "number"}, "currencyCode": {"description": "The 3-letter currency code defined in [ISO 4217](https://www.iso.org/iso-4217-currency-codes.html). If this field is an unrecognizable currency code, an INVALID_ARGUMENT error is returned. The Product.Type.VARIANT Products with the same Product.primary_product_id must share the same currency_code. Otherwise, a FAILED_PRECONDITION error is returned.", "type": "string"}, "originalPrice": {"description": "Price of the product without any discount. If zero, by default set to be the price. If set, original_price should be greater than or equal to price, otherwise an INVALID_ARGUMENT error is thrown.", "format": "float", "type": "number"}, "price": {"description": "Price of the product. Google Merchant Center property [price](https://support.google.com/merchants/answer/6324371). Schema.org property [Offer.price](https://schema.org/price).", "format": "float", "type": "number"}, "priceEffectiveTime": {"description": "The timestamp when the price starts to be effective. This can be set as a future timestamp, and the price is only used for search after price_effective_time. If so, the original_price must be set and original_price is used before price_effective_time. Do not set if price is always effective because it will cause additional latency during search.", "format": "google-datetime", "type": "string"}, "priceExpireTime": {"description": "The timestamp when the price stops to be effective. The price is used for search before price_expire_time. If this field is set, the original_price must be set and original_price is used after price_expire_time. Do not set if price is always effective because it will cause additional latency during search.", "format": "google-datetime", "type": "string"}, "priceRange": {"$ref": "GoogleCloudRetailV2alphaPriceInfoPriceRange", "description": "Output only. The price range of all the child Product.Type.VARIANT Products grouped together on the Product.Type.PRIMARY Product. Only populated for Product.Type.PRIMARY Products. Note: This field is OUTPUT_ONLY for ProductService.GetProduct. Do not set this field in API requests.", "readOnly": true}}, "type": "object"}, "GoogleCloudRetailV2alphaPriceInfoPriceRange": {"description": "The price range of all variant Product having the same Product.primary_product_id.", "id": "GoogleCloudRetailV2alphaPriceInfoPriceRange", "properties": {"originalPrice": {"$ref": "GoogleCloudRetailV2alphaInterval", "description": "The inclusive Product.pricing_info.original_price internal of all variant Product having the same Product.primary_product_id."}, "price": {"$ref": "GoogleCloudRetailV2alphaInterval", "description": "The inclusive Product.pricing_info.price interval of all variant Product having the same Product.primary_product_id."}}, "type": "object"}, "GoogleCloudRetailV2alphaProduct": {"description": "Product captures all metadata information of items to be recommended or searched.", "id": "GoogleCloudRetailV2alphaProduct", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaCustomAttribute"}, "description": "Highly encouraged. Extra product attributes to be included. For example, for products, this could include the store name, vendor, style, color, etc. These are very strong signals for recommendation model, thus we highly recommend providing the attributes here. Features that can take on one of a limited number of possible values. Two types of features can be set are: Textual features. some examples would be the brand/maker of a product, or country of a customer. Numerical features. Some examples would be the height/weight of a product, or age of a customer. For example: `{ \"vendor\": {\"text\": [\"vendor123\", \"vendor456\"]}, \"lengths_cm\": {\"numbers\":[2.3, 15.4]}, \"heights_cm\": {\"numbers\":[8.1, 6.4]} }`. This field needs to pass all below criteria, otherwise an INVALID_ARGUMENT error is returned: * Max entries count: 200. * The key must be a UTF-8 encoded string with a length limit of 128 characters. * For indexable attribute, the key must match the pattern: `a-zA-Z0-9*`. For example, `key0LikeThis` or `KEY_1_LIKE_THIS`. * For text attributes, at most 400 values are allowed. Empty values are not allowed. Each value must be a non-empty UTF-8 encoded string with a length limit of 256 characters. * For number attributes, at most 400 values are allowed.", "type": "object"}, "audience": {"$ref": "GoogleCloudRetailV2alphaAudience", "description": "The target group associated with a given audience (e.g. male, veterans, car owners, musicians, etc.) of the product."}, "availability": {"description": "The online availability of the Product. Default to Availability.IN_STOCK. For primary products with variants set the availability of the primary as Availability.OUT_OF_STOCK and set the true availability at the variant level. This way the primary product will be considered \"in stock\" as long as it has at least one variant in stock. For primary products with no variants set the true availability at the primary level. Corresponding properties: Google Merchant Center property [availability](https://support.google.com/merchants/answer/6324448). Schema.org property [Offer.availability](https://schema.org/availability).", "enum": ["AVAILABILITY_UNSPECIFIED", "IN_STOCK", "OUT_OF_STOCK", "PREORDER", "BACKORDER"], "enumDescriptions": ["Default product availability. Default to Availability.IN_STOCK if unset.", "Product in stock.", "Product out of stock.", "Product that is in pre-order state.", "Product that is back-ordered (i.e. temporarily out of stock)."], "type": "string"}, "availableQuantity": {"description": "The available quantity of the item.", "format": "int32", "type": "integer"}, "availableTime": {"description": "The timestamp when this Product becomes available for SearchService.Search. Note that this is only applicable to Type.PRIMARY and Type.COLLECTION, and ignored for Type.VARIANT.", "format": "google-datetime", "type": "string"}, "brands": {"description": "The brands of the product. A maximum of 30 brands are allowed unless overridden through the Google Cloud console. Each brand must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [brand](https://support.google.com/merchants/answer/6324351). Schema.org property [Product.brand](https://schema.org/brand).", "items": {"type": "string"}, "type": "array"}, "categories": {"description": "Product categories. This field is repeated for supporting one product belonging to several parallel categories. Strongly recommended using the full path for better search / recommendation quality. To represent full path of category, use '>' sign to separate different hierarchies. If '>' is part of the category name, replace it with other character(s). For example, if a shoes product belongs to both [\"Shoes & Accessories\" -> \"Shoes\"] and [\"Sports & Fitness\" -> \"Athletic Clothing\" -> \"Shoes\"], it could be represented as: \"categories\": [ \"Shoes & Accessories > Shoes\", \"Sports & Fitness > Athletic Clothing > Shoes\" ] Must be set for Type.PRIMARY Product otherwise an INVALID_ARGUMENT error is returned. At most 250 values are allowed per Product unless overridden through the Google Cloud console. Empty values are not allowed. Each value must be a UTF-8 encoded string with a length limit of 5,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property google_product_category. Schema.org property [Product.category] (https://schema.org/category). [mc_google_product_category]: https://support.google.com/merchants/answer/6324436", "items": {"type": "string"}, "type": "array"}, "collectionMemberIds": {"description": "The id of the collection members when type is Type.COLLECTION. Non-existent product ids are allowed. The type of the members must be either Type.PRIMARY or Type.VARIANT otherwise an INVALID_ARGUMENT error is thrown. Should not set it for other types. A maximum of 1000 values are allowed. Otherwise, an INVALID_ARGUMENT error is return.", "items": {"type": "string"}, "type": "array"}, "colorInfo": {"$ref": "GoogleCloudRetailV2alphaColorInfo", "description": "The color of the product. Corresponding properties: Google Merchant Center property [color](https://support.google.com/merchants/answer/6324487). Schema.org property [Product.color](https://schema.org/color)."}, "conditions": {"description": "The condition of the product. Strongly encouraged to use the standard values: \"new\", \"refurbished\", \"used\". A maximum of 1 value is allowed per Product. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [condition](https://support.google.com/merchants/answer/6324469). Schema.org property [Offer.itemCondition](https://schema.org/itemCondition).", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Product description. This field must be a UTF-8 encoded string with a length limit of 5,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [description](https://support.google.com/merchants/answer/6324468). Schema.org property [Product.description](https://schema.org/description).", "type": "string"}, "expireTime": {"description": "Note that this field is applied in the following ways: * If the Product is already expired when it is uploaded, this product is not indexed for search. * If the Product is not expired when it is uploaded, only the Type.PRIMARY's and Type.COLLECTION's expireTime is respected, and Type.VARIANT's expireTime is not used. In general, we suggest the users to delete the stale products explicitly, instead of using this field to determine staleness. expire_time must be later than available_time and publish_time, otherwise an INVALID_ARGUMENT error is thrown. Corresponding properties: Google Merchant Center property [expiration_date](https://support.google.com/merchants/answer/6324499).", "format": "google-datetime", "type": "string"}, "fulfillmentInfo": {"description": "Fulfillment information, such as the store IDs for in-store pickup or region IDs for different shipping methods. All the elements must have distinct FulfillmentInfo.type. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"$ref": "GoogleCloudRetailV2alphaFulfillmentInfo"}, "type": "array"}, "gtin": {"description": "The Global Trade Item Number (GTIN) of the product. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. This field must be a Unigram. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [gtin](https://support.google.com/merchants/answer/6324461). Schema.org property [Product.isbn](https://schema.org/isbn), [Product.gtin8](https://schema.org/gtin8), [Product.gtin12](https://schema.org/gtin12), [Product.gtin13](https://schema.org/gtin13), or [Product.gtin14](https://schema.org/gtin14). If the value is not a valid GTIN, an INVALID_ARGUMENT error is returned.", "type": "string"}, "id": {"description": "Immutable. Product identifier, which is the final component of name. For example, this field is \"id_1\", if name is `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/id_1`. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [id](https://support.google.com/merchants/answer/6324405). Schema.org property [Product.sku](https://schema.org/sku).", "type": "string"}, "images": {"description": "Product images for the product. We highly recommend putting the main image first. A maximum of 300 images are allowed. Corresponding properties: Google Merchant Center property [image_link](https://support.google.com/merchants/answer/6324350). Schema.org property [Product.image](https://schema.org/image).", "items": {"$ref": "GoogleCloudRetailV2alphaImage"}, "type": "array"}, "languageCode": {"description": "Language of the title/description and other string attributes. Use language tags defined by [BCP 47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). For product prediction, this field is ignored and the model automatically detects the text language. The Product can include text in different languages, but duplicating Products to provide text in multiple languages can result in degraded model performance. For product search this field is in use. It defaults to \"en-US\" if unset.", "type": "string"}, "localInventories": {"description": "Output only. A list of local inventories specific to different places. This field can be managed by ProductService.AddLocalInventories and ProductService.RemoveLocalInventories APIs if fine-grained, high-volume updates are necessary.", "items": {"$ref": "GoogleCloudRetailV2alphaLocalInventory"}, "readOnly": true, "type": "array"}, "materials": {"description": "The material of the product. For example, \"leather\", \"wooden\". A maximum of 20 values are allowed. Each value must be a UTF-8 encoded string with a length limit of 200 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [material](https://support.google.com/merchants/answer/6324410). Schema.org property [Product.material](https://schema.org/material).", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Immutable. Full resource name of the product, such as `projects/*/locations/global/catalogs/default_catalog/branches/default_branch/products/product_id`.", "type": "string"}, "patterns": {"description": "The pattern or graphic print of the product. For example, \"striped\", \"polka dot\", \"paisley\". A maximum of 20 values are allowed per Product. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [pattern](https://support.google.com/merchants/answer/6324483). Schema.org property [Product.pattern](https://schema.org/pattern).", "items": {"type": "string"}, "type": "array"}, "priceInfo": {"$ref": "GoogleCloudRetailV2alphaPriceInfo", "description": "Product price and cost information. Corresponding properties: Google Merchant Center property [price](https://support.google.com/merchants/answer/6324371)."}, "primaryProductId": {"description": "Variant group identifier. Must be an id, with the same parent branch with this product. Otherwise, an error is thrown. For Type.PRIMARY Products, this field can only be empty or set to the same value as id. For VARIANT Products, this field cannot be empty. A maximum of 2,000 products are allowed to share the same Type.PRIMARY Product. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [item_group_id](https://support.google.com/merchants/answer/6324507). Schema.org property [Product.inProductGroupWithID](https://schema.org/inProductGroupWithID).", "type": "string"}, "promotions": {"description": "The promotions applied to the product. A maximum of 10 values are allowed per Product. Only Promotion.promotion_id will be used, other fields will be ignored if set.", "items": {"$ref": "GoogleCloudRetailV2alphaPromotion"}, "type": "array"}, "publishTime": {"description": "The timestamp when the product is published by the retailer for the first time, which indicates the freshness of the products. Note that this field is different from available_time, given it purely describes product freshness regardless of when it is available on search and recommendation.", "format": "google-datetime", "type": "string"}, "rating": {"$ref": "GoogleCloudRetailV2alphaRating", "description": "The rating of this product."}, "retrievableFields": {"deprecated": true, "description": "Indicates which fields in the Products are returned in SearchResponse. Supported fields for all types: * audience * availability * brands * color_info * conditions * gtin * materials * name * patterns * price_info * rating * sizes * title * uri Supported fields only for Type.PRIMARY and Type.COLLECTION: * categories * description * images Supported fields only for Type.VARIANT: * Only the first image in images To mark attributes as retrievable, include paths of the form \"attributes.key\" where \"key\" is the key of a custom attribute, as specified in attributes. For Type.PRIMARY and Type.COLLECTION, the following fields are always returned in SearchResponse by default: * name For Type.VARIANT, the following fields are always returned in by default: * name * color_info Note: Returning more fields in SearchResponse can increase response payload size and serving latency. This field is deprecated. Use the retrievable site-wide control instead.", "format": "google-fieldmask", "type": "string"}, "sizes": {"description": "The size of the product. To represent different size systems or size types, consider using this format: [[[size_system:]size_type:]size_value]. For example, in \"US:MENS:M\", \"US\" represents size system; \"MENS\" represents size type; \"M\" represents size value. In \"GIRLS:27\", size system is empty; \"GIRLS\" represents size type; \"27\" represents size value. In \"32 inches\", both size system and size type are empty, while size value is \"32 inches\". A maximum of 20 values are allowed per Product. Each value must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [size](https://support.google.com/merchants/answer/6324492), [size_type](https://support.google.com/merchants/answer/6324497), and [size_system](https://support.google.com/merchants/answer/6324502). Schema.org property [Product.size](https://schema.org/size).", "items": {"type": "string"}, "type": "array"}, "tags": {"description": "Custom tags associated with the product. At most 250 values are allowed per Product. This value must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. This tag can be used for filtering recommendation results by passing the tag as part of the PredictRequest.filter. Corresponding properties: Google Merchant Center property [custom_label_0–4](https://support.google.com/merchants/answer/6324473).", "items": {"type": "string"}, "type": "array"}, "title": {"description": "Required. Product title. This field must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [title](https://support.google.com/merchants/answer/6324415). Schema.org property [Product.name](https://schema.org/name).", "type": "string"}, "ttl": {"description": "Input only. The TTL (time to live) of the product. Note that this is only applicable to Type.PRIMARY and Type.COLLECTION, and ignored for Type.VARIANT. In general, we suggest the users to delete the stale products explicitly, instead of using this field to determine staleness. If it is set, it must be a non-negative value, and expire_time is set as current timestamp plus ttl. The derived expire_time is returned in the output and ttl is left blank when retrieving the Product. If it is set, the product is not available for SearchService.Search after current timestamp plus ttl. However, the product can still be retrieved by ProductService.GetProduct and ProductService.ListProducts.", "format": "google-duration", "type": "string"}, "type": {"description": "Immutable. The type of the product. Default to Catalog.product_level_config.ingestion_product_type if unset.", "enum": ["TYPE_UNSPECIFIED", "PRIMARY", "VARIANT", "COLLECTION"], "enumDescriptions": ["Default value. Default to Catalog.product_level_config.ingestion_product_type if unset.", "The primary type. As the primary unit for predicting, indexing and search serving, a Type.PRIMARY Product is grouped with multiple Type.VARIANT Products.", "The variant type. Type.VARIANT Products usually share some common attributes on the same Type.PRIMARY Products, but they have variant attributes like different colors, sizes and prices, etc.", "The collection type. Collection products are bundled Type.PRIMARY Products or Type.VARIANT Products that are sold together, such as a jewelry set with necklaces, earrings and rings, etc."], "type": "string"}, "uri": {"description": "Canonical URL directly linking to the product detail page. It is strongly recommended to provide a valid uri for the product, otherwise the service performance could be significantly degraded. This field must be a UTF-8 encoded string with a length limit of 5,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. Corresponding properties: Google Merchant Center property [link](https://support.google.com/merchants/answer/6324416). Schema.org property [Offer.url](https://schema.org/url).", "type": "string"}, "variants": {"description": "Output only. Product variants grouped together on primary product which share similar product attributes. It's automatically grouped by primary_product_id for all the product variants. Only populated for Type.PRIMARY Products. Note: This field is OUTPUT_ONLY for ProductService.GetProduct. Do not set this field in API requests.", "items": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaProductAttributeInterval": {"description": "Product attribute name and numeric interval.", "id": "GoogleCloudRetailV2alphaProductAttributeInterval", "properties": {"interval": {"$ref": "GoogleCloudRetailV2alphaInterval", "description": "The numeric interval (e.g. [10, 20))"}, "name": {"description": "The attribute name (e.g. \"length\")", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaProductAttributeValue": {"description": "Product attribute which structured by an attribute name and value. This structure is used in conversational search filters and answers. For example, if we have `name=color` and `value=red`, this means that the color is `red`.", "id": "GoogleCloudRetailV2alphaProductAttributeValue", "properties": {"name": {"description": "The attribute name.", "type": "string"}, "value": {"description": "The attribute value.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaProductDetail": {"description": "Detailed product information associated with a user event.", "id": "GoogleCloudRetailV2alphaProductDetail", "properties": {"product": {"$ref": "GoogleCloudRetailV2alphaProduct", "description": "Required. Product information. Required field(s): * Product.id Optional override field(s): * Product.price_info If any supported optional fields are provided, we will treat them as a full override when looking up product information from the catalog. Thus, it is important to ensure that the overriding fields are accurate and complete. All other product fields are ignored and instead populated via catalog lookup after event ingestion."}, "quantity": {"description": "Quantity of the product associated with the user event. For example, this field will be 2 if two products are added to the shopping cart for `purchase-complete` event. Required for `add-to-cart` and `purchase-complete` event types.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaProductInlineSource": {"description": "The inline source for the input config for ImportProducts method.", "id": "GoogleCloudRetailV2alphaProductInlineSource", "properties": {"products": {"description": "Required. A list of products to update/create. Each product must have a valid Product.id. Recommended max of 100 items.", "items": {"$ref": "GoogleCloudRetailV2alphaProduct"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaProductInputConfig": {"description": "The input config source for products.", "id": "GoogleCloudRetailV2alphaProductInputConfig", "properties": {"bigQuerySource": {"$ref": "GoogleCloudRetailV2alphaBigQuerySource", "description": "BigQuery input source."}, "gcsSource": {"$ref": "GoogleCloudRetailV2alphaGcsSource", "description": "Google Cloud Storage location for the input content."}, "productInlineSource": {"$ref": "GoogleCloudRetailV2alphaProductInlineSource", "description": "The Inline source for the input content for products."}}, "type": "object"}, "GoogleCloudRetailV2alphaProductLevelConfig": {"description": "Configures what level the product should be uploaded with regards to how users will be send events and how predictions will be made.", "id": "GoogleCloudRetailV2alphaProductLevelConfig", "properties": {"ingestionProductType": {"description": "The type of Products allowed to be ingested into the catalog. Acceptable values are: * `primary` (default): You can ingest Products of all types. When ingesting a Product, its type will default to Product.Type.PRIMARY if unset. * `variant` (incompatible with Retail Search): You can only ingest Product.Type.VARIANT Products. This means Product.primary_product_id cannot be empty. If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned. If this field is `variant` and merchant_center_product_id_field is `itemGroupId`, an INVALID_ARGUMENT error is returned. See [Product levels](https://cloud.google.com/retail/docs/catalog#product-levels) for more details.", "type": "string"}, "merchantCenterProductIdField": {"description": "Which field of [Merchant Center Product](/bigquery-transfer/docs/merchant-center-products-schema) should be imported as Product.id. Acceptable values are: * `offerId` (default): Import `offerId` as the product ID. * `itemGroupId`: Import `itemGroupId` as the product ID. Notice that Retail API will choose one item from the ones with the same `itemGroupId`, and use it to represent the item group. If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned. If this field is `itemGroupId` and ingestion_product_type is `variant`, an INVALID_ARGUMENT error is returned. See [Product levels](https://cloud.google.com/retail/docs/catalog#product-levels) for more details.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaProject": {"description": "Metadata that describes a Cloud Retail Project.", "id": "GoogleCloudRetailV2alphaProject", "properties": {"enrolledSolutions": {"description": "Output only. Retail API solutions that the project has enrolled.", "items": {"enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Retail Search."], "type": "string"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. Full resource name of the retail project, such as `projects/{project_id_or_number}/retailProject`.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaPromotion": {"description": "Promotion specification.", "id": "GoogleCloudRetailV2alphaPromotion", "properties": {"promotionId": {"description": "Promotion identifier, which is the final component of name. For example, this field is \"free_gift\", if name is `projects/*/locations/global/catalogs/default_catalog/promotions/free_gift`. The value must be a UTF-8 encoded string with a length limit of 128 characters, and match the pattern: `a-zA-Z*`. For example, id0LikeThis or ID_1_LIKE_THIS. Otherwise, an INVALID_ARGUMENT error is returned. Corresponds to Google Merchant Center property [promotion_id](https://support.google.com/merchants/answer/7050148).", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaPurchaseTransaction": {"description": "A transaction represents the entire purchase transaction.", "id": "GoogleCloudRetailV2alphaPurchaseTransaction", "properties": {"cost": {"description": "All the costs associated with the products. These can be manufacturing costs, shipping expenses not borne by the end user, or any other costs, such that: * Profit = revenue - tax - cost", "format": "float", "type": "number"}, "currencyCode": {"description": "Required. Currency code. Use three-character ISO-4217 code.", "type": "string"}, "id": {"description": "The transaction ID with a length limit of 128 characters.", "type": "string"}, "revenue": {"description": "Required. Total non-zero revenue or grand total associated with the transaction. This value include shipping, tax, or other adjustments to total revenue that you want to include as part of your revenue calculations.", "format": "float", "type": "number"}, "tax": {"description": "All the taxes associated with the transaction.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudRetailV2alphaPurgeMetadata": {"description": "Metadata related to the progress of the Purge operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2alphaPurgeMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaPurgeProductsMetadata": {"description": "Metadata related to the progress of the PurgeProducts operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2alphaPurgeProductsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaPurgeProductsRequest": {"description": "Request message for PurgeProducts method.", "id": "GoogleCloudRetailV2alphaPurgeProductsRequest", "properties": {"filter": {"description": "Required. The filter string to specify the products to be deleted with a length limit of 5,000 characters. Empty string filter is not allowed. \"*\" implies delete all items in a branch. The eligible fields for filtering are: * `availability`: Double quoted Product.availability string. * `create_time` : in ISO 8601 \"zulu\" format. Supported syntax: * Comparators (\">\", \"<\", \">=\", \"<=\", \"=\"). Examples: * create_time <= \"2015-02-13T17:05:46Z\" * availability = \"IN_STOCK\" * Conjunctions (\"AND\") Examples: * create_time <= \"2015-02-13T17:05:46Z\" AND availability = \"PREORDER\" * Disjunctions (\"OR\") Examples: * create_time <= \"2015-02-13T17:05:46Z\" OR availability = \"IN_STOCK\" * Can support nested queries. Examples: * (create_time <= \"2015-02-13T17:05:46Z\" AND availability = \"PREORDER\") OR (create_time >= \"2015-02-14T13:03:32Z\" AND availability = \"IN_STOCK\") * Filter Limits: * Filter should not contain more than 6 conditions. * Max nesting depth should not exceed 2 levels. Examples queries: * Delete back order products created before a timestamp. create_time <= \"2015-02-13T17:05:46Z\" OR availability = \"BACKORDER\"", "type": "string"}, "force": {"description": "Actually perform the purge. If `force` is set to false, the method will return the expected purge count without deleting any products.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaPurgeProductsResponse": {"description": "Response of the PurgeProductsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRetailV2alphaPurgeProductsResponse", "properties": {"purgeCount": {"description": "The total count of products purged as a result of the operation.", "format": "int64", "type": "string"}, "purgeSample": {"description": "A sample of the product names that will be deleted. Only populated if `force` is set to false. A max of 100 names will be returned and the names are chosen at random.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaPurgeUserEventsRequest": {"description": "Request message for PurgeUserEvents method.", "id": "GoogleCloudRetailV2alphaPurgeUserEventsRequest", "properties": {"filter": {"description": "Required. The filter string to specify the events to be deleted with a length limit of 5,000 characters. Empty string filter is not allowed. The eligible fields for filtering are: * `eventType`: Double quoted UserEvent.event_type string. * `eventTime`: in ISO 8601 \"zulu\" format. * `visitorId`: Double quoted string. Specifying this will delete all events associated with a visitor. * `userId`: Double quoted string. Specifying this will delete all events associated with a user. Examples: * Deleting all events in a time range: `eventTime > \"2012-04-23T18:25:43.511Z\" eventTime < \"2012-04-23T18:30:43.511Z\"` * Deleting specific eventType in time range: `eventTime > \"2012-04-23T18:25:43.511Z\" eventType = \"detail-page-view\"` * Deleting all events for a specific visitor: `visitorId = \"visitor1024\"` The filtering fields are assumed to have an implicit AND.", "type": "string"}, "force": {"description": "Actually perform the purge. If `force` is set to false, the method will return the expected purge count without deleting any user events.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaPurgeUserEventsResponse": {"description": "Response of the PurgeUserEventsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRetailV2alphaPurgeUserEventsResponse", "properties": {"purgedEventsCount": {"description": "The total count of events purged as a result of the operation.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRating": {"description": "The rating of a Product.", "id": "GoogleCloudRetailV2alphaRating", "properties": {"averageRating": {"description": "The average rating of the Product. The rating is scaled at 1-5. Otherwise, an INVALID_ARGUMENT error is returned.", "format": "float", "type": "number"}, "ratingCount": {"description": "The total number of ratings. This value is independent of the value of rating_histogram. This value must be nonnegative. Otherwise, an INVALID_ARGUMENT error is returned.", "format": "int32", "type": "integer"}, "ratingHistogram": {"description": "List of rating counts per rating value (index = rating - 1). The list is empty if there is no rating. If the list is non-empty, its size is always 5. Otherwise, an INVALID_ARGUMENT error is returned. For example, [41, 14, 13, 47, 303]. It means that the Product got 41 ratings with 1 star, 14 ratings with 2 star, and so on.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaRejoinUserEventsMetadata": {"description": "Metadata for `RejoinUserEvents` method.", "id": "GoogleCloudRetailV2alphaRejoinUserEventsMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaRejoinUserEventsRequest": {"description": "Request message for RejoinUserEvents method.", "id": "GoogleCloudRetailV2alphaRejoinUserEventsRequest", "properties": {"userEventRejoinScope": {"description": "The type of the user event rejoin to define the scope and range of the user events to be rejoined with the latest product catalog. Defaults to `USER_EVENT_REJOIN_SCOPE_UNSPECIFIED` if this field is not set, or set to an invalid integer value.", "enum": ["USER_EVENT_REJOIN_SCOPE_UNSPECIFIED", "JOINED_EVENTS", "UNJOINED_EVENTS"], "enumDescriptions": ["Rejoin all events with the latest product catalog, including both joined events and unjoined events.", "Only rejoin joined events with the latest product catalog.", "Only rejoin unjoined events with the latest product catalog."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRejoinUserEventsResponse": {"description": "Response message for `RejoinUserEvents` method.", "id": "GoogleCloudRetailV2alphaRejoinUserEventsResponse", "properties": {"rejoinedUserEventsCount": {"description": "Number of user events that were joined with latest product catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveCatalogAttributeRequest": {"description": "Request for CatalogService.RemoveCatalogAttribute method.", "id": "GoogleCloudRetailV2alphaRemoveCatalogAttributeRequest", "properties": {"key": {"description": "Required. The attribute name key of the CatalogAttribute to remove.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveControlRequest": {"description": "Request for RemoveControl method.", "id": "GoogleCloudRetailV2alphaRemoveControlRequest", "properties": {"controlId": {"description": "Required. The id of the control to apply. Assumed to be in the same catalog as the serving config.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesMetadata": {"description": "Metadata related to the progress of the RemoveFulfillmentPlaces operation. Currently empty because there is no meaningful metadata populated from the ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesRequest": {"description": "Request message for ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesRequest", "properties": {"allowMissing": {"description": "If set to true, and the Product is not found, the fulfillment information will still be processed and retained for at most 1 day and processed once the Product is created. If set to false, a NOT_FOUND error is returned if the Product is not found.", "type": "boolean"}, "placeIds": {"description": "Required. The IDs for this type, such as the store IDs for \"pickup-in-store\" or the region IDs for \"same-day-delivery\", to be removed for this type. At least 1 value is required, and a maximum of 2000 values are allowed. Each value must be a string with a length limit of 10 characters, matching the pattern `[a-zA-Z0-9_-]+`, such as \"store1\" or \"REGION-2\". Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}, "removeTime": {"description": "The time when the fulfillment updates are issued, used to prevent out-of-order updates on fulfillment information. If not provided, the internal system time will be used.", "format": "google-datetime", "type": "string"}, "type": {"description": "Required. The fulfillment type, including commonly used types (such as pickup in store and same day delivery), and custom types. Supported values: * \"pickup-in-store\" * \"ship-to-store\" * \"same-day-delivery\" * \"next-day-delivery\" * \"custom-type-1\" * \"custom-type-2\" * \"custom-type-3\" * \"custom-type-4\" * \"custom-type-5\" If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned. This field directly corresponds to Product.fulfillment_info.type.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesResponse": {"description": "Response of the RemoveFulfillmentPlacesRequest. Currently empty because there is no meaningful response populated from the ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2alphaRemoveFulfillmentPlacesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveLocalInventoriesMetadata": {"description": "Metadata related to the progress of the RemoveLocalInventories operation. Currently empty because there is no meaningful metadata populated from the ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2alphaRemoveLocalInventoriesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveLocalInventoriesRequest": {"description": "Request message for ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2alphaRemoveLocalInventoriesRequest", "properties": {"allowMissing": {"description": "If set to true, and the Product is not found, the local inventory removal request will still be processed and retained for at most 1 day and processed once the Product is created. If set to false, a NOT_FOUND error is returned if the Product is not found.", "type": "boolean"}, "placeIds": {"description": "Required. A list of place IDs to have their inventory deleted. At most 3000 place IDs are allowed per request.", "items": {"type": "string"}, "type": "array"}, "removeTime": {"description": "The time when the inventory deletions are issued. Used to prevent out-of-order updates and deletions on local inventory fields. If not provided, the internal system time will be used.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRemoveLocalInventoriesResponse": {"description": "Response of the ProductService.RemoveLocalInventories API. Currently empty because there is no meaningful response populated from the ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2alphaRemoveLocalInventoriesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaReplaceCatalogAttributeRequest": {"description": "Request for CatalogService.ReplaceCatalogAttribute method.", "id": "GoogleCloudRetailV2alphaReplaceCatalogAttributeRequest", "properties": {"catalogAttribute": {"$ref": "GoogleCloudRetailV2alphaCatalogAttribute", "description": "Required. The updated CatalogAttribute."}, "updateMask": {"description": "Indicates which fields in the provided CatalogAttribute to update. The following are NOT supported: * CatalogAttribute.key If not set, all supported fields are updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaResumeModelRequest": {"description": "Request for resuming training of a model.", "id": "GoogleCloudRetailV2alphaResumeModelRequest", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaRule": {"description": "A rule is a condition-action pair * A condition defines when a rule is to be triggered. * An action specifies what occurs on that trigger. Currently rules only work for controls with SOLUTION_TYPE_SEARCH.", "id": "GoogleCloudRetailV2alphaRule", "properties": {"boostAction": {"$ref": "GoogleCloudRetailV2alphaRuleBoostAction", "description": "A boost action."}, "condition": {"$ref": "GoogleCloudRetailV2alphaCondition", "description": "Required. The condition that triggers the rule. If the condition is empty, the rule will always apply."}, "doNotAssociateAction": {"$ref": "GoogleCloudRetailV2alphaRuleDoNotAssociateAction", "description": "Prevents term from being associated with other terms."}, "filterAction": {"$ref": "GoogleCloudRetailV2alphaRuleFilterAction", "description": "Filters results."}, "forceReturnFacetAction": {"$ref": "GoogleCloudRetailV2alphaRuleForceReturnFacetAction", "description": "Force returns an attribute as a facet in the request."}, "ignoreAction": {"$ref": "GoogleCloudRetailV2alphaRuleIgnoreAction", "description": "Ignores specific terms from query during search."}, "onewaySynonymsAction": {"$ref": "GoogleCloudRetailV2alphaRuleOnewaySynonymsAction", "description": "Treats specific term as a synonym with a group of terms. Group of terms will not be treated as synonyms with the specific term."}, "pinAction": {"$ref": "GoogleCloudRetailV2alphaRulePinAction", "description": "Pins one or more specified products to a specific position in the results."}, "redirectAction": {"$ref": "GoogleCloudRetailV2alphaRuleRedirectAction", "description": "Redirects a shopper to a specific page."}, "removeFacetAction": {"$ref": "GoogleCloudRetailV2alphaRuleRemoveFacetAction", "description": "Remove an attribute as a facet in the request (if present)."}, "replacementAction": {"$ref": "GoogleCloudRetailV2alphaRuleReplacementAction", "description": "Replaces specific terms in the query."}, "twowaySynonymsAction": {"$ref": "GoogleCloudRetailV2alphaRuleTwowaySynonymsAction", "description": "Treats a set of terms as synonyms of one another."}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleBoostAction": {"description": "A boost action to apply to results matching condition specified above.", "id": "GoogleCloudRetailV2alphaRuleBoostAction", "properties": {"boost": {"description": "Strength of the condition boost, which must be in [-1, 1]. Negative boost means demotion. Default is 0.0. Setting to 1.0 gives the item a big promotion. However, it does not necessarily mean that the boosted item will be the top result at all times, nor that other items will be excluded. Results could still be shown even when none of them matches the condition. And results that are significantly more relevant to the search query can still trump your heavily favored but irrelevant items. Setting to -1.0 gives the item a big demotion. However, results that are deeply relevant might still be shown. The item will have an upstream battle to get a fairly high ranking, but it is not blocked out completely. Setting to 0.0 means no boost applied. The boosting condition is ignored.", "format": "float", "type": "number"}, "productsFilter": {"description": "The filter can have a max size of 5000 characters. An expression which specifies which products to apply an action to. The syntax and supported fields are the same as a filter expression. See SearchRequest.filter for detail syntax and limitations. Examples: * To boost products with product ID \"product_1\" or \"product_2\", and color \"Red\" or \"Blue\": *(id: ANY(\"product_1\", \"product_2\")) * *AND * *(colorFamilies: ANY(\"Red\", \"Blue\")) *", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleDoNotAssociateAction": {"description": "Prevents `query_term` from being associated with specified terms during search. Example: Don't associate \"gShoe\" and \"cheap\".", "id": "GoogleCloudRetailV2alphaRuleDoNotAssociateAction", "properties": {"doNotAssociateTerms": {"description": "Cannot contain duplicates or the query term. Can specify up to 100 terms.", "items": {"type": "string"}, "type": "array"}, "queryTerms": {"description": "Terms from the search query. Will not consider do_not_associate_terms for search if in search query. Can specify up to 100 terms.", "items": {"type": "string"}, "type": "array"}, "terms": {"description": "Will be [deprecated = true] post migration;", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleFilterAction": {"description": "* Rule Condition: - No Condition.query_terms provided is a global match. - 1 or more Condition.query_terms provided are combined with OR operator. * Action Input: The request query and filter that are applied to the retrieved products, in addition to any filters already provided with the SearchRequest. The AND operator is used to combine the query's existing filters with the filter rule(s). NOTE: May result in 0 results when filters conflict. * Action Result: Filters the returned objects to be ONLY those that passed the filter.", "id": "GoogleCloudRetailV2alphaRuleFilterAction", "properties": {"filter": {"description": "A filter to apply on the matching condition results. Supported features: * filter must be set. * Filter syntax is identical to SearchRequest.filter. For more information, see [Filter](/retail/docs/filter-and-order#filter). * To filter products with product ID \"product_1\" or \"product_2\", and color \"Red\" or \"Blue\": *(id: ANY(\"product_1\", \"product_2\")) * *AND * *(colorFamilies: ANY(\"Red\", \"Blue\")) *", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleForceReturnFacetAction": {"description": "Force returns an attribute/facet in the request around a certain position or above. * Rule Condition: Must specify non-empty Condition.query_terms (for search only) or Condition.page_categories (for browse only), but can't specify both. * Action Inputs: attribute name, position * Action Result: Will force return a facet key around a certain position or above if the condition is satisfied. Example: Suppose the query is \"shoes\", the Condition.query_terms is \"shoes\", the ForceReturnFacetAction.FacetPositionAdjustment.attribute_name is \"size\" and the ForceReturnFacetAction.FacetPositionAdjustment.position is 8. Two cases: a) The facet key \"size\" is not already in the top 8 slots, then the facet \"size\" will appear at a position close to 8. b) The facet key \"size\" in among the top 8 positions in the request, then it will stay at its current rank.", "id": "GoogleCloudRetailV2alphaRuleForceReturnFacetAction", "properties": {"facetPositionAdjustments": {"description": "Each instance corresponds to a force return attribute for the given condition. There can't be more 15 instances here.", "items": {"$ref": "GoogleCloudRetailV2alphaRuleForceReturnFacetActionFacetPositionAdjustment"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleForceReturnFacetActionFacetPositionAdjustment": {"description": "Each facet position adjustment consists of a single attribute name (i.e. facet key) along with a specified position.", "id": "GoogleCloudRetailV2alphaRuleForceReturnFacetActionFacetPositionAdjustment", "properties": {"attributeName": {"description": "The attribute name to force return as a facet. Each attribute name should be a valid attribute name, be non-empty and contain at most 80 characters long.", "type": "string"}, "position": {"description": "This is the position in the request as explained above. It should be strictly positive be at most 100.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleIgnoreAction": {"description": "Prevents a term in the query from being used in search. Example: Don't search for \"shoddy\".", "id": "GoogleCloudRetailV2alphaRuleIgnoreAction", "properties": {"ignoreTerms": {"description": "Terms to ignore in the search query.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleOnewaySynonymsAction": {"description": "Maps a set of terms to a set of synonyms. Set of synonyms will be treated as synonyms of each query term only. `query_terms` will not be treated as synonyms of each other. Example: \"sneakers\" will use a synonym of \"shoes\". \"shoes\" will not use a synonym of \"sneakers\".", "id": "GoogleCloudRetailV2alphaRuleOnewaySynonymsAction", "properties": {"onewayTerms": {"description": "Will be [deprecated = true] post migration;", "items": {"type": "string"}, "type": "array"}, "queryTerms": {"description": "Terms from the search query. Will treat synonyms as their synonyms. Not themselves synonyms of the synonyms. Can specify up to 100 terms.", "items": {"type": "string"}, "type": "array"}, "synonyms": {"description": "Defines a set of synonyms. Cannot contain duplicates. Can specify up to 100 synonyms.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaRulePinAction": {"description": "Pins one or more specified products to a specific position in the results. * Rule Condition: Must specify non-empty Condition.query_terms (for search only) or Condition.page_categories (for browse only), but can't specify both. * Action Input: mapping of `[pin_position, product_id]` pairs (pin position uses 1-based indexing). * Action Result: Will pin products with matching ids to the position specified in the final result order. Example: Suppose the query is `shoes`, the Condition.query_terms is `shoes` and the pin_map has `{1, \"pid1\"}`, then product with `pid1` will be pinned to the top position in the final results. If multiple PinActions are matched to a single request the actions will be processed from most to least recently updated. Pins to positions larger than the max allowed page size of 120 are not allowed.", "id": "GoogleCloudRetailV2alphaRulePinAction", "properties": {"pinMap": {"additionalProperties": {"type": "string"}, "description": "Required. A map of positions to product_ids. Partial matches per action are allowed, if a certain position in the map is already filled that `[position, product_id]` pair will be ignored but the rest may still be applied. This case will only occur if multiple pin actions are matched to a single request, as the map guarantees that pin positions are unique within the same action. Duplicate product_ids are not permitted within a single pin map. The max size of this map is 120, equivalent to the max [request page size](https://cloud.google.com/retail/docs/reference/rest/v2/projects.locations.catalogs.placements/search#request-body).", "type": "object"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleRedirectAction": {"description": "Redirects a shopper to a specific page. * Rule Condition: Must specify Condition.query_terms. * Action Input: Request Query * Action Result: Redirects shopper to provided uri.", "id": "GoogleCloudRetailV2alphaRuleRedirectAction", "properties": {"redirectUri": {"description": "URL must have length equal or less than 2000 characters.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleRemoveFacetAction": {"description": "Removes an attribute/facet in the request if is present. * Rule Condition: Must specify non-empty Condition.query_terms (for search only) or Condition.page_categories (for browse only), but can't specify both. * Action Input: attribute name * Action Result: Will remove the attribute (as a facet) from the request if it is present. Example: Suppose the query is \"shoes\", the Condition.query_terms is \"shoes\" and the attribute name \"size\", then facet key \"size\" will be removed from the request (if it is present).", "id": "GoogleCloudRetailV2alphaRuleRemoveFacetAction", "properties": {"attributeNames": {"description": "The attribute names (i.e. facet keys) to remove from the dynamic facets (if present in the request). There can't be more 3 attribute names. Each attribute name should be a valid attribute name, be non-empty and contain at most 80 characters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleReplacementAction": {"description": "Replaces a term in the query. Multiple replacement candidates can be specified. All `query_terms` will be replaced with the replacement term. Example: Replace \"gShoe\" with \"google shoe\".", "id": "GoogleCloudRetailV2alphaRuleReplacementAction", "properties": {"queryTerms": {"description": "Terms from the search query. Will be replaced by replacement term. Can specify up to 100 terms.", "items": {"type": "string"}, "type": "array"}, "replacementTerm": {"description": "Term that will be used for replacement.", "type": "string"}, "term": {"description": "Will be [deprecated = true] post migration;", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaRuleTwowaySynonymsAction": {"description": "Creates a set of terms that will be treated as synonyms of each other. Example: synonyms of \"sneakers\" and \"shoes\": * \"sneakers\" will use a synonym of \"shoes\". * \"shoes\" will use a synonym of \"sneakers\".", "id": "GoogleCloudRetailV2alphaRuleTwowaySynonymsAction", "properties": {"synonyms": {"description": "Defines a set of synonyms. Can specify up to 100 synonyms. Must specify at least 2 synonyms.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequest": {"description": "Request message for SearchService.Search method.", "id": "GoogleCloudRetailV2alphaSearchRequest", "properties": {"boostSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestBoostSpec", "description": "Boost specification to boost certain products. For more information, see [Boost results](https://cloud.google.com/retail/docs/boosting). Notice that if both ServingConfig.boost_control_ids and SearchRequest.boost_spec are set, the boost conditions from both places are evaluated. If a search request matches multiple boost conditions, the final boost score is equal to the sum of the boost scores from all matched boost conditions."}, "branch": {"description": "The branch resource name, such as `projects/*/locations/global/catalogs/default_catalog/branches/0`. Use \"default_branch\" as the branch ID or leave this field empty, to search products under the default branch.", "type": "string"}, "canonicalFilter": {"description": "The default filter that is applied when a user performs a search without checking any filters on the search page. The filter applied to every search request when quality improvement such as query expansion is needed. In the case a query does not have a sufficient amount of results this filter will be used to determine whether or not to enable the query expansion flow. The original filter will still be used for the query expanded search. This field is strongly recommended to achieve high search quality. For more information about filter syntax, see SearchRequest.filter.", "type": "string"}, "conversationalSearchSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpec", "description": "Optional. This field specifies all conversational related parameters addition to traditional retail search."}, "dynamicFacetSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestDynamicFacetSpec", "deprecated": true, "description": "Deprecated. Refer to https://cloud.google.com/retail/docs/configs#dynamic to enable dynamic facets. Do not set this field. The specification for dynamically generated facets. Notice that only textual facets can be dynamically generated."}, "entity": {"description": "The entity for customers that may run multiple different entities, domains, sites or regions, for example, `Google US`, `Google Ads`, `Waymo`, `google.com`, `youtube.com`, etc. If this is set, it should be exactly matched with UserEvent.entity to get search results boosted by entity.", "type": "string"}, "experimentId": {"description": "Optional. An ID for the experiment group this search belongs to.", "type": "string"}, "facetSpecs": {"description": "Facet specifications for faceted search. If empty, no facets are returned. A maximum of 200 values are allowed. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchRequestFacetSpec"}, "type": "array"}, "filter": {"description": "The filter syntax consists of an expression language for constructing a predicate from one or more fields of the products being filtered. Filter expression is case-sensitive. For more information, see [Filter](https://cloud.google.com/retail/docs/filter-and-order#filter). If this field is unrecognizable, an INVALID_ARGUMENT is returned.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels applied to a resource must meet the following requirements: * Each resource can have multiple labels, up to a maximum of 64. * Each label must be a key-value pair. * Keys have a minimum length of 1 character and a maximum length of 63 characters and cannot be empty. Values can be empty and have a maximum length of 63 characters. * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. All characters must use UTF-8 encoding, and international characters are allowed. * The key portion of a label must be unique. However, you can use the same key with multiple resources. * Keys must start with a lowercase letter or international character. For more information, see [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements) in the Resource Manager documentation.", "type": "object"}, "languageCode": {"description": "Optional. The BCP-47 language code, such as \"en-US\" or \"sr-Latn\" [list](https://www.unicode.org/cldr/charts/46/summary/root.html). For more information, see [Standardized codes](https://google.aip.dev/143). This field helps to better interpret the query. If a value isn't specified, the query language code is automatically detected, which may not be accurate.", "type": "string"}, "offset": {"description": "A 0-indexed integer that specifies the current offset (that is, starting result location, amongst the Products deemed by the API as relevant) in search results. This field is only considered if page_token is unset. If this field is negative, an INVALID_ARGUMENT is returned.", "format": "int32", "type": "integer"}, "orderBy": {"description": "The order in which products are returned. Products can be ordered by a field in an Product object. Leave it unset if ordered by relevance. OrderBy expression is case-sensitive. For more information, see [Order](https://cloud.google.com/retail/docs/filter-and-order#order). If this field is unrecognizable, an INVALID_ARGUMENT is returned.", "type": "string"}, "pageCategories": {"description": "The categories associated with a category page. Must be set for category navigation queries to achieve good search quality. The format should be the same as UserEvent.page_categories; To represent full path of category, use '>' sign to separate different hierarchies. If '>' is part of the category name, replace it with other character(s). Category pages include special pages such as sales or promotions. For instance, a special sale page may have the category hierarchy: \"pageCategories\" : [\"Sales > 2017 Black Friday Deals\"].", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum number of Products to return. If unspecified, defaults to a reasonable value. The maximum allowed value is 120. Values above 120 will be coerced to 120. If this field is negative, an INVALID_ARGUMENT is returned.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token SearchResponse.next_page_token, received from a previous SearchService.Search call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to SearchService.Search must match the call that provided the page token. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "personalizationSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestPersonalizationSpec", "description": "The specification for personalization. Notice that if both ServingConfig.personalization_spec and SearchRequest.personalization_spec are set. SearchRequest.personalization_spec will override ServingConfig.personalization_spec."}, "placeId": {"description": "Optional. An id corresponding to a place, such as a store id or region id. When specified, we use the price from the local inventory with the matching product's LocalInventory.place_id for revenue optimization.", "type": "string"}, "query": {"description": "Raw search query. If this field is empty, the request is considered a category browsing request and returned results are based on filter and page_categories.", "type": "string"}, "queryExpansionSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestQueryExpansionSpec", "description": "The query expansion specification that specifies the conditions under which query expansion occurs. For more information, see [Query expansion](https://cloud.google.com/retail/docs/result-size#query_expansion)."}, "regionCode": {"description": "Optional. The Unicode country/region code (CLDR) of a location, such as \"US\" and \"419\" [list](https://www.unicode.org/cldr/charts/46/supplemental/territory_information.html). For more information, see [Standardized codes](https://google.aip.dev/143). If set, then results will be boosted based on the region_code provided.", "type": "string"}, "relevanceThreshold": {"description": "The relevance threshold of the search results. Defaults to RelevanceThreshold.HIGH, which means only the most relevant results are shown, and the least number of results are returned. For more information, see [Adjust result size](https://cloud.google.com/retail/docs/result-size#relevance_thresholding).", "enum": ["RELEVANCE_THRESHOLD_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "LOWEST"], "enumDescriptions": ["Default value. In this case, server behavior defaults to RelevanceThreshold.HIGH.", "High relevance threshold.", "Medium relevance threshold.", "Low relevance threshold.", "Lowest relevance threshold."], "type": "string"}, "searchMode": {"description": "The search mode of the search request. If not specified, a single search request triggers both product search and faceted search.", "enum": ["SEARCH_MODE_UNSPECIFIED", "PRODUCT_SEARCH_ONLY", "FACETED_SEARCH_ONLY"], "enumDescriptions": ["Default value. In this case both product search and faceted search will be performed. Both SearchResponse.SearchResult and SearchResponse.Facet will be returned.", "Only product search will be performed. The faceted search will be disabled. Only SearchResponse.SearchResult will be returned. SearchResponse.Facet will not be returned, even if SearchRequest.facet_specs or SearchRequest.dynamic_facet_spec is set.", "Only faceted search will be performed. The product search will be disabled. When in this mode, one or both of SearchRequest.facet_specs and SearchRequest.dynamic_facet_spec should be set. Otherwise, an INVALID_ARGUMENT error is returned. Only SearchResponse.Facet will be returned. SearchResponse.SearchResult will not be returned."], "type": "string"}, "spellCorrectionSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestSpellCorrectionSpec", "description": "The spell correction specification that specifies the mode under which spell correction will take effect."}, "tileNavigationSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestTileNavigationSpec", "description": "Optional. This field specifies tile navigation related parameters."}, "userAttributes": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaStringList"}, "description": "Optional. The user attributes that could be used for personalization of search results. * Populate at most 100 key-value pairs per query. * Only supports string keys and repeated string values. * Duplicate keys are not allowed within a single query. Example: user_attributes: [ { key: \"pets\" value { values: \"dog\" values: \"cat\" } }, { key: \"state\" value { values: \"CA\" } } ]", "type": "object"}, "userInfo": {"$ref": "GoogleCloudRetailV2alphaUserInfo", "description": "User information."}, "variantRollupKeys": {"description": "The keys to fetch and rollup the matching variant Products attributes, FulfillmentInfo or LocalInventorys attributes. The attributes from all the matching variant Products or LocalInventorys are merged and de-duplicated. Notice that rollup attributes will lead to extra query latency. Maximum number of keys is 30. For FulfillmentInfo, a fulfillment type and a fulfillment ID must be provided in the format of \"fulfillmentType.fulfillmentId\". E.g., in \"pickupInStore.store123\", \"pickupInStore\" is fulfillment type and \"store123\" is the store ID. Supported keys are: * colorFamilies * price * originalPrice * discount * variantId * inventory(place_id,price) * inventory(place_id,original_price) * inventory(place_id,attributes.key), where key is any key in the Product.local_inventories.attributes map. * attributes.key, where key is any key in the Product.attributes map. * pickupInStore.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"pickup-in-store\". * shipToStore.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"ship-to-store\". * sameDayDelivery.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"same-day-delivery\". * nextDayDelivery.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"next-day-delivery\". * customFulfillment1.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"custom-type-1\". * customFulfillment2.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"custom-type-2\". * customFulfillment3.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"custom-type-3\". * customFulfillment4.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"custom-type-4\". * customFulfillment5.id, where id is any FulfillmentInfo.place_ids for FulfillmentInfo.type \"custom-type-5\". If this field is set to an invalid value other than these, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}, "visitorId": {"description": "Required. A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. This should be the same identifier as UserEvent.visitor_id. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestBoostSpec": {"description": "Boost specification to boost certain items.", "id": "GoogleCloudRetailV2alphaSearchRequestBoostSpec", "properties": {"conditionBoostSpecs": {"description": "Condition boost specifications. If a product matches multiple conditions in the specifications, boost scores from these specifications are all applied and combined in a non-linear way. Maximum number of specifications is 20.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchRequestBoostSpecConditionBoostSpec"}, "type": "array"}, "skipBoostSpecValidation": {"description": "Whether to skip boostspec validation. If this field is set to true, invalid BoostSpec.condition_boost_specs will be ignored and valid BoostSpec.condition_boost_specs will still be applied.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestBoostSpecConditionBoostSpec": {"description": "Boost applies to products which match a condition.", "id": "GoogleCloudRetailV2alphaSearchRequestBoostSpecConditionBoostSpec", "properties": {"boost": {"description": "Strength of the condition boost, which should be in [-1, 1]. Negative boost means demotion. Default is 0.0. Setting to 1.0 gives the item a big promotion. However, it does not necessarily mean that the boosted item will be the top result at all times, nor that other items will be excluded. Results could still be shown even when none of them matches the condition. And results that are significantly more relevant to the search query can still trump your heavily favored but irrelevant items. Setting to -1.0 gives the item a big demotion. However, results that are deeply relevant might still be shown. The item will have an upstream battle to get a fairly high ranking, but it is not blocked out completely. Setting to 0.0 means no boost applied. The boosting condition is ignored.", "format": "float", "type": "number"}, "condition": {"description": "An expression which specifies a boost condition. The syntax and supported fields are the same as a filter expression. See SearchRequest.filter for detail syntax and limitations. Examples: * To boost products with product ID \"product_1\" or \"product_2\", and color \"Red\" or \"Blue\": * (id: ANY(\"product_1\", \"product_2\")) AND (colorFamilies: ANY(\"Red\",\"Blue\"))", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpec": {"description": "This field specifies all conversational related parameters addition to traditional retail search.", "id": "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpec", "properties": {"conversationId": {"description": "This field specifies the conversation id, which maintains the state of the conversation between client side and server side. Use the value from the previous ConversationalSearchResult.conversation_id. For the initial request, this should be empty.", "type": "string"}, "followupConversationRequested": {"description": "This field specifies whether the customer would like to do conversational search. If this field is set to true, conversational related extra information will be returned from server side, including follow-up question, answer options, etc.", "type": "boolean"}, "userAnswer": {"$ref": "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpecUserAnswer", "description": "This field specifies the current user answer during the conversational search. This can be either user selected from suggested answers or user input plain text."}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpecUserAnswer": {"description": "This field specifies the current user answer during the conversational search. This can be either user selected from suggested answers or user input plain text.", "id": "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpecUserAnswer", "properties": {"selectedAnswer": {"$ref": "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpecUserAnswerSelectedAnswer", "description": "This field specifies the selected attributes during the conversational search. This should be a subset of ConversationalSearchResult.suggested_answers."}, "textAnswer": {"description": "This field specifies the incremental input text from the user during the conversational search.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpecUserAnswerSelectedAnswer": {"description": "This field specifies the selected answers during the conversational search.", "id": "GoogleCloudRetailV2alphaSearchRequestConversationalSearchSpecUserAnswerSelectedAnswer", "properties": {"productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "This field specifies the selected answer which is a attribute key-value."}, "productAttributeValues": {"deprecated": true, "description": "This field is deprecated and should not be set.", "items": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestDynamicFacetSpec": {"description": "The specifications of dynamically generated facets.", "id": "GoogleCloudRetailV2alphaSearchRequestDynamicFacetSpec", "properties": {"mode": {"description": "Mode of the DynamicFacet feature. Defaults to Mode.DISABLED if it's unset.", "enum": ["MODE_UNSPECIFIED", "DISABLED", "ENABLED"], "enumDescriptions": ["Default value.", "Disable Dynamic Facet.", "Automatic mode built by Google Retail Search."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestFacetSpec": {"description": "A facet specification to perform faceted search.", "id": "GoogleCloudRetailV2alphaSearchRequestFacetSpec", "properties": {"enableDynamicPosition": {"description": "Enables dynamic position for this facet. If set to true, the position of this facet among all facets in the response is determined by Google Retail Search. It is ordered together with dynamic facets if dynamic facets is enabled. If set to false, the position of this facet in the response is the same as in the request, and it is ranked before the facets with dynamic position enable and all dynamic facets. For example, you may always want to have rating facet returned in the response, but it's not necessarily to always display the rating facet at the top. In that case, you can set enable_dynamic_position to true so that the position of rating facet in response is determined by Google Retail Search. Another example, assuming you have the following facets in the request: * \"rating\", enable_dynamic_position = true * \"price\", enable_dynamic_position = false * \"brands\", enable_dynamic_position = false And also you have a dynamic facets enable, which generates a facet \"gender\". Then, the final order of the facets in the response can be (\"price\", \"brands\", \"rating\", \"gender\") or (\"price\", \"brands\", \"gender\", \"rating\") depends on how Google Retail Search orders \"gender\" and \"rating\" facets. However, notice that \"price\" and \"brands\" are always ranked at first and second position because their enable_dynamic_position values are false.", "type": "boolean"}, "excludedFilterKeys": {"description": "List of keys to exclude when faceting. By default, FacetKey.key is not excluded from the filter unless it is listed in this field. Listing a facet key in this field allows its values to appear as facet results, even when they are filtered out of search results. Using this field does not affect what search results are returned. For example, suppose there are 100 products with the color facet \"Red\" and 200 products with the color facet \"Blue\". A query containing the filter \"colorFamilies:ANY(\"Red\")\" and having \"colorFamilies\" as FacetKey.key would by default return only \"Red\" products in the search results, and also return \"Red\" with count 100 as the only color facet. Although there are also blue products available, \"Blue\" would not be shown as an available facet value. If \"colorFamilies\" is listed in \"excludedFilterKeys\", then the query returns the facet values \"Red\" with count 100 and \"Blue\" with count 200, because the \"colorFamilies\" key is now excluded from the filter. Because this field doesn't affect search results, the search results are still correctly filtered to return only \"Red\" products. A maximum of 100 values are allowed. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}, "facetKey": {"$ref": "GoogleCloudRetailV2alphaSearchRequestFacetSpecFacetKey", "description": "Required. The facet key specification."}, "limit": {"description": "Maximum of facet values that should be returned for this facet. If unspecified, defaults to 50. The maximum allowed value is 300. Values above 300 will be coerced to 300. If this field is negative, an INVALID_ARGUMENT is returned.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestFacetSpecFacetKey": {"description": "Specifies how a facet is computed.", "id": "GoogleCloudRetailV2alphaSearchRequestFacetSpecFacetKey", "properties": {"caseInsensitive": {"description": "True to make facet keys case insensitive when getting faceting values with prefixes or contains; false otherwise.", "type": "boolean"}, "contains": {"description": "Only get facet values that contains the given strings. For example, suppose \"categories\" has three values \"Women > Shoe\", \"Women > Dress\" and \"Men > Shoe\". If set \"contains\" to \"Shoe\", the \"categories\" facet gives only \"Women > Shoe\" and \"Men > Shoe\". Only supported on textual fields. Maximum is 10.", "items": {"type": "string"}, "type": "array"}, "intervals": {"description": "Set only if values should be bucketized into intervals. Must be set for facets with numerical values. Must not be set for facet with text values. Maximum number of intervals is 40. For all numerical facet keys that appear in the list of products from the catalog, the percentiles 0, 10, 30, 50, 70, 90, and 100 are computed from their distribution weekly. If the model assigns a high score to a numerical facet key and its intervals are not specified in the search request, these percentiles become the bounds for its intervals and are returned in the response. If the facet key intervals are specified in the request, then the specified intervals are returned instead.", "items": {"$ref": "GoogleCloudRetailV2alphaInterval"}, "type": "array"}, "key": {"description": "Required. Supported textual and numerical facet keys in Product object, over which the facet values are computed. Facet key is case-sensitive. Allowed facet keys when FacetKey.query is not specified: * textual_field = * \"brands\" * \"categories\" * \"genders\" * \"ageGroups\" * \"availability\" * \"colorFamilies\" * \"colors\" * \"sizes\" * \"materials\" * \"patterns\" * \"conditions\" * \"attributes.key\" * \"pickupInStore\" * \"shipToStore\" * \"sameDayDelivery\" * \"nextDayDelivery\" * \"customFulfillment1\" * \"customFulfillment2\" * \"customFulfillment3\" * \"customFulfillment4\" * \"customFulfillment5\" * \"inventory(place_id,attributes.key)\" * numerical_field = * \"price\" * \"discount\" * \"rating\" * \"ratingCount\" * \"attributes.key\" * \"inventory(place_id,price)\" * \"inventory(place_id,original_price)\" * \"inventory(place_id,attributes.key)\"", "type": "string"}, "orderBy": {"description": "The order in which SearchResponse.Facet.values are returned. Allowed values are: * \"count desc\", which means order by SearchResponse.Facet.values.count descending. * \"value desc\", which means order by SearchResponse.Facet.values.value descending. Only applies to textual facets. If not set, textual values are sorted in [natural order](https://en.wikipedia.org/wiki/Natural_sort_order); numerical intervals are sorted in the order given by FacetSpec.FacetKey.intervals; FulfillmentInfo.place_ids are sorted in the order given by FacetSpec.FacetKey.restricted_values.", "type": "string"}, "prefixes": {"description": "Only get facet values that start with the given string prefix. For example, suppose \"categories\" has three values \"Women > Shoe\", \"Women > Dress\" and \"Men > Shoe\". If set \"prefixes\" to \"Women\", the \"categories\" facet gives only \"Women > Shoe\" and \"Women > Dress\". Only supported on textual fields. Maximum is 10.", "items": {"type": "string"}, "type": "array"}, "query": {"description": "The query that is used to compute facet for the given facet key. When provided, it overrides the default behavior of facet computation. The query syntax is the same as a filter expression. See SearchRequest.filter for detail syntax and limitations. Notice that there is no limitation on FacetKey.key when query is specified. In the response, SearchResponse.Facet.values.value is always \"1\" and SearchResponse.Facet.values.count is the number of results that match the query. For example, you can set a customized facet for \"shipToStore\", where FacetKey.key is \"customizedShipToStore\", and FacetKey.query is \"availability: ANY(\\\"IN_STOCK\\\") AND shipToStore: ANY(\\\"123\\\")\". Then the facet counts the products that are both in stock and ship to store \"123\".", "type": "string"}, "restrictedValues": {"description": "Only get facet for the given restricted values. For example, when using \"pickupInStore\" as key and set restricted values to [\"store123\", \"store456\"], only facets for \"store123\" and \"store456\" are returned. Only supported on predefined textual fields, custom textual attributes and fulfillments. Maximum is 20. Must be set for the fulfillment facet keys: * pickupInStore * shipToStore * sameDayDelivery * nextDayDelivery * customFulfillment1 * customFulfillment2 * customFulfillment3 * customFulfillment4 * customFulfillment5", "items": {"type": "string"}, "type": "array"}, "returnMinMax": {"description": "Returns the min and max value for each numerical facet intervals. Ignored for textual facets.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestPersonalizationSpec": {"description": "The specification for personalization.", "id": "GoogleCloudRetailV2alphaSearchRequestPersonalizationSpec", "properties": {"mode": {"description": "Defaults to Mode.AUTO.", "enum": ["MODE_UNSPECIFIED", "AUTO", "DISABLED"], "enumDescriptions": ["Default value. In this case, server behavior defaults to Mode.AUTO.", "Let CRS decide whether to use personalization based on quality of user event data.", "Disable personalization."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestQueryExpansionSpec": {"description": "Specification to determine under which conditions query expansion should occur.", "id": "GoogleCloudRetailV2alphaSearchRequestQueryExpansionSpec", "properties": {"condition": {"description": "The condition under which query expansion should occur. Default to Condition.DISABLED.", "enum": ["CONDITION_UNSPECIFIED", "DISABLED", "AUTO"], "enumDescriptions": ["Unspecified query expansion condition. In this case, server behavior defaults to Condition.DISABLED.", "Disabled query expansion. Only the exact search query is used, even if SearchResponse.total_size is zero.", "Automatic query expansion built by Google Retail Search."], "type": "string"}, "pinUnexpandedResults": {"description": "Whether to pin unexpanded results. The default value is false. If this field is set to true, unexpanded products are always at the top of the search results, followed by the expanded results.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestSpellCorrectionSpec": {"description": "The specification for query spell correction.", "id": "GoogleCloudRetailV2alphaSearchRequestSpellCorrectionSpec", "properties": {"mode": {"description": "The mode under which spell correction should take effect to replace the original search query. Default to Mode.AUTO.", "enum": ["MODE_UNSPECIFIED", "SUGGESTION_ONLY", "AUTO"], "enumDescriptions": ["Unspecified spell correction mode. In this case, server behavior defaults to Mode.AUTO.", "Google Retail Search will try to find a spell suggestion if there is any and put in the SearchResponse.corrected_query. The spell suggestion will not be used as the search query.", "Automatic spell correction built by Google Retail Search. Search will be based on the corrected query if found."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchRequestTileNavigationSpec": {"description": "This field specifies tile navigation related parameters.", "id": "GoogleCloudRetailV2alphaSearchRequestTileNavigationSpec", "properties": {"appliedTiles": {"description": "This optional field specifies the tiles which are already clicked in client side. While the feature works without this field set, particularly for an initial query, it is highly recommended to set this field because it can improve the quality of the search response and removes possible duplicate tiles. NOTE: This field is not being used for filtering search products. Client side should also put all the applied tiles in SearchRequest.filter.", "items": {"$ref": "GoogleCloudRetailV2alphaTile"}, "type": "array"}, "tileNavigationRequested": {"description": "This field specifies whether the customer would like to request tile navigation.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponse": {"description": "Response message for SearchService.Search method.", "id": "GoogleCloudRetailV2alphaSearchResponse", "properties": {"appliedControls": {"description": "The fully qualified resource name of applied [controls](https://cloud.google.com/retail/docs/serving-control-rules).", "items": {"type": "string"}, "type": "array"}, "attributionToken": {"description": "A unique search token. This should be included in the UserEvent logs resulting from this search, which enables accurate attribution of search model performance.", "type": "string"}, "conversationalSearchResult": {"$ref": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResult", "description": "This field specifies all related information that is needed on client side for UI rendering of conversational retail search."}, "correctedQuery": {"description": "Contains the spell corrected query, if found. If the spell correction type is AUTOMATIC, then the search results are based on corrected_query. Otherwise the original query is used for search.", "type": "string"}, "experimentInfo": {"description": "Metadata related to A/B testing experiment associated with this response. Only exists when an experiment is triggered.", "items": {"$ref": "GoogleCloudRetailV2alphaExperimentInfo"}, "type": "array"}, "facets": {"description": "Results of facets requested by user.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchResponseFacet"}, "type": "array"}, "invalidConditionBoostSpecs": {"description": "The invalid SearchRequest.BoostSpec.condition_boost_specs that are not applied during serving.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchRequestBoostSpecConditionBoostSpec"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as SearchRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "pinControlMetadata": {"$ref": "GoogleCloudRetailV2alphaPinControlMetadata", "description": "Metadata for pin controls which were applicable to the request. This contains two map fields, one for all matched pins and one for pins which were matched but not applied. The two maps are keyed by pin position, and the values are the product ids which were matched to that pin."}, "queryExpansionInfo": {"$ref": "GoogleCloudRetailV2alphaSearchResponseQueryExpansionInfo", "description": "Query expansion information for the returned results."}, "redirectUri": {"description": "The URI of a customer-defined redirect page. If redirect action is triggered, no search is performed, and only redirect_uri and attribution_token are set in the response.", "type": "string"}, "results": {"description": "A list of matched items. The order represents the ranking.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchResponseSearchResult"}, "type": "array"}, "tileNavigationResult": {"$ref": "GoogleCloudRetailV2alphaSearchResponseTileNavigationResult", "description": "This field specifies all related information for tile navigation that will be used in client side."}, "totalSize": {"description": "The estimated total count of matched items irrespective of pagination. The count of results returned by pagination may be less than the total_size that matches.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResult": {"description": "This field specifies all related information that is needed on client side for UI rendering of conversational retail search.", "id": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResult", "properties": {"additionalFilter": {"$ref": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultAdditionalFilter", "description": "This is the incremental additional filters implied from the current user answer. User should add the suggested addition filters to the previous SearchRequest.filter, and use the merged filter in the follow up search request."}, "additionalFilters": {"deprecated": true, "description": "This field is deprecated but will be kept for backward compatibility. There is expected to have only one additional filter and the value will be the same to the same as field `additional_filter`.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultAdditionalFilter"}, "type": "array"}, "conversationId": {"description": "Conversation UUID. This field will be stored in client side storage to maintain the conversation session with server and will be used for next search request's SearchRequest.ConversationalSearchSpec.conversation_id to restore conversation state in server.", "type": "string"}, "followupQuestion": {"description": "The follow-up question. e.g., `What is the color?`", "type": "string"}, "refinedQuery": {"description": "The current refined query for the conversational search. This field will be used in customer UI that the query in the search bar should be replaced with the refined query. For example, if SearchRequest.query is `dress` and next SearchRequest.ConversationalSearchSpec.UserAnswer.text_answer is `red color`, which does not match any product attribute value filters, the refined query will be `dress, red color`.", "type": "string"}, "suggestedAnswers": {"description": "The answer options provided to client for the follow-up question.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultSuggestedAnswer"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultAdditionalFilter": {"description": "Additional filter that client side need to apply.", "id": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultAdditionalFilter", "properties": {"productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "Product attribute value, including an attribute key and an attribute value. Other types can be added here in the future."}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultSuggestedAnswer": {"description": "Suggested answers to the follow-up question.", "id": "GoogleCloudRetailV2alphaSearchResponseConversationalSearchResultSuggestedAnswer", "properties": {"productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "Product attribute value, including an attribute key and an attribute value. Other types can be added here in the future."}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseFacet": {"description": "A facet result.", "id": "GoogleCloudRetailV2alphaSearchResponseFacet", "properties": {"dynamicFacet": {"description": "Whether the facet is dynamically generated.", "type": "boolean"}, "key": {"description": "The key for this facet. E.g., \"colorFamilies\" or \"price\" or \"attributes.attr1\".", "type": "string"}, "values": {"description": "The facet values for this field.", "items": {"$ref": "GoogleCloudRetailV2alphaSearchResponseFacetFacetValue"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseFacetFacetValue": {"description": "A facet value which contains value names and their count.", "id": "GoogleCloudRetailV2alphaSearchResponseFacetFacetValue", "properties": {"count": {"description": "Number of items that have this facet value.", "format": "int64", "type": "string"}, "interval": {"$ref": "GoogleCloudRetailV2alphaInterval", "description": "Interval value for a facet, such as [10, 20) for facet \"price\"."}, "maxValue": {"description": "The maximum value in the FacetValue.interval. Only supported on numerical facets and returned if SearchRequest.FacetSpec.FacetKey.return_min_max is true.", "format": "double", "type": "number"}, "minValue": {"description": "The minimum value in the FacetValue.interval. Only supported on numerical facets and returned if SearchRequest.FacetSpec.FacetKey.return_min_max is true.", "format": "double", "type": "number"}, "value": {"description": "Text value of a facet, such as \"Black\" for facet \"colorFamilies\".", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseQueryExpansionInfo": {"description": "Information describing query expansion including whether expansion has occurred.", "id": "GoogleCloudRetailV2alphaSearchResponseQueryExpansionInfo", "properties": {"expandedQuery": {"description": "<PERSON><PERSON> describing whether query expansion has occurred.", "type": "boolean"}, "pinnedResultCount": {"description": "Number of pinned results. This field will only be set when expansion happens and SearchRequest.QueryExpansionSpec.pin_unexpanded_results is set to true.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseSearchResult": {"description": "Represents the search results.", "id": "GoogleCloudRetailV2alphaSearchResponseSearchResult", "properties": {"id": {"description": "Product.id of the searched Product.", "type": "string"}, "matchingVariantCount": {"description": "The count of matched variant Products.", "format": "int32", "type": "integer"}, "matchingVariantFields": {"additionalProperties": {"format": "google-fieldmask", "type": "string"}, "description": "If a variant Product matches the search query, this map indicates which Product fields are matched. The key is the Product.name, the value is a field mask of the matched Product fields. If matched attributes cannot be determined, this map will be empty. For example, a key \"sku1\" with field mask \"products.color_info\" indicates there is a match between \"sku1\" ColorInfo and the query.", "type": "object"}, "modelScores": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaDoubleList"}, "description": "Google provided available scores.", "type": "object"}, "personalLabels": {"description": "Specifies previous events related to this product for this user based on UserEvent with same SearchRequest.visitor_id or UserInfo.user_id. This is set only when SearchRequest.PersonalizationSpec.mode is SearchRequest.PersonalizationSpec.Mode.AUTO. Possible values: * `purchased`: Indicates that this product has been purchased before.", "items": {"type": "string"}, "type": "array"}, "product": {"$ref": "GoogleCloudRetailV2alphaProduct", "description": "The product data snippet in the search response. Only Product.name is guaranteed to be populated. Product.variants contains the product variants that match the search query. If there are multiple product variants matching the query, top 5 most relevant product variants are returned and ordered by relevancy. If relevancy can be deternmined, use matching_variant_fields to look up matched product variants fields. If relevancy cannot be determined, e.g. when searching \"shoe\" all products in a shoe product can be a match, 5 product variants are returned but order is meaningless."}, "variantRollupValues": {"additionalProperties": {"type": "any"}, "description": "The rollup matching variant Product attributes. The key is one of the SearchRequest.variant_rollup_keys. The values are the merged and de-duplicated Product attributes. Notice that the rollup values are respect filter. For example, when filtering by \"colorFamilies:ANY(\\\"red\\\")\" and rollup \"colorFamilies\", only \"red\" is returned. For textual and numerical attributes, the rollup values is a list of string or double values with type google.protobuf.ListValue. For example, if there are two variants with colors \"red\" and \"blue\", the rollup values are { key: \"colorFamilies\" value { list_value { values { string_value: \"red\" } values { string_value: \"blue\" } } } } For FulfillmentInfo, the rollup values is a double value with type google.protobuf.Value. For example, `{key: \"pickupInStore.store1\" value { number_value: 10 }}` means a there are 10 variants in this product are available in the store \"store1\".", "type": "object"}}, "type": "object"}, "GoogleCloudRetailV2alphaSearchResponseTileNavigationResult": {"description": "This field specifies all related information for tile navigation that will be used in client side.", "id": "GoogleCloudRetailV2alphaSearchResponseTileNavigationResult", "properties": {"tiles": {"description": "The current tiles that are used for tile navigation, sorted by engagement.", "items": {"$ref": "GoogleCloudRetailV2alphaTile"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaServingConfig": {"description": "Configures metadata that is used to generate serving time results (e.g. search results or recommendation predictions).", "id": "GoogleCloudRetailV2alphaServingConfig", "properties": {"boostControlIds": {"description": "Condition boost specifications. If a product matches multiple conditions in the specifications, boost scores from these specifications are all applied and combined in a non-linear way. Maximum number of specifications is 100. Notice that if both ServingConfig.boost_control_ids and SearchRequest.boost_spec are set, the boost conditions from both places are evaluated. If a search request matches multiple boost conditions, the final boost score is equal to the sum of the boost scores from all matched boost conditions. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "displayName": {"description": "Required. The human readable serving config display name. Used in Retail UI. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "diversityLevel": {"description": "How much diversity to use in recommendation model results e.g. `medium-diversity` or `high-diversity`. Currently supported values: * `no-diversity` * `low-diversity` * `medium-diversity` * `high-diversity` * `auto-diversity` If not specified, we choose default based on recommendation model type. Default value: `no-diversity`. Can only be set if solution_types is SOLUTION_TYPE_RECOMMENDATION.", "type": "string"}, "diversityType": {"description": "What kind of diversity to use - data driven or rule based. If unset, the server behavior defaults to RULE_BASED_DIVERSITY.", "enum": ["DIVERSITY_TYPE_UNSPECIFIED", "RULE_BASED_DIVERSITY", "DATA_DRIVEN_DIVERSITY"], "enumDescriptions": ["Default value.", "Rule based diversity.", "Data driven diversity."], "type": "string"}, "doNotAssociateControlIds": {"description": "Condition do not associate specifications. If multiple do not associate conditions match, all matching do not associate controls in the list will execute. - Order does not matter. - Maximum number of specifications is 100. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "dynamicFacetSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestDynamicFacetSpec", "description": "The specification for dynamically generated facets. Notice that only textual facets can be dynamically generated. Can only be set if solution_types is SOLUTION_TYPE_SEARCH."}, "enableCategoryFilterLevel": {"description": "Whether to add additional category filters on the `similar-items` model. If not specified, we enable it by default. Allowed values are: * `no-category-match`: No additional filtering of original results from the model and the customer's filters. * `relaxed-category-match`: Only keep results with categories that match at least one item categories in the PredictRequests's context item. * If customer also sends filters in the PredictRequest, then the results will satisfy both conditions (user given and category match). Can only be set if solution_types is SOLUTION_TYPE_RECOMMENDATION.", "type": "string"}, "facetControlIds": {"description": "Facet specifications for faceted search. If empty, no facets are returned. The ids refer to the ids of Control resources with only the Facet control set. These controls are assumed to be in the same Catalog as the ServingConfig. A maximum of 100 values are allowed. Otherwise, an INVALID_ARGUMENT error is returned. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "filterControlIds": {"description": "Condition filter specifications. If a product matches multiple conditions in the specifications, filters from these specifications are all applied and combined via the AND operator. Maximum number of specifications is 100. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "ignoreControlIds": {"description": "Condition ignore specifications. If multiple ignore conditions match, all matching ignore controls in the list will execute. - Order does not matter. - Maximum number of specifications is 100. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "ignoreRecsDenylist": {"description": "When the flag is enabled, the products in the denylist will not be filtered out in the recommendation filtering results.", "type": "boolean"}, "modelId": {"description": "The id of the model in the same Catalog to use at serving time. Currently only RecommendationModels are supported: https://cloud.google.com/retail/recommendations-ai/docs/create-models Can be changed but only to a compatible model (e.g. others-you-may-like CTR to others-you-may-like CVR). Required when solution_types is SOLUTION_TYPE_RECOMMENDATION.", "type": "string"}, "name": {"description": "Immutable. Fully qualified name `projects/*/locations/global/catalogs/*/servingConfig/*`", "type": "string"}, "onewaySynonymsControlIds": {"description": "Condition oneway synonyms specifications. If multiple oneway synonyms conditions match, all matching oneway synonyms controls in the list will execute. Order of controls in the list will not matter. Maximum number of specifications is 100. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "personalizationSpec": {"$ref": "GoogleCloudRetailV2alphaSearchRequestPersonalizationSpec", "description": "The specification for personalization spec. Can only be set if solution_types is SOLUTION_TYPE_SEARCH. Notice that if both ServingConfig.personalization_spec and SearchRequest.personalization_spec are set. SearchRequest.personalization_spec will override ServingConfig.personalization_spec."}, "priceRerankingLevel": {"description": "How much price ranking we want in serving results. Price reranking causes product items with a similar recommendation probability to be ordered by price, with the highest-priced items first. This setting could result in a decrease in click-through and conversion rates. Allowed values are: * `no-price-reranking` * `low-price-reranking` * `medium-price-reranking` * `high-price-reranking` If not specified, we choose default based on model type. Default value: `no-price-reranking`. Can only be set if solution_types is SOLUTION_TYPE_RECOMMENDATION.", "type": "string"}, "redirectControlIds": {"description": "Condition redirect specifications. Only the first triggered redirect action is applied, even if multiple apply. Maximum number of specifications is 1000. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "replacementControlIds": {"description": "Condition replacement specifications. - Applied according to the order in the list. - A previously replaced term can not be re-replaced. - Maximum number of specifications is 100. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}, "solutionTypes": {"description": "Required. Immutable. Specifies the solution types that a serving config can be associated with. Currently we support setting only one type of solution.", "items": {"enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Retail Search."], "type": "string"}, "type": "array"}, "twowaySynonymsControlIds": {"description": "Condition synonyms specifications. If multiple syonyms conditions match, all matching synonyms control in the list will execute. Order of controls in the list will not matter. Maximum number of specifications is 100. Can only be set if solution_types is SOLUTION_TYPE_SEARCH.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaSetDefaultBranchRequest": {"description": "Request message to set a specified branch as new default_branch.", "id": "GoogleCloudRetailV2alphaSetDefaultBranchRequest", "properties": {"branchId": {"description": "The final component of the resource name of a branch. This field must be one of \"0\", \"1\" or \"2\". Otherwise, an INVALID_ARGUMENT error is returned. If there are no sufficient active products in the targeted branch and force is not set, a FAILED_PRECONDITION error is returned.", "type": "string"}, "force": {"description": "If set to true, it permits switching to a branch with branch_id even if it has no sufficient active products.", "type": "boolean"}, "note": {"description": "Some note on this request, this can be retrieved by CatalogService.GetDefaultBranch before next valid default branch set occurs. This field must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSetInventoryMetadata": {"description": "Metadata related to the progress of the SetInventory operation. Currently empty because there is no meaningful metadata populated from the ProductService.SetInventory method.", "id": "GoogleCloudRetailV2alphaSetInventoryMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaSetInventoryRequest": {"description": "Request message for ProductService.SetInventory method.", "id": "GoogleCloudRetailV2alphaSetInventoryRequest", "properties": {"allowMissing": {"description": "If set to true, and the Product with name Product.name is not found, the inventory update will still be processed and retained for at most 1 day until the Product is created. If set to false, a NOT_FOUND error is returned if the Product is not found.", "type": "boolean"}, "inventory": {"$ref": "GoogleCloudRetailV2alphaProduct", "description": "Required. The inventory information to update. The allowable fields to update are: * Product.price_info * Product.availability * Product.available_quantity * Product.fulfillment_info The updated inventory fields must be specified in SetInventoryRequest.set_mask. If SetInventoryRequest.inventory.name is empty or invalid, an INVALID_ARGUMENT error is returned. If the caller does not have permission to update the Product named in Product.name, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the Product to update does not have existing inventory information, the provided inventory information will be inserted. If the Product to update has existing inventory information, the provided inventory information will be merged while respecting the last update time for each inventory field, using the provided or default value for SetInventoryRequest.set_time. The caller can replace place IDs for a subset of fulfillment types in the following ways: * Adds \"fulfillment_info\" in SetInventoryRequest.set_mask * Specifies only the desired fulfillment types and corresponding place IDs to update in SetInventoryRequest.inventory.fulfillment_info The caller can clear all place IDs from a subset of fulfillment types in the following ways: * Adds \"fulfillment_info\" in SetInventoryRequest.set_mask * Specifies only the desired fulfillment types to clear in SetInventoryRequest.inventory.fulfillment_info * Checks that only the desired fulfillment info types have empty SetInventoryRequest.inventory.fulfillment_info.place_ids The last update time is recorded for the following inventory fields: * Product.price_info * Product.availability * Product.available_quantity * Product.fulfillment_info If a full overwrite of inventory information while ignoring timestamps is needed, ProductService.UpdateProduct should be invoked instead."}, "setMask": {"description": "Indicates which inventory fields in the provided Product to update. At least one field must be provided. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned and the entire update will be ignored.", "format": "google-fieldmask", "type": "string"}, "setTime": {"description": "The time when the request is issued, used to prevent out-of-order updates on inventory fields with the last update time recorded. If not provided, the internal system time will be used.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaSetInventoryResponse": {"description": "Response of the SetInventoryRequest. Currently empty because there is no meaningful response populated from the ProductService.SetInventory method.", "id": "GoogleCloudRetailV2alphaSetInventoryResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaStringList": {"description": "A list of string values.", "id": "GoogleCloudRetailV2alphaStringList", "properties": {"values": {"description": "String values.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaTile": {"description": "This field specifies the tile information including an attribute key, attribute value. More fields will be added in the future, eg: product id or product counts, etc.", "id": "GoogleCloudRetailV2alphaTile", "properties": {"productAttributeInterval": {"$ref": "GoogleCloudRetailV2alphaProductAttributeInterval", "description": "The product attribute key-numeric interval."}, "productAttributeValue": {"$ref": "GoogleCloudRetailV2alphaProductAttributeValue", "description": "The product attribute key-value."}, "representativeProductId": {"description": "The representative product id for this tile.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaTransformedUserEventsMetadata": {"description": "Metadata related to transform user events operation.", "id": "GoogleCloudRetailV2alphaTransformedUserEventsMetadata", "properties": {"sourceEventsCount": {"description": "Count of entries in the source user events BigQuery table.", "format": "int64", "type": "string"}, "transformedEventsCount": {"description": "Count of entries in the transformed user events BigQuery table, which could be different from the actually imported number of user events.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaTuneModelMetadata": {"description": "Metadata associated with a tune operation.", "id": "GoogleCloudRetailV2alphaTuneModelMetadata", "properties": {"model": {"description": "The resource name of the model that this tune applies to. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaTuneModelRequest": {"description": "Request to manually start a tuning process now (instead of waiting for the periodically scheduled tuning to happen).", "id": "GoogleCloudRetailV2alphaTuneModelRequest", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaTuneModelResponse": {"description": "Response associated with a tune operation.", "id": "GoogleCloudRetailV2alphaTuneModelResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2alphaUpdateGenerativeQuestionConfigRequest": {"description": "Request for UpdateGenerativeQuestionConfig method.", "id": "GoogleCloudRetailV2alphaUpdateGenerativeQuestionConfigRequest", "properties": {"generativeQuestionConfig": {"$ref": "GoogleCloudRetailV2alphaGenerativeQuestionConfig", "description": "Required. The question to update."}, "updateMask": {"description": "Optional. Indicates which fields in the provided GenerativeQuestionConfig to update. The following are NOT supported: * GenerativeQuestionConfig.frequency If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaUserEvent": {"description": "UserEvent captures all metadata information Retail API needs to know about how end users interact with customers' website.", "id": "GoogleCloudRetailV2alphaUserEvent", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudRetailV2alphaCustomAttribute"}, "description": "Extra user event features to include in the recommendation model. If you provide custom attributes for ingested user events, also include them in the user events that you associate with prediction requests. Custom attribute formatting must be consistent between imported events and events provided with prediction requests. This lets the Retail API use those custom attributes when training models and serving predictions, which helps improve recommendation quality. This field needs to pass all below criteria, otherwise an INVALID_ARGUMENT error is returned: * The key must be a UTF-8 encoded string with a length limit of 5,000 characters. * For text attributes, at most 400 values are allowed. Empty values are not allowed. Each value must be a UTF-8 encoded string with a length limit of 256 characters. * For number attributes, at most 400 values are allowed. For product recommendations, an example of extra user information is traffic_channel, which is how a user arrives at the site. Users can arrive at the site by coming to the site directly, coming through Google search, or in other ways.", "type": "object"}, "attributionToken": {"description": "Highly recommended for user events that are the result of PredictionService.Predict. This field enables accurate attribution of recommendation model performance. The value must be a valid PredictResponse.attribution_token for user events that are the result of PredictionService.Predict. The value must be a valid SearchResponse.attribution_token for user events that are the result of SearchService.Search. This token enables us to accurately attribute page view or purchase back to the event and the particular predict response containing this clicked/purchased product. If user clicks on product K in the recommendation results, pass PredictResponse.attribution_token as a URL parameter to product <PERSON>'s page. When recording events on product <PERSON>'s page, log the PredictResponse.attribution_token to this field.", "type": "string"}, "cartId": {"description": "The ID or name of the associated shopping cart. This ID is used to associate multiple items added or present in the cart before purchase. This can only be set for `add-to-cart`, `purchase-complete`, or `shopping-cart-page-view` events.", "type": "string"}, "completionDetail": {"$ref": "GoogleCloudRetailV2alphaCompletionDetail", "description": "The main auto-completion details related to the event. This field should be set for `search` event when autocomplete function is enabled and the user clicks a suggestion for search."}, "entity": {"description": "The entity for customers that may run multiple different entities, domains, sites or regions, for example, `Google US`, `Google Ads`, `Waymo`, `google.com`, `youtube.com`, etc. We recommend that you set this field to get better per-entity search, completion, and prediction results.", "type": "string"}, "eventTime": {"description": "Only required for UserEventService.ImportUserEvents method. Timestamp of when the user event happened.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Required. User event type. Allowed values are: * `add-to-cart`: Products being added to cart. * `remove-from-cart`: Products being removed from cart. * `category-page-view`: Special pages such as sale or promotion pages viewed. * `detail-page-view`: Products detail page viewed. * `home-page-view`: Homepage viewed. * `purchase-complete`: User finishing a purchase. * `search`: Product search. * `shopping-cart-page-view`: User viewing a shopping cart.", "type": "string"}, "experimentIds": {"description": "A list of identifiers for the independent experiment groups this user event belongs to. This is used to distinguish between user events associated with different experiment setups (e.g. using Retail API, using different recommendation models).", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "The filter syntax consists of an expression language for constructing a predicate from one or more fields of the products being filtered. See SearchRequest.filter for definition and syntax. The value must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "offset": {"description": "An integer that specifies the current offset for pagination (the 0-indexed starting location, amongst the products deemed by the API as relevant). See SearchRequest.offset for definition. If this field is negative, an INVALID_ARGUMENT is returned. This can only be set for `search` events. Other event types should not set this field. Otherwise, an INVALID_ARGUMENT error is returned.", "format": "int32", "type": "integer"}, "orderBy": {"description": "The order in which products are returned. See SearchRequest.order_by for definition and syntax. The value must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. This can only be set for `search` events. Other event types should not set this field. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "pageCategories": {"description": "The categories associated with a category page. To represent full path of category, use '>' sign to separate different hierarchies. If '>' is part of the category name, replace it with other character(s). Category pages include special pages such as sales or promotions. For instance, a special sale page may have the category hierarchy: \"pageCategories\" : [\"Sales > 2017 Black Friday Deals\"]. Required for `category-page-view` events. At least one of search_query or page_categories is required for `search` events. Other event types should not set this field. Otherwise, an INVALID_ARGUMENT error is returned.", "items": {"type": "string"}, "type": "array"}, "pageViewId": {"description": "A unique ID of a web page view. This should be kept the same for all user events triggered from the same pageview. For example, an item detail page view could trigger multiple events as the user is browsing the page. The `pageViewId` property should be kept the same for all these events so that they can be grouped together properly. When using the client side event reporting with JavaScript pixel and Google Tag Manager, this value is filled in automatically.", "type": "string"}, "productDetails": {"description": "The main product details related to the event. This field is optional except for the following event types: * `add-to-cart` * `detail-page-view` * `purchase-complete` In a `search` event, this field represents the products returned to the end user on the current page (the end user may have not finished browsing the whole page yet). When a new page is returned to the end user, after pagination/filtering/ordering even for the same query, a new `search` event with different product_details is desired. The end user may have not finished browsing the whole page yet.", "items": {"$ref": "GoogleCloudRetailV2alphaProductDetail"}, "type": "array"}, "purchaseTransaction": {"$ref": "GoogleCloudRetailV2alphaPurchaseTransaction", "description": "A transaction represents the entire purchase transaction. Required for `purchase-complete` events. Other event types should not set this field. Otherwise, an INVALID_ARGUMENT error is returned."}, "referrerUri": {"description": "The referrer URL of the current page. When using the client side event reporting with JavaScript pixel and Google Tag Manager, this value is filled in automatically.", "type": "string"}, "searchQuery": {"description": "The user's search query. See SearchRequest.query for definition. The value must be a UTF-8 encoded string with a length limit of 5,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. At least one of search_query or page_categories is required for `search` events. Other event types should not set this field. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "sessionId": {"description": "A unique identifier for tracking a visitor session with a length limit of 128 bytes. A session is an aggregation of an end user behavior in a time span. A general guideline to populate the session_id: 1. If user has no activity for 30 min, a new session_id should be assigned. 2. The session_id should be unique across users, suggest use uuid or add visitor_id as prefix.", "type": "string"}, "uri": {"description": "Complete URL (window.location.href) of the user's current page. When using the client side event reporting with JavaScript pixel and Google Tag Manager, this value is filled in automatically. Maximum length 5,000 characters.", "type": "string"}, "userInfo": {"$ref": "GoogleCloudRetailV2alphaUserInfo", "description": "User information."}, "visitorId": {"description": "Required. A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor log in/out of the website. Don't set the field to the same fixed ID for different users. This mixes the event history of those users together, which results in degraded model quality. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned. The field should not contain PII or user-data. We recommend to use Google Analytics [Client ID](https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#clientId) for this field.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaUserEventImportSummary": {"description": "A summary of import result. The UserEventImportSummary summarizes the import status for user events.", "id": "GoogleCloudRetailV2alphaUserEventImportSummary", "properties": {"joinedEventsCount": {"description": "Count of user events imported with complete existing catalog information.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with catalog information not found in the imported catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2alphaUserEventInlineSource": {"description": "The inline source for the input config for ImportUserEvents method.", "id": "GoogleCloudRetailV2alphaUserEventInlineSource", "properties": {"userEvents": {"description": "Required. A list of user events to import. Recommended max of 10k items.", "items": {"$ref": "GoogleCloudRetailV2alphaUserEvent"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2alphaUserEventInputConfig": {"description": "The input config source for user events.", "id": "GoogleCloudRetailV2alphaUserEventInputConfig", "properties": {"bigQuerySource": {"$ref": "GoogleCloudRetailV2alphaBigQuerySource", "description": "Required. BigQuery input source."}, "gcsSource": {"$ref": "GoogleCloudRetailV2alphaGcsSource", "description": "Required. Google Cloud Storage location for the input content."}, "userEventInlineSource": {"$ref": "GoogleCloudRetailV2alphaUserEventInlineSource", "description": "Required. The Inline source for the input content for UserEvents."}}, "type": "object"}, "GoogleCloudRetailV2alphaUserInfo": {"description": "Information of an end user.", "id": "GoogleCloudRetailV2alphaUserInfo", "properties": {"directUserRequest": {"description": "True if the request is made directly from the end user, in which case the ip_address and user_agent can be populated from the HTTP request. This flag should be set only if the API request is made directly from the end user such as a mobile app (and not if a gateway or a server is processing and pushing the user events). This should not be set when using the JavaScript tag in UserEventService.CollectUserEvent.", "type": "boolean"}, "ipAddress": {"description": "The end user's IP address. This field is used to extract location information for personalization. This field must be either an IPv4 address (e.g. \"************\") or an IPv6 address (e.g. \"2001:0db8:85a3:0000:0000:8a2e:0370:7334\"). Otherwise, an INVALID_ARGUMENT error is returned. This should not be set when: * setting SearchRequest.user_info. * using the JavaScript tag in UserEventService.CollectUserEvent or if direct_user_request is set.", "type": "string"}, "userAgent": {"description": "User agent as included in the HTTP header. The field must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an INVALID_ARGUMENT error is returned. This should not be set when using the client side event reporting with GTM or JavaScript tag in UserEventService.CollectUserEvent or if direct_user_request is set.", "type": "string"}, "userId": {"description": "Highly recommended for logged-in users. Unique identifier for logged-in user, such as a user name. Don't set for anonymous users. Always use a hashed value for this ID. Don't set the field to the same fixed ID for different users. This mixes the event history of those users together, which results in degraded model quality. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaAddFulfillmentPlacesMetadata": {"description": "Metadata related to the progress of the AddFulfillmentPlaces operation. Currently empty because there is no meaningful metadata populated from the ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2betaAddFulfillmentPlacesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaAddFulfillmentPlacesResponse": {"description": "Response of the AddFulfillmentPlacesRequest. Currently empty because there is no meaningful response populated from the ProductService.AddFulfillmentPlaces method.", "id": "GoogleCloudRetailV2betaAddFulfillmentPlacesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaAddLocalInventoriesMetadata": {"description": "Metadata related to the progress of the AddLocalInventories operation. Currently empty because there is no meaningful metadata populated from the ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2betaAddLocalInventoriesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaAddLocalInventoriesResponse": {"description": "Response of the ProductService.AddLocalInventories API. Currently empty because there is no meaningful response populated from the ProductService.AddLocalInventories method.", "id": "GoogleCloudRetailV2betaAddLocalInventoriesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaBigQueryOutputResult": {"description": "A BigQuery output result.", "id": "GoogleCloudRetailV2betaBigQueryOutputResult", "properties": {"datasetId": {"description": "The ID of a BigQuery Dataset.", "type": "string"}, "tableId": {"description": "The ID of a BigQuery Table.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaCreateModelMetadata": {"description": "Metadata associated with a create operation.", "id": "GoogleCloudRetailV2betaCreateModelMetadata", "properties": {"model": {"description": "The resource name of the model that this create applies to. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaExportAnalyticsMetricsResponse": {"description": "Response of the ExportAnalyticsMetricsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2betaExportAnalyticsMetricsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2betaExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2betaOutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2betaExportErrorsConfig": {"description": "Configuration of destination for Export related errors.", "id": "GoogleCloudRetailV2betaExportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage path for import errors. This must be an empty, existing Cloud Storage bucket. Export errors will be written to a file in this bucket, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaExportMetadata": {"description": "Metadata related to the progress of the Export operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2betaExportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaExportProductsResponse": {"description": "Response of the ExportProductsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2betaExportProductsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2betaExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2betaOutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2betaExportUserEventsResponse": {"description": "Response of the ExportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2betaExportUserEventsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2betaExportErrorsConfig", "description": "This field is never set."}, "outputResult": {"$ref": "GoogleCloudRetailV2betaOutputResult", "description": "Output result indicating where the data were exported to."}}, "type": "object"}, "GoogleCloudRetailV2betaGcsOutputResult": {"description": "A Gcs output result.", "id": "GoogleCloudRetailV2betaGcsOutputResult", "properties": {"outputUri": {"description": "The uri of Gcs output", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaImportCompletionDataResponse": {"description": "Response of the ImportCompletionDataRequest. If the long running operation is done, this message is returned by the google.longrunning.Operations.response field if the operation is successful.", "id": "GoogleCloudRetailV2betaImportCompletionDataResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2betaImportErrorsConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudRetailV2betaImportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage prefix for import errors. This must be an empty, existing Cloud Storage directory. Import errors are written to sharded files in this directory, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaImportMetadata": {"description": "Metadata related to the progress of the Import operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2betaImportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "notificationPubsubTopic": {"description": "Pub/Sub topic for receiving notification. If this field is set, when the import is finished, a notification is sent to specified Pub/Sub topic. The message data is JSON string of a Operation. Format of the Pub/Sub topic is `projects/{project}/topics/{topic}`.", "type": "string"}, "requestId": {"deprecated": true, "description": "Deprecated. This field is never set.", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaImportProductsResponse": {"description": "Response of the ImportProductsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2betaImportProductsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2betaImportErrorsConfig", "description": "Echoes the destination for the complete errors in the request if set."}}, "type": "object"}, "GoogleCloudRetailV2betaImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRetailV2betaImportUserEventsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRetailV2betaImportErrorsConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "importSummary": {"$ref": "GoogleCloudRetailV2betaUserEventImportSummary", "description": "Aggregated statistics of user event import status."}}, "type": "object"}, "GoogleCloudRetailV2betaModel": {"description": "Metadata that describes the training and serving parameters of a Model. A Model can be associated with a ServingConfig and then queried through the Predict API.", "id": "GoogleCloudRetailV2betaModel", "properties": {"createTime": {"description": "Output only. Timestamp the Recommendation Model was created at.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataState": {"description": "Output only. The state of data requirements for this model: `DATA_OK` and `DATA_ERROR`. Recommendation model cannot be trained if the data is in `DATA_ERROR` state. Recommendation model can have `DATA_ERROR` state even if serving state is `ACTIVE`: models were trained successfully before, but cannot be refreshed because model no longer has sufficient data for training.", "enum": ["DATA_STATE_UNSPECIFIED", "DATA_OK", "DATA_ERROR"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The model has sufficient training data.", "The model does not have sufficient training data. Error messages can be queried via Stackdriver."], "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The display name of the model. Should be human readable, used to display Recommendation Models in the Retail Cloud Console Dashboard. UTF-8 encoded string with limit of 1024 characters.", "type": "string"}, "filteringOption": {"description": "Optional. If `RECOMMENDATIONS_FILTERING_ENABLED`, recommendation filtering by attributes is enabled for the model.", "enum": ["RECOMMENDATIONS_FILTERING_OPTION_UNSPECIFIED", "RECOMMENDATIONS_FILTERING_DISABLED", "RECOMMENDATIONS_FILTERING_ENABLED"], "enumDescriptions": ["Value used when unset. In this case, server behavior defaults to RECOMMENDATIONS_FILTERING_DISABLED.", "Recommendation filtering is disabled.", "Recommendation filtering is enabled."], "type": "string"}, "lastTuneTime": {"description": "Output only. The timestamp when the latest successful tune finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "modelFeaturesConfig": {"$ref": "GoogleCloudRetailV2betaModelModelFeaturesConfig", "description": "Optional. Additional model features config."}, "name": {"description": "Required. The fully qualified resource name of the model. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}` catalog_id has char limit of 50. recommendation_model_id has char limit of 40.", "type": "string"}, "optimizationObjective": {"description": "Optional. The optimization objective e.g. `cvr`. Currently supported values: `ctr`, `cvr`, `revenue-per-order`. If not specified, we choose default based on model type. Default depends on type of recommendation: `recommended-for-you` => `ctr` `others-you-may-like` => `ctr` `frequently-bought-together` => `revenue_per_order` This field together with optimization_objective describe model metadata to use to control model training and serving. See https://cloud.google.com/retail/docs/models for more details on what the model metadata control and which combination of parameters are valid. For invalid combinations of parameters (e.g. type = `frequently-bought-together` and optimization_objective = `ctr`), you receive an error 400 if you try to create/update a recommendation with this set of knobs.", "type": "string"}, "periodicTuningState": {"description": "Optional. The state of periodic tuning. The period we use is 3 months - to do a one-off tune earlier use the `TuneModel` method. Default value is `PERIODIC_TUNING_ENABLED`.", "enum": ["PERIODIC_TUNING_STATE_UNSPECIFIED", "PERIODIC_TUNING_DISABLED", "ALL_TUNING_DISABLED", "PERIODIC_TUNING_ENABLED"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The model has periodic tuning disabled. Tuning can be reenabled by calling the `EnableModelPeriodicTuning` method or by calling the `TuneModel` method.", "The model cannot be tuned with periodic tuning OR the `TuneModel` method. Hide the options in customer UI and reject any requests through the backend self serve API.", "The model has periodic tuning enabled. Tuning can be disabled by calling the `DisableModelPeriodicTuning` method."], "type": "string"}, "servingConfigLists": {"description": "Output only. The list of valid serving configs associated with the PageOptimizationConfig.", "items": {"$ref": "GoogleCloudRetailV2betaModelServingConfigList"}, "readOnly": true, "type": "array"}, "servingState": {"description": "Output only. The serving state of the model: `ACTIVE`, `NOT_ACTIVE`.", "enum": ["SERVING_STATE_UNSPECIFIED", "INACTIVE", "ACTIVE", "TUNED"], "enumDescriptions": ["Unspecified serving state.", "The model is not serving.", "The model is serving and can be queried.", "The model is trained on tuned hyperparameters and can be queried."], "readOnly": true, "type": "string"}, "trainingState": {"description": "Optional. The training state that the model is in (e.g. `TRAINING` or `PAUSED`). Since part of the cost of running the service is frequency of training - this can be used to determine when to train model in order to control cost. If not specified: the default value for `CreateModel` method is `TRAINING`. The default value for `UpdateModel` method is to keep the state the same as before.", "enum": ["TRAINING_STATE_UNSPECIFIED", "PAUSED", "TRAINING"], "enumDescriptions": ["Unspecified training state.", "The model training is paused.", "The model is training."], "type": "string"}, "tuningOperation": {"description": "Output only. The tune operation associated with the model. Can be used to determine if there is an ongoing tune for this recommendation. Empty field implies no tune is goig on.", "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of model e.g. `home-page`. Currently supported values: `recommended-for-you`, `others-you-may-like`, `frequently-bought-together`, `page-optimization`, `similar-items`, `buy-it-again`, `on-sale-items`, and `recently-viewed`(readonly value). This field together with optimization_objective describe model metadata to use to control model training and serving. See https://cloud.google.com/retail/docs/models for more details on what the model metadata control and which combination of parameters are valid. For invalid combinations of parameters (e.g. type = `frequently-bought-together` and optimization_objective = `ctr`), you receive an error 400 if you try to create/update a recommendation with this set of knobs.", "type": "string"}, "updateTime": {"description": "Output only. Timestamp the Recommendation Model was last updated. E.g. if a Recommendation Model was paused - this would be the time the pause was initiated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaModelFrequentlyBoughtTogetherFeaturesConfig": {"description": "Additional configs for the frequently-bought-together model type.", "id": "GoogleCloudRetailV2betaModelFrequentlyBoughtTogetherFeaturesConfig", "properties": {"contextProductsType": {"description": "Optional. Specifies the context of the model when it is used in predict requests. Can only be set for the `frequently-bought-together` type. If it isn't specified, it defaults to MULTIPLE_CONTEXT_PRODUCTS.", "enum": ["CONTEXT_PRODUCTS_TYPE_UNSPECIFIED", "SINGLE_CONTEXT_PRODUCT", "MULTIPLE_CONTEXT_PRODUCTS"], "enumDescriptions": ["Unspecified default value, should never be explicitly set. Defaults to MULTIPLE_CONTEXT_PRODUCTS.", "Use only a single product as context for the recommendation. Typically used on pages like add-to-cart or product details.", "Use one or multiple products as context for the recommendation. Typically used on shopping cart pages."], "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaModelModelFeaturesConfig": {"description": "Additional model features config.", "id": "GoogleCloudRetailV2betaModelModelFeaturesConfig", "properties": {"frequentlyBoughtTogetherConfig": {"$ref": "GoogleCloudRetailV2betaModelFrequentlyBoughtTogetherFeaturesConfig", "description": "Additional configs for frequently-bought-together models."}}, "type": "object"}, "GoogleCloudRetailV2betaModelServingConfigList": {"description": "Represents an ordered combination of valid serving configs, which can be used for `PAGE_OPTIMIZATION` recommendations.", "id": "GoogleCloudRetailV2betaModelServingConfigList", "properties": {"servingConfigIds": {"description": "Optional. A set of valid serving configs that may be used for `PAGE_OPTIMIZATION`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2betaOutputResult": {"description": "Output result that stores the information about where the exported data is stored.", "id": "GoogleCloudRetailV2betaOutputResult", "properties": {"bigqueryResult": {"description": "The BigQuery location where the result is stored.", "items": {"$ref": "GoogleCloudRetailV2betaBigQueryOutputResult"}, "type": "array"}, "gcsResult": {"description": "The Google Cloud Storage location where the result is stored.", "items": {"$ref": "GoogleCloudRetailV2betaGcsOutputResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2betaPurgeMetadata": {"description": "Metadata related to the progress of the Purge operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2betaPurgeMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaPurgeProductsMetadata": {"description": "Metadata related to the progress of the PurgeProducts operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRetailV2betaPurgeProductsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaPurgeProductsResponse": {"description": "Response of the PurgeProductsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRetailV2betaPurgeProductsResponse", "properties": {"purgeCount": {"description": "The total count of products purged as a result of the operation.", "format": "int64", "type": "string"}, "purgeSample": {"description": "A sample of the product names that will be deleted. Only populated if `force` is set to false. A max of 100 names will be returned and the names are chosen at random.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRetailV2betaPurgeUserEventsResponse": {"description": "Response of the PurgeUserEventsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRetailV2betaPurgeUserEventsResponse", "properties": {"purgedEventsCount": {"description": "The total count of events purged as a result of the operation.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaRejoinUserEventsMetadata": {"description": "Metadata for `RejoinUserEvents` method.", "id": "GoogleCloudRetailV2betaRejoinUserEventsMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaRejoinUserEventsResponse": {"description": "Response message for `RejoinUserEvents` method.", "id": "GoogleCloudRetailV2betaRejoinUserEventsResponse", "properties": {"rejoinedUserEventsCount": {"description": "Number of user events that were joined with latest product catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaRemoveFulfillmentPlacesMetadata": {"description": "Metadata related to the progress of the RemoveFulfillmentPlaces operation. Currently empty because there is no meaningful metadata populated from the ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2betaRemoveFulfillmentPlacesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaRemoveFulfillmentPlacesResponse": {"description": "Response of the RemoveFulfillmentPlacesRequest. Currently empty because there is no meaningful response populated from the ProductService.RemoveFulfillmentPlaces method.", "id": "GoogleCloudRetailV2betaRemoveFulfillmentPlacesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaRemoveLocalInventoriesMetadata": {"description": "Metadata related to the progress of the RemoveLocalInventories operation. Currently empty because there is no meaningful metadata populated from the ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2betaRemoveLocalInventoriesMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaRemoveLocalInventoriesResponse": {"description": "Response of the ProductService.RemoveLocalInventories API. Currently empty because there is no meaningful response populated from the ProductService.RemoveLocalInventories method.", "id": "GoogleCloudRetailV2betaRemoveLocalInventoriesResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaSetInventoryMetadata": {"description": "Metadata related to the progress of the SetInventory operation. Currently empty because there is no meaningful metadata populated from the ProductService.SetInventory method.", "id": "GoogleCloudRetailV2betaSetInventoryMetadata", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaSetInventoryResponse": {"description": "Response of the SetInventoryRequest. Currently empty because there is no meaningful response populated from the ProductService.SetInventory method.", "id": "GoogleCloudRetailV2betaSetInventoryResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaTuneModelMetadata": {"description": "Metadata associated with a tune operation.", "id": "GoogleCloudRetailV2betaTuneModelMetadata", "properties": {"model": {"description": "The resource name of the model that this tune applies to. Format: `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudRetailV2betaTuneModelResponse": {"description": "Response associated with a tune operation.", "id": "GoogleCloudRetailV2betaTuneModelResponse", "properties": {}, "type": "object"}, "GoogleCloudRetailV2betaUserEventImportSummary": {"description": "A summary of import result. The UserEventImportSummary summarizes the import status for user events.", "id": "GoogleCloudRetailV2betaUserEventImportSummary", "properties": {"joinedEventsCount": {"description": "Count of user events imported with complete existing catalog information.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with catalog information not found in the imported catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Vertex AI Search for commerce API", "version": "v2alpha", "version_module": true}