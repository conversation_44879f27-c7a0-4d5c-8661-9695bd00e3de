"""
小樱的Gmail服务
集成邮件发送功能，并扩展读取、回复等功能
"""

import base64
import json
import os
from email.mime.text import MIMEText
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from config import Config

class SakuraGmailService:
    def __init__(self):
        """初始化小樱的Gmail服务"""
        self.service = None
        self.user_email = None
        self._authenticate()
    
    def _authenticate(self):
        """Gmail API认证"""
        try:
            Config.validate_gmail_credentials()
            creds = None
            
            # 设置代理（如果需要）
            self._setup_proxy()
            
            # 尝试加载已保存的token
            if Config.GMAIL_TOKEN_FILE.exists():
                creds = Credentials.from_authorized_user_file(
                    str(Config.GMAIL_TOKEN_FILE), Config.GMAIL_SCOPES)
            
            # 如果没有有效凭据，进行授权流程
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(Config.GMAIL_CREDENTIALS_FILE), Config.GMAIL_SCOPES)
                    creds = flow.run_local_server(port=0)
                
                # 保存凭据供下次使用
                with open(Config.GMAIL_TOKEN_FILE, 'w') as token:
                    token.write(creds.to_json())
            
            # 构建Gmail服务
            self.service = build('gmail', 'v1', credentials=creds)
            
            # 获取用户邮箱地址
            profile = self.service.users().getProfile(userId='me').execute()
            self.user_email = profile['emailAddress']
            
        except Exception as e:
            raise Exception(f"小樱的邮箱连接失败了呢～: {str(e)}")
    
    def _setup_proxy(self):
        """设置代理"""
        proxy_http = os.environ.get('HTTP_PROXY') or os.environ.get('http_proxy')
        proxy_https = os.environ.get('HTTPS_PROXY') or os.environ.get('https_proxy')
        
        if proxy_http or proxy_https:
            print(f"🌐 小樱检测到代理设置: HTTP={proxy_http}, HTTPS={proxy_https}")
    
    def send_email(self, to, subject, body):
        """发送邮件"""
        try:
            # 创建邮件
            message = MIMEText(body, 'plain', 'utf-8')
            message['to'] = to
            message['subject'] = subject
            message['from'] = self.user_email
            
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
            
            # 发送邮件
            result = self.service.users().messages().send(
                userId='me', body={'raw': raw_message}).execute()
            
            return {
                'success': True,
                'message': f'邮件发送成功！消息ID: {result["id"]}',
                'message_id': result['id']
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'邮件发送失败: {str(e)}',
                'message_id': None
            }
    
    def read_emails(self, count=5, only_unread=True):
        """读取邮件"""
        try:
            # 构建查询
            query = 'in:inbox'
            if only_unread:
                query += ' is:unread'
            
            # 获取邮件列表
            results = self.service.users().messages().list(
                userId='me', q=query, maxResults=count).execute()
            
            messages = results.get('messages', [])
            
            if not messages:
                return {
                    'success': True,
                    'emails': [],
                    'message': '没有新邮件呢～'
                }
            
            # 获取邮件详情
            emails = []
            for msg in messages:
                email_data = self.service.users().messages().get(
                    userId='me', id=msg['id']).execute()
                
                # 解析邮件信息
                headers = email_data['payload'].get('headers', [])
                subject = next((h['value'] for h in headers if h['name'] == 'Subject'), '无主题')
                sender = next((h['value'] for h in headers if h['name'] == 'From'), '未知发件人')
                date = next((h['value'] for h in headers if h['name'] == 'Date'), '未知时间')
                
                # 获取邮件内容
                body = self._extract_email_body(email_data['payload'])
                
                emails.append({
                    'id': msg['id'],
                    'subject': subject,
                    'sender': sender,
                    'date': date,
                    'body': body[:200] + '...' if len(body) > 200 else body,  # 截取前200字符
                    'is_unread': 'UNREAD' in email_data.get('labelIds', [])
                })
            
            return {
                'success': True,
                'emails': emails,
                'message': f'找到了{len(emails)}封邮件！'
            }
            
        except Exception as e:
            return {
                'success': False,
                'emails': [],
                'message': f'读取邮件失败: {str(e)}'
            }
    
    def _extract_email_body(self, payload):
        """提取邮件正文"""
        try:
            if 'parts' in payload:
                for part in payload['parts']:
                    if part['mimeType'] == 'text/plain':
                        data = part['body']['data']
                        return base64.urlsafe_b64decode(data).decode('utf-8')
            elif payload['mimeType'] == 'text/plain':
                data = payload['body']['data']
                return base64.urlsafe_b64decode(data).decode('utf-8')
            return "无法解析邮件内容"
        except:
            return "邮件内容解析失败"
    
    def mark_as_read(self, message_id):
        """标记邮件为已读"""
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()

            return {
                'success': True,
                'message': '邮件已标记为已读！'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'标记失败: {str(e)}'
            }

    def reply_email(self, message_id, reply_content):
        """回复邮件"""
        try:
            # 获取原邮件
            original = self.service.users().messages().get(
                userId='me', id=message_id).execute()

            # 获取原邮件信息
            headers = original['payload'].get('headers', [])
            original_subject = next((h['value'] for h in headers if h['name'] == 'Subject'), '')
            original_from = next((h['value'] for h in headers if h['name'] == 'From'), '')

            # 构建回复主题
            reply_subject = f"Re: {original_subject}" if not original_subject.startswith('Re:') else original_subject

            # 提取发件人邮箱
            import re
            email_match = re.search(r'<(.+?)>', original_from)
            reply_to = email_match.group(1) if email_match else original_from

            # 发送回复
            return self.send_email(reply_to, reply_subject, reply_content)

        except Exception as e:
            return {
                'success': False,
                'message': f'回复邮件失败: {str(e)}',
                'message_id': None
            }
