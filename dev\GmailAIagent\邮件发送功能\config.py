"""
Gmail API 配置文件
绝对精简版本
"""

import os
from pathlib import Path

class Config:
    # Gmail API 配置
    CREDENTIALS_FILE = Path(__file__).parent.parent / "client_secret.json"
    TOKEN_FILE = Path(__file__).parent / "token.json"
    SCOPES = ['https://www.googleapis.com/auth/gmail.send']
    
    @classmethod
    def validate_credentials(cls):
        """验证凭据文件是否存在"""
        if not cls.CREDENTIALS_FILE.exists():
            raise FileNotFoundError(f"凭据文件不存在: {cls.CREDENTIALS_FILE}")
        return True
